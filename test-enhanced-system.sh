#!/bin/bash

# Enhanced Agentic Integration Test Suite
# Siła grzmotu, pełna moc wiatru! ⚡🌩️

API_BASE="http://localhost:5420/api"
LM_STUDIO_BASE="http://*************:1234"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test counters
PASSED=0
FAILED=0
TOTAL=0

log_header() {
    echo ""
    echo "============================================================"
    echo -e "${CYAN}🚀 $1${NC}"
    echo "============================================================"
}

log_test() {
    echo ""
    echo -e "${BLUE}🧪 Testing: $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED++))
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_info() {
    echo -e "${CYAN}  $1${NC}"
}

run_test() {
    ((TOTAL++))
    local test_name="$1"
    local test_command="$2"
    
    log_test "$test_name"
    
    if eval "$test_command"; then
        log_success "$test_name PASSED"
        return 0
    else
        log_error "$test_name FAILED"
        return 1
    fi
}

# Test 1: LM Studio Connection
test_lm_studio() {
    local response=$(curl -s -w "%{http_code}" "$LM_STUDIO_BASE/v1/models" -o /tmp/lm_models.json)
    
    if [ "$response" = "200" ]; then
        local model_count=$(cat /tmp/lm_models.json | grep -o '"id"' | wc -l)
        log_info "LM Studio connected! Found $model_count models"
        
        # Show available models
        if command -v jq &> /dev/null; then
            log_info "Available models:"
            cat /tmp/lm_models.json | jq -r '.data[].id' | while read model; do
                log_info "  📦 $model"
            done
        fi
        return 0
    else
        log_error "LM Studio not responding (HTTP $response)"
        return 1
    fi
}

# Test 2: Backend Health
test_backend_health() {
    local response=$(curl -s -w "%{http_code}" "$API_BASE/monitoring/health" -o /tmp/health.json)
    
    if [ "$response" = "200" ]; then
        log_info "Backend health check successful"
        
        if command -v jq &> /dev/null; then
            local api_status=$(cat /tmp/health.json | jq -r '.checks.api')
            local lm_status=$(cat /tmp/health.json | jq -r '.checks.lmStudio')
            local mcp_status=$(cat /tmp/health.json | jq -r '.checks.mcpTools')
            
            log_info "  API: $api_status"
            log_info "  LM Studio: $lm_status"
            log_info "  MCP Tools: $mcp_status"
        fi
        return 0
    else
        log_error "Backend health check failed (HTTP $response)"
        return 1
    fi
}

# Test 3: System Metrics
test_system_metrics() {
    local response=$(curl -s -w "%{http_code}" "$API_BASE/monitoring/system" -o /tmp/metrics.json)
    
    if [ "$response" = "200" ]; then
        log_info "System metrics retrieved successfully"
        
        if command -v jq &> /dev/null; then
            local agents=$(cat /tmp/metrics.json | jq -r '.data.totalAgents')
            local active=$(cat /tmp/metrics.json | jq -r '.data.activeAgents')
            local tasks=$(cat /tmp/metrics.json | jq -r '.data.completedTasks')
            local response_time=$(cat /tmp/metrics.json | jq -r '.data.averageResponseTime')
            local success_rate=$(cat /tmp/metrics.json | jq -r '.data.successRate')
            local memory=$(cat /tmp/metrics.json | jq -r '.data.memoryUsage')
            local cpu=$(cat /tmp/metrics.json | jq -r '.data.cpuUsage')
            
            log_info "  🤖 Total Agents: $agents"
            log_info "  ⚡ Active Agents: $active"
            log_info "  ✅ Completed Tasks: $tasks"
            log_info "  ⏱️  Avg Response Time: ${response_time}ms"
            log_info "  📊 Success Rate: ${success_rate}%"
            log_info "  💾 Memory Usage: ${memory}%"
            log_info "  🖥️  CPU Usage: ${cpu}%"
        fi
        return 0
    else
        log_error "System metrics failed (HTTP $response)"
        return 1
    fi
}

# Test 4: Autonomous Research
test_autonomous_research() {
    log_info "Starting autonomous research for Vitamin C..."
    
    local research_data='{
        "supplement": "Vitamin C",
        "goals": ["Immune Support", "Antioxidant Protection"],
        "depth": "basic"
    }'
    
    local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/enhanced-research/autonomous" \
        -H "Content-Type: application/json" \
        -d "$research_data" \
        -o /tmp/research.json)
    
    if [ "$response" = "200" ]; then
        if command -v jq &> /dev/null; then
            local success=$(cat /tmp/research.json | jq -r '.success')
            local flow_id=$(cat /tmp/research.json | jq -r '.flowId')
            local duration=$(cat /tmp/research.json | jq -r '.estimatedDuration')
            
            if [ "$success" = "true" ]; then
                log_info "Research started successfully!"
                log_info "  🆔 Flow ID: $flow_id"
                log_info "  ⏱️  Estimated Duration: $duration"
                
                # Wait a bit and check status
                log_info "Waiting 5 seconds to check progress..."
                sleep 5
                
                local status_response=$(curl -s -w "%{http_code}" "$API_BASE/enhanced-research/flows/$flow_id/status" -o /tmp/status.json)
                
                if [ "$status_response" = "200" ]; then
                    local status=$(cat /tmp/status.json | jq -r '.data.status')
                    local progress=$(cat /tmp/status.json | jq -r '.data.progress')
                    local step=$(cat /tmp/status.json | jq -r '.data.currentStep')
                    
                    log_info "  📊 Status: $status"
                    log_info "  📈 Progress: ${progress}%"
                    log_info "  🔄 Current Step: $step"
                fi
                
                return 0
            else
                log_error "Research request failed"
                return 1
            fi
        else
            log_info "Research started (jq not available for detailed parsing)"
            return 0
        fi
    else
        log_error "Autonomous research failed (HTTP $response)"
        return 1
    fi
}

# Test 5: Enhanced Search
test_enhanced_search() {
    log_info "Testing enhanced supplement search..."
    
    local search_data='{
        "query": "vitamin d benefits immune system",
        "includeTavily": true,
        "includeBraveSearch": true,
        "includeScientificDatabases": true
    }'
    
    local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/enhanced-research/supplements/search" \
        -H "Content-Type: application/json" \
        -d "$search_data" \
        -o /tmp/search.json)
    
    if [ "$response" = "200" ]; then
        if command -v jq &> /dev/null; then
            local success=$(cat /tmp/search.json | jq -r '.success')
            
            if [ "$success" = "true" ]; then
                local original_query=$(cat /tmp/search.json | jq -r '.query.original')
                local enhanced_query=$(cat /tmp/search.json | jq -r '.query.enhanced' | cut -c1-100)
                local results_count=$(cat /tmp/search.json | jq -r '.data.results | length')
                
                log_info "Enhanced search completed!"
                log_info "  🔍 Original Query: $original_query"
                log_info "  🧠 Enhanced Query: ${enhanced_query}..."
                log_info "  📊 Results: $results_count"
                return 0
            else
                log_error "Search request failed"
                return 1
            fi
        else
            log_info "Enhanced search completed (jq not available for detailed parsing)"
            return 0
        fi
    else
        log_error "Enhanced search failed (HTTP $response)"
        return 1
    fi
}

# Test 6: Interaction Analysis
test_interaction_analysis() {
    log_info "Testing supplement interaction analysis..."
    
    local interaction_data='{
        "supplements": ["Vitamin D", "Magnesium", "Omega-3"]
    }'
    
    local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/enhanced-research/interactions" \
        -H "Content-Type: application/json" \
        -d "$interaction_data" \
        -o /tmp/interactions.json)
    
    if [ "$response" = "200" ]; then
        if command -v jq &> /dev/null; then
            local success=$(cat /tmp/interactions.json | jq -r '.success')
            
            if [ "$success" = "true" ]; then
                local supplements=$(cat /tmp/interactions.json | jq -r '.data.supplements | join(", ")')
                local timestamp=$(cat /tmp/interactions.json | jq -r '.data.timestamp')
                
                log_info "Interaction analysis completed!"
                log_info "  💊 Supplements: $supplements"
                log_info "  📅 Timestamp: $timestamp"
                return 0
            else
                log_error "Interaction analysis failed"
                return 1
            fi
        else
            log_info "Interaction analysis completed (jq not available for detailed parsing)"
            return 0
        fi
    else
        log_error "Interaction analysis failed (HTTP $response)"
        return 1
    fi
}

# Test 7: Active Flows
test_active_flows() {
    local response=$(curl -s -w "%{http_code}" "$API_BASE/enhanced-research/flows" -o /tmp/flows.json)
    
    if [ "$response" = "200" ]; then
        if command -v jq &> /dev/null; then
            local flow_count=$(cat /tmp/flows.json | jq -r '.count')
            log_info "Found $flow_count active flows"
            
            if [ "$flow_count" -gt 0 ]; then
                cat /tmp/flows.json | jq -r '.data[] | "  📊 \(.name) - Status: \(.status) (\(.progress)%)"'
            fi
        else
            log_info "Active flows retrieved (jq not available for detailed parsing)"
        fi
        return 0
    else
        log_error "Active flows failed (HTTP $response)"
        return 1
    fi
}

# Main test runner
main() {
    log_header "Enhanced Agentic Integration Test Suite"
    echo -e "${MAGENTA}⚡ Siła grzmotu, pełna moc wiatru! Duchy wody nam sprzyjają! ⚡${NC}"
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        log_warning "jq not found - JSON parsing will be limited"
    fi
    
    # Run all tests
    run_test "LM Studio Connection" "test_lm_studio"
    run_test "Backend Health Check" "test_backend_health"
    run_test "System Metrics" "test_system_metrics"
    run_test "Autonomous Research" "test_autonomous_research"
    run_test "Enhanced Search" "test_enhanced_search"
    run_test "Interaction Analysis" "test_interaction_analysis"
    run_test "Active Flows" "test_active_flows"
    
    # Final summary
    log_header "Test Results Summary"
    echo -e "${GREEN}✅ Passed: $PASSED${NC}"
    echo -e "${RED}❌ Failed: $FAILED${NC}"
    echo -e "${BLUE}📊 Total: $TOTAL${NC}"
    
    local success_rate=$((PASSED * 100 / TOTAL))
    echo -e "${CYAN}📈 Success Rate: ${success_rate}%${NC}"
    
    if [ $PASSED -eq $TOTAL ]; then
        echo ""
        echo -e "${GREEN}🎉 ALL TESTS PASSED! Enhanced Agentic System is FULLY OPERATIONAL! 🚀${NC}"
        echo -e "${MAGENTA}⚡ Siła grzmotu triumfuje! Pełna moc wiatru! ⚡${NC}"
        exit 0
    else
        echo ""
        echo -e "${YELLOW}⚠️  Some tests failed. Check the logs above for details.${NC}"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    rm -f /tmp/lm_models.json /tmp/health.json /tmp/metrics.json /tmp/research.json /tmp/status.json /tmp/search.json /tmp/interactions.json /tmp/flows.json
}

# Set up cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
