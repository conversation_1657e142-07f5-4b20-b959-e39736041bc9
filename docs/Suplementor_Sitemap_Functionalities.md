# Suplementor Platform: Sitemap of Functionalities

This document provides a hierarchical overview of the Suplementor platform's functionalities, distinguishing between currently implemented features and those planned for enhancement or addition. It serves as a "tree map" to visualize the current state and future direction of the user interface and underlying capabilities.

## 1. Existing Core Functionalities

These are the functionalities currently present or implied by the existing frontend structure.

*   **Home / Dashboard**
    *   `frontend/src/pages/ResearchPage.tsx` (Likely a central research dashboard)
    *   `frontend/src/pages/UltimateHealthInterface.tsx` (Broader health interface)
    *   `frontend/src/components/dashboard/CosmicDashboard.tsx`
    *   `frontend/src/components/dashboard/UltimateResearchDashboard.tsx`
*   **Supplement Research & Information**
    *   `frontend/src/components/supplement/SupplementResearchInterface.tsx` (Main interface for supplement research)
    *   `frontend/src/pages/SupplementKnowledgePage.tsx`
    *   `frontend/src/pages/SearchPage.tsx` (General search)
    *   `frontend/src/components/search/CosmicSupplementSearch.tsx`
*   **Knowledge Graph Visualization**
    *   `frontend/src/components/graph/CosmicKnowledgeGraph.tsx`
    *   `frontend/src/components/organisms/GraphVisualization.tsx`
    *   `frontend/src/pages/GraphPage.tsx`
    *   `frontend/src/pages/CosmicGraphPage.tsx`
    *   `frontend/src/pages/InfiniteGraphDemo.tsx`
*   **Data Upload**
    *   `frontend/src/pages/UploadPage.tsx`
*   **Analytics & Performance**
    *   `frontend/src/pages/AnalyticsPage.tsx`
    *   `frontend/src/components/analytics/PerformanceAnalytics.tsx`
*   **User Profile & Settings**
    *   `frontend/src/pages/SettingsPage.tsx`
    *   `frontend/src/components/profile/EnhancedUserProfile.tsx`
    *   `frontend/src/components/profile/HealthProfileManager.tsx`
*   **Specific Health/Neuroregulation Pages**
    *   `frontend/src/pages/NeuroregulationPage.tsx`
    *   `frontend/src/pages/SerotoninPage.tsx`
    *   `frontend/src/components/neuro/NeuroregulationCard.tsx`
    *   `frontend/src/components/neuro/NeurotransmitterCard.tsx`
*   **About Page**
    *   `frontend/src/pages/AboutPage.tsx`
*   **Chat Interface**
    *   `frontend/src/components/chat/CognitiveChat.tsx`
    *   `frontend/src/components/chat/GemmaChat.tsx`

## 2. Planned & Enhanced Functionalities

These functionalities are proposed enhancements or new additions based on the comprehensive plans outlined in `docs/AI_Models_Dashboard_Improvement_Plan.md`, `docs/Unified_Research_Orchestration.md`, `docs/Generative_Database_Interface_Plan.md`, and `docs/Overall_Interface_Design_Plan.md`.

*   **AI Models Management (Enhanced)**
    *   **AI Models Dashboard (`AIModelsDashboard.tsx`):**
        *   **Gemma3 MEDICAL Model Details:** Dedicated display and metrics for the specialized Gemma3 MEDICAL model.
        *   **AG-UI Protocol Status:** Real-time connection status and health of the AG-UI communication channel.
        *   **Enhanced Metrics & Visualizations:** Historical performance trends, comparative analysis, granular cost breakdown, throughput/latency.
        *   **Advanced Filtering & Sorting:** Multi-faceted filtering by provider, model type, capabilities, domain, availability, and performance. Customizable sorting.
        *   **Model Management Actions:** Enable/disable models, view versions, deployment status, retraining triggers (requires backend).
*   **Research & Knowledge Discovery (Enhanced)**
    *   **Agentic Research Initiation:**
        *   User interface to define complex research goals and parameters for autonomous agents.
    *   **Live Research Monitoring:**
        *   Real-time feed of agent activities, progress, and intermediate results (via AG-UI).
    *   **Structured Research Results Viewer:**
        *   Presentation of "new results after agentic scanning research scanning" in a clear, digestible format.
        *   Integration of AI-generated summaries, key findings, and warnings.
        *   Semantic search capabilities for research documents (leveraging Weaviate).
    *   **Unified Research Orchestration (Backend-driven, UI Impact):**
        *   Coordination of `ResearchService`, `GemmaService`, `Gemma3Neo4jProcessor`, `GraphService`, Weaviate, and MongoDB by `AutonomousResearchOrchestrator`.
*   **Generative Data Management (New)**
    *   **Data Generation Trigger Interface:**
        *   UI for users to manually request generation of new supplement profiles, knowledge, or data.
    *   **AI-Generated Content Review Queue:**
        *   A dedicated section for human experts to review, edit, and approve/reject AI-generated content before full integration into the knowledge base.
    *   **Generated Content Detail View:**
        *   Detailed view of generated items with comparison tools and audit trails.
*   **Overall Interface Design (Cross-cutting Enhancements)**
    *   **Global Navigation:** Consistent sidebar navigation for primary sections.
    *   **Header:** Global search, notifications, user quick actions.
    *   **Responsive Layout:** Optimized for various screen sizes.
    *   **Dark Mode:** User-selectable dark theme.
    *   **Accessibility Features:** WCAG compliance for improved usability.
    *   **Modular Component Architecture:** Ensuring scalability and maintainability.

## 3. Visual Representation of Key Areas

```mermaid
graph TD
    A[Suplementor Platform] --> B[Dashboard/Overview];
    A --> C[AI Models Management];
    A --> D[Research & Knowledge Discovery];
    A --> E[Generative Data Management];
    A --> F[User Profile & Settings];
    A --> G[Data Upload];
    A --> H[Analytics & Performance];
    A --> I[About];
    A --> J[Specific Health Pages];

    C --> C1[AI Models Dashboard (Enhanced)];
    C1 --> C1a[Gemma3 MEDICAL Details];
    C1 --> C1b[AG-UI Status];
    C1 --> C1c[Enhanced Metrics];
    C1 --> C1d[Advanced Filtering];
    C1 --> C1e[Model Actions];

    D --> D1[Agentic Research Initiation];
    D --> D2[Live Research Monitoring];
    D --> D3[Structured Research Results];
    D --> D4[Knowledge Graph Visualization];
    D --> D5[Semantic Search];

    E --> E1[Data Generation Trigger];
    E --> E2[Content Review Queue];
    E --> E3[Generated Content Detail];
```

This sitemap provides a clear roadmap for the evolution of the Suplementor platform's interface, ensuring a structured and user-centric approach to development.