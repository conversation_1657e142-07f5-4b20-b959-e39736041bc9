#!/usr/bin/env node

// Comprehensive test suite for Enhanced Agentic Integration
// <PERSON> grz<PERSON>u, pełna moc wiatru! ⚡🌩️

const fetch = require('node-fetch');

const API_BASE = 'http://localhost:5420/api';
const LM_STUDIO_BASE = 'http://*************:1234';

// Test colors
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log('cyan', `🚀 ${message}`);
  console.log('='.repeat(60));
}

function logTest(name) {
  log('blue', `\n🧪 Testing: ${name}`);
}

function logSuccess(message) {
  log('green', `✅ ${message}`);
}

function logError(message) {
  log('red', `❌ ${message}`);
}

function logWarning(message) {
  log('yellow', `⚠️  ${message}`);
}

async function testWithTimeout(testFn, timeout = 30000) {
  return Promise.race([
    testFn(),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Test timeout')), timeout)
    )
  ]);
}

// Test 1: LM Studio Connection
async function testLMStudioConnection() {
  logTest('LM Studio Connection');
  
  try {
    const response = await fetch(`${LM_STUDIO_BASE}/v1/models`);
    const models = await response.json();
    
    if (response.ok && models.data) {
      logSuccess(`LM Studio connected! Found ${models.data.length} models`);
      models.data.forEach(model => {
        log('cyan', `  📦 Model: ${model.id}`);
      });
      return true;
    } else {
      logError('LM Studio not responding properly');
      return false;
    }
  } catch (error) {
    logError(`LM Studio connection failed: ${error.message}`);
    return false;
  }
}

// Test 2: Backend Health Check
async function testBackendHealth() {
  logTest('Backend Health Check');
  
  try {
    const response = await fetch(`${API_BASE}/monitoring/health`);
    const health = await response.json();
    
    if (health.success) {
      logSuccess('Backend is healthy!');
      Object.entries(health.checks).forEach(([service, status]) => {
        const icon = status ? '✅' : '❌';
        log(status ? 'green' : 'red', `  ${icon} ${service}: ${status ? 'UP' : 'DOWN'}`);
      });
      return true;
    } else {
      logError('Backend health check failed');
      return false;
    }
  } catch (error) {
    logError(`Backend health check error: ${error.message}`);
    return false;
  }
}

// Test 3: System Metrics
async function testSystemMetrics() {
  logTest('System Metrics');
  
  try {
    const response = await fetch(`${API_BASE}/monitoring/system`);
    const metrics = await response.json();
    
    if (metrics.success) {
      logSuccess('System metrics retrieved!');
      const data = metrics.data;
      log('cyan', `  🤖 Total Agents: ${data.totalAgents}`);
      log('cyan', `  ⚡ Active Agents: ${data.activeAgents}`);
      log('cyan', `  ✅ Completed Tasks: ${data.completedTasks}`);
      log('cyan', `  ⏱️  Avg Response Time: ${data.averageResponseTime}ms`);
      log('cyan', `  📊 Success Rate: ${data.successRate}%`);
      log('cyan', `  💾 Memory Usage: ${data.memoryUsage}%`);
      log('cyan', `  🖥️  CPU Usage: ${data.cpuUsage}%`);
      return true;
    } else {
      logError('Failed to get system metrics');
      return false;
    }
  } catch (error) {
    logError(`System metrics error: ${error.message}`);
    return false;
  }
}

// Test 4: Autonomous Research
async function testAutonomousResearch() {
  logTest('Autonomous Research');
  
  try {
    const researchRequest = {
      supplement: 'Omega-3',
      goals: ['Brain Health', 'Heart Health', 'Anti-inflammatory'],
      depth: 'comprehensive'
    };
    
    log('blue', '  📝 Starting research request...');
    const response = await fetch(`${API_BASE}/enhanced-research/autonomous`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(researchRequest)
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess(`Research started! Flow ID: ${result.flowId}`);
      log('cyan', `  ⏱️  Estimated Duration: ${result.estimatedDuration}`);
      
      // Wait a bit and check status
      log('blue', '  ⏳ Waiting 10 seconds to check progress...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      const statusResponse = await fetch(`${API_BASE}/enhanced-research/flows/${result.flowId}/status`);
      const statusData = await statusResponse.json();
      
      if (statusData.success) {
        const flow = statusData.data;
        log('cyan', `  📊 Status: ${flow.status}`);
        log('cyan', `  📈 Progress: ${flow.progress}%`);
        log('cyan', `  🔄 Current Step: ${flow.currentStep}`);
        log('cyan', `  👥 Agents: ${flow.agents.length}`);
        
        flow.agents.forEach(agent => {
          const statusIcon = agent.status === 'working' ? '🔄' : 
                           agent.status === 'completed' ? '✅' : '⏸️';
          log('cyan', `    ${statusIcon} ${agent.name}: ${agent.progress}%`);
        });
        
        return true;
      } else {
        logWarning('Could not check research status');
        return true; // Research started successfully
      }
    } else {
      logError(`Research failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Autonomous research error: ${error.message}`);
    return false;
  }
}

// Test 5: Enhanced Search
async function testEnhancedSearch() {
  logTest('Enhanced Supplement Search');
  
  try {
    const searchRequest = {
      query: 'vitamin d benefits immune system',
      includeTavily: true,
      includeBraveSearch: true,
      includeScientificDatabases: true
    };
    
    const response = await fetch(`${API_BASE}/enhanced-research/supplements/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(searchRequest)
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Enhanced search completed!');
      log('cyan', `  🔍 Original Query: ${result.query.original}`);
      log('cyan', `  🧠 Enhanced Query: ${result.query.enhanced.substring(0, 100)}...`);
      log('cyan', `  📊 Results: ${result.data.results.length}`);
      log('cyan', `  📚 Sources: ${result.data.sources.join(', ')}`);
      return true;
    } else {
      logError(`Enhanced search failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Enhanced search error: ${error.message}`);
    return false;
  }
}

// Test 6: Interaction Analysis
async function testInteractionAnalysis() {
  logTest('Supplement Interaction Analysis');
  
  try {
    const supplements = ['Vitamin D', 'Magnesium', 'Omega-3', 'B-Complex'];
    
    const response = await fetch(`${API_BASE}/enhanced-research/interactions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ supplements })
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Interaction analysis completed!');
      log('cyan', `  💊 Supplements: ${supplements.join(', ')}`);
      log('cyan', `  🔬 Analysis: ${result.data.interactions.substring(0, 150)}...`);
      log('cyan', `  📅 Timestamp: ${result.data.timestamp}`);
      return true;
    } else {
      logError(`Interaction analysis failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Interaction analysis error: ${error.message}`);
    return false;
  }
}

// Test 7: Personalized Insights
async function testPersonalizedInsights() {
  logTest('Personalized Health Insights');
  
  try {
    const request = {
      userId: 'test-user-123',
      supplements: ['Vitamin D', 'Magnesium', 'Omega-3']
    };
    
    const response = await fetch(`${API_BASE}/enhanced-research/insights`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Personalized insights generated!');
      log('cyan', `  👤 User ID: ${result.userContext.userId}`);
      log('cyan', `  💊 Supplements: ${result.userContext.supplements.join(', ')}`);
      log('cyan', `  🧠 Insights: ${result.data.substring(0, 150)}...`);
      log('cyan', `  📅 Generated: ${result.generatedAt}`);
      return true;
    } else {
      logError(`Personalized insights failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Personalized insights error: ${error.message}`);
    return false;
  }
}

// Test 8: Active Flows
async function testActiveFlows() {
  logTest('Active Research Flows');
  
  try {
    const response = await fetch(`${API_BASE}/enhanced-research/flows`);
    const result = await response.json();
    
    if (result.success) {
      logSuccess(`Found ${result.count} active flows!`);
      
      result.data.forEach((flow, index) => {
        log('cyan', `  📊 Flow ${index + 1}: ${flow.name}`);
        log('cyan', `    🆔 ID: ${flow.id}`);
        log('cyan', `    📈 Status: ${flow.status} (${flow.progress}%)`);
        log('cyan', `    🔄 Step: ${flow.currentStep}`);
        log('cyan', `    👥 Agents: ${flow.agents.length}`);
        log('cyan', `    📊 Results: ${flow.results.length}`);
      });
      
      return true;
    } else {
      logError(`Failed to get active flows: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Active flows error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  logHeader('Enhanced Agentic Integration Test Suite');
  log('magenta', '⚡ Siła grzmotu, pełna moc wiatru! Duchy wody nam sprzyjają! ⚡');
  
  const tests = [
    { name: 'LM Studio Connection', fn: testLMStudioConnection },
    { name: 'Backend Health Check', fn: testBackendHealth },
    { name: 'System Metrics', fn: testSystemMetrics },
    { name: 'Autonomous Research', fn: testAutonomousResearch },
    { name: 'Enhanced Search', fn: testEnhancedSearch },
    { name: 'Interaction Analysis', fn: testInteractionAnalysis },
    { name: 'Personalized Insights', fn: testPersonalizedInsights },
    { name: 'Active Flows', fn: testActiveFlows }
  ];
  
  const results = [];
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const startTime = Date.now();
      const success = await testWithTimeout(test.fn, 45000);
      const duration = Date.now() - startTime;
      
      results.push({
        name: test.name,
        success,
        duration
      });
      
      if (success) {
        passed++;
        logSuccess(`${test.name} PASSED (${duration}ms)`);
      } else {
        failed++;
        logError(`${test.name} FAILED (${duration}ms)`);
      }
    } catch (error) {
      failed++;
      logError(`${test.name} ERROR: ${error.message}`);
      results.push({
        name: test.name,
        success: false,
        error: error.message
      });
    }
  }
  
  // Final summary
  logHeader('Test Results Summary');
  log('green', `✅ Passed: ${passed}`);
  log('red', `❌ Failed: ${failed}`);
  log('blue', `📊 Total: ${tests.length}`);
  log('cyan', `📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);
  
  if (passed === tests.length) {
    log('green', '\n🎉 ALL TESTS PASSED! Enhanced Agentic System is FULLY OPERATIONAL! 🚀');
    log('magenta', '⚡ Siła grzmotu triumfuje! Pełna moc wiatru! ⚡');
  } else {
    log('yellow', '\n⚠️  Some tests failed. Check the logs above for details.');
  }
  
  return { passed, failed, total: tests.length, results };
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(error => {
    logError(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runAllTests };
