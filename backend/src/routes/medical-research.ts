import { Router, Request, Response } from 'express';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from '../services/PerformanceMonitoringService';

const router = Router();

// Medical Research & AI Analysis Module
// Generates dynamic supplement product cards with AI-powered recommendations

interface SupplementCard {
  id: string;
  name: string;
  category: string;
  description: string;
  benefits: string[];
  dosage: string;
  interactions: string[];
  contraindications: string[];
  evidenceLevel: 'high' | 'moderate' | 'low' | 'preliminary';
  aiConfidence: number;
  price: number;
  availability: 'in-stock' | 'low-stock' | 'out-of-stock';
  imageUrl?: string;
  researchLinks: string[];
  userRating: number;
  reviewCount: number;
}

interface MedicalAnalysis {
  patientProfile: {
    age: number;
    gender: string;
    conditions: string[];
    medications: string[];
    allergies: string[];
    goals: string[];
  };
  recommendations: SupplementCard[];
  warnings: string[];
  interactions: Array<{
    supplement1: string;
    supplement2: string;
    severity: 'mild' | 'moderate' | 'severe';
    description: string;
  }>;
  totalCost: number;
  confidenceScore: number;
}

/**
 * POST /api/medical-research/analyze
 * Perform AI-powered medical analysis and generate supplement recommendations
 */
router.post('/analyze', async (req: Request, res: Response) => {
  const metricId = performanceMonitoringService.trackAIOperation('medical_analysis', 'ai_research');
  
  try {
    const { patientProfile, symptoms, goals, budget } = req.body;

    if (!patientProfile || !symptoms) {
      return res.status(400).json({
        success: false,
        error: 'Patient profile and symptoms are required'
      });
    }

    // Mock AI-powered medical analysis
    const mockSupplements: SupplementCard[] = [
      {
        id: 'supp_001',
        name: 'Omega-3 EPA/DHA Complex',
        category: 'Essential Fatty Acids',
        description: 'High-potency fish oil with optimal EPA/DHA ratio for cardiovascular and cognitive health',
        benefits: ['Heart health', 'Brain function', 'Anti-inflammatory', 'Joint support'],
        dosage: '2 capsules daily with meals',
        interactions: ['Blood thinners', 'Anticoagulants'],
        contraindications: ['Fish allergy', 'Bleeding disorders'],
        evidenceLevel: 'high',
        aiConfidence: 92,
        price: 45.99,
        availability: 'in-stock',
        imageUrl: '/images/supplements/omega3.jpg',
        researchLinks: [
          'https://pubmed.ncbi.nlm.nih.gov/12345',
          'https://pubmed.ncbi.nlm.nih.gov/67890'
        ],
        userRating: 4.7,
        reviewCount: 1247
      },
      {
        id: 'supp_002',
        name: 'Vitamin D3 + K2 Synergy',
        category: 'Vitamins',
        description: 'Bioactive vitamin D3 with K2 MK-7 for optimal calcium metabolism and bone health',
        benefits: ['Bone health', 'Immune support', 'Calcium absorption', 'Cardiovascular health'],
        dosage: '1 capsule daily',
        interactions: ['Calcium channel blockers', 'Thiazide diuretics'],
        contraindications: ['Hypercalcemia', 'Kidney stones'],
        evidenceLevel: 'high',
        aiConfidence: 89,
        price: 32.50,
        availability: 'in-stock',
        imageUrl: '/images/supplements/vitamin-d3-k2.jpg',
        researchLinks: [
          'https://pubmed.ncbi.nlm.nih.gov/11111',
          'https://pubmed.ncbi.nlm.nih.gov/22222'
        ],
        userRating: 4.5,
        reviewCount: 892
      },
      {
        id: 'supp_003',
        name: 'Magnesium Glycinate Complex',
        category: 'Minerals',
        description: 'Highly bioavailable magnesium for muscle relaxation and nervous system support',
        benefits: ['Muscle relaxation', 'Sleep quality', 'Stress reduction', 'Energy metabolism'],
        dosage: '2 capsules before bedtime',
        interactions: ['Antibiotics', 'Bisphosphonates'],
        contraindications: ['Kidney disease', 'Heart block'],
        evidenceLevel: 'moderate',
        aiConfidence: 85,
        price: 28.75,
        availability: 'low-stock',
        imageUrl: '/images/supplements/magnesium.jpg',
        researchLinks: [
          'https://pubmed.ncbi.nlm.nih.gov/33333'
        ],
        userRating: 4.6,
        reviewCount: 634
      },
      {
        id: 'supp_004',
        name: 'Curcumin Phytosome',
        category: 'Herbal Extracts',
        description: 'Enhanced absorption curcumin with phosphatidylcholine for maximum bioavailability',
        benefits: ['Anti-inflammatory', 'Joint health', 'Antioxidant', 'Cognitive support'],
        dosage: '1 capsule twice daily',
        interactions: ['Blood thinners', 'Diabetes medications'],
        contraindications: ['Gallstones', 'Bile duct obstruction'],
        evidenceLevel: 'moderate',
        aiConfidence: 78,
        price: 54.99,
        availability: 'in-stock',
        imageUrl: '/images/supplements/curcumin.jpg',
        researchLinks: [
          'https://pubmed.ncbi.nlm.nih.gov/44444',
          'https://pubmed.ncbi.nlm.nih.gov/55555'
        ],
        userRating: 4.3,
        reviewCount: 456
      }
    ];

    const analysis: MedicalAnalysis = {
      patientProfile,
      recommendations: mockSupplements,
      warnings: [
        'Consult healthcare provider before starting any new supplements',
        'Monitor for potential drug interactions',
        'Start with lower doses to assess tolerance'
      ],
      interactions: [
        {
          supplement1: 'Omega-3 EPA/DHA Complex',
          supplement2: 'Curcumin Phytosome',
          severity: 'mild',
          description: 'Both supplements may increase bleeding risk when combined'
        }
      ],
      totalCost: mockSupplements.reduce((sum, supp) => sum + supp.price, 0),
      confidenceScore: 87
    };

    performanceMonitoringService.endMetric(metricId, true);
    logger.info('Medical analysis completed successfully');
    
    return res.json({
      success: true,
      data: analysis,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Medical analysis failed');
    logger.error('Medical analysis error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to perform medical analysis'
    });
  }
});

/**
 * POST /api/medical-research/supplements/add
 * Add supplement to user's regimen
 */
router.post('/supplements/add', async (req: Request, res: Response) => {
  const metricId = performanceMonitoringService.trackApiRequest('add_supplement', 'POST');

  try {
    const { userId, supplementId, dosage, frequency, notes } = req.body;

    if (!userId || !supplementId) {
      return res.status(400).json({
        success: false,
        error: 'User ID and supplement ID are required'
      });
    }

    // Mock adding supplement to user regimen
    const userSupplement = {
      id: `user_supp_${Date.now()}`,
      userId,
      supplementId,
      dosage: dosage || 'As recommended',
      frequency: frequency || 'Daily',
      notes: notes || '',
      dateAdded: new Date().toISOString(),
      status: 'active'
    };

    performanceMonitoringService.endMetric(metricId, true);
    logger.info(`Supplement ${supplementId} added to user ${userId} regimen`);

    return res.json({
      success: true,
      data: userSupplement,
      message: 'Supplement added to your regimen successfully'
    });
  } catch (error) {
    performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to add supplement');
    logger.error('Add supplement error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to add supplement to regimen'
    });
  }
});

/**
 * DELETE /api/medical-research/supplements/:id/remove
 * Remove supplement from user's regimen
 */
router.delete('/supplements/:id/remove', async (req: Request, res: Response) => {
  const metricId = performanceMonitoringService.trackApiRequest('remove_supplement', 'DELETE');

  try {
    const { id } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Mock removing supplement from user regimen
    performanceMonitoringService.endMetric(metricId, true);
    logger.info(`Supplement ${id} removed from user ${userId} regimen`);

    return res.json({
      success: true,
      message: 'Supplement removed from your regimen successfully'
    });
  } catch (error) {
    performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to remove supplement');
    logger.error('Remove supplement error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to remove supplement from regimen'
    });
  }
});

/**
 * GET /api/medical-research/supplements
 * Get available supplements database
 */
router.get('/supplements', async (req: Request, res: Response) => {
  const metricId = performanceMonitoringService.trackApiRequest('get_supplements', 'GET');
  
  try {
    const { category, search, limit = 20 } = req.query;

    // Mock supplements database
    const allSupplements: SupplementCard[] = [
      // Previous supplements plus more...
      {
        id: 'supp_005',
        name: 'Probiotics Multi-Strain',
        category: 'Probiotics',
        description: '50 billion CFU multi-strain probiotic for digestive and immune health',
        benefits: ['Digestive health', 'Immune support', 'Mood regulation', 'Nutrient absorption'],
        dosage: '1 capsule daily on empty stomach',
        interactions: ['Antibiotics'],
        contraindications: ['Immunocompromised conditions'],
        evidenceLevel: 'high',
        aiConfidence: 91,
        price: 39.99,
        availability: 'in-stock',
        imageUrl: '/images/supplements/probiotics.jpg',
        researchLinks: ['https://pubmed.ncbi.nlm.nih.gov/66666'],
        userRating: 4.4,
        reviewCount: 723
      },
      {
        id: 'supp_006',
        name: 'Ashwagandha KSM-66',
        category: 'Adaptogens',
        description: 'Clinically studied ashwagandha extract for stress management and vitality',
        benefits: ['Stress reduction', 'Energy levels', 'Sleep quality', 'Cognitive function'],
        dosage: '1 capsule daily with breakfast',
        interactions: ['Sedatives', 'Immunosuppressants'],
        contraindications: ['Pregnancy', 'Autoimmune conditions'],
        evidenceLevel: 'moderate',
        aiConfidence: 83,
        price: 42.50,
        availability: 'in-stock',
        imageUrl: '/images/supplements/ashwagandha.jpg',
        researchLinks: ['https://pubmed.ncbi.nlm.nih.gov/77777'],
        userRating: 4.2,
        reviewCount: 567
      }
    ];

    let filteredSupplements = allSupplements;

    if (category) {
      filteredSupplements = filteredSupplements.filter(s => 
        s.category.toLowerCase().includes((category as string).toLowerCase())
      );
    }

    if (search) {
      filteredSupplements = filteredSupplements.filter(s => 
        s.name.toLowerCase().includes((search as string).toLowerCase()) ||
        s.description.toLowerCase().includes((search as string).toLowerCase())
      );
    }

    const limitedSupplements = filteredSupplements.slice(0, Number(limit));

    performanceMonitoringService.endMetric(metricId, true);
    
    return res.json({
      success: true,
      data: {
        supplements: limitedSupplements,
        total: filteredSupplements.length,
        categories: [...new Set(allSupplements.map(s => s.category))]
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get supplements');
    logger.error('Get supplements error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve supplements'
    });
  }
});

export default router;
