import express, { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import ResearchAIService from '@/services/ResearchAIService';
import UserProfileService from '@/services/UserProfileService';
import { logger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';

const router = express.Router();
const researchAIService = new ResearchAIService();
const userProfileService = new UserProfileService();

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed');
  }
  next();
};

// POST /api/research-ai/search - Advanced research search with AI analysis
router.post('/search',
  body('query').optional().isString(),
  body('supplement').optional().isString(),
  body('condition').optional().isString(),
  body('studyType').optional().isIn(['RCT', 'Meta-Analysis', 'Observational', 'Review', 'Case Study']),
  body('minYear').optional().isInt({ min: 1990, max: 2024 }),
  body('minEvidenceLevel').optional().isInt({ min: 1, max: 5 }),
  body('includeMetaAnalysis').optional().isBoolean(),
  body('maxResults').optional().isInt({ min: 1, max: 100 }),
  body('userId').optional().isString(),
  validateRequest,
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const {
        query,
        supplement,
        condition,
        studyType,
        minYear,
        minEvidenceLevel,
        includeMetaAnalysis,
        maxResults = 20,
        userId,
      } = req.body;

      // Build research query
      const researchQuery = {
        supplement: supplement || query,
        condition,
        studyType,
        minYear,
        minEvidenceLevel,
        includeMetaAnalysis,
        maxResults,
      };

      // Search research papers
      const papers = await researchAIService.searchResearchPapers(researchQuery);

      // Get user profile for personalized insights
      let userProfile = null;
      if (userId) {
        userProfile = await userProfileService.getUserProfile(userId);
      }

      // Generate AI insights
      const aiInsights = await researchAIService.generateAIInsights(papers, userProfile);

      // Calculate research statistics
      const stats = {
        totalPapers: papers.length,
        avgQualityScore: papers.reduce((sum, p) => sum + p.qualityScore, 0) / papers.length,
        avgEvidenceLevel: papers.reduce((sum, p) => sum + p.evidenceLevel, 0) / papers.length,
        avgConfidence: papers.reduce((sum, p) => sum + p.findings.confidence, 0) / papers.length,
        studyTypeDistribution: papers.reduce((acc, p) => {
          acc[p.studyType] = (acc[p.studyType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      };

      logger.info(`Research AI search completed`, {
        query: researchQuery,
        resultsCount: papers.length,
        insightsCount: aiInsights.length,
        userId,
      });

      return res.json({
        success: true,
        data: {
          papers,
          insights: aiInsights,
          stats,
          query: researchQuery,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research-ai/insights/:userId - Get personalized research insights
router.get('/insights/:userId',
  param('userId').isString().notEmpty(),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  validateRequest,
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;
      const limit = parseInt(req.query.limit as string) || 10;

      // Get user profile
      const userProfile = await userProfileService.getUserProfile(userId);
      if (!userProfile) {
        return res.status(404).json({
          success: false,
          message: 'User profile not found',
        });
      }

      // Search for research relevant to user's supplements
      const userSupplements = userProfile.currentSupplements || [];
      const researchPromises = userSupplements.slice(0, 5).map(supplement =>
        researchAIService.searchResearchPapers({
          supplement,
          minEvidenceLevel: 3,
          maxResults: 5,
        })
      );

      const researchResults = await Promise.all(researchPromises);
      const allPapers = researchResults.flat();

      // Generate personalized insights
      const insights = await researchAIService.generateAIInsights(allPapers, userProfile);

      return res.json({
        success: true,
        data: insights.slice(0, limit),
        count: insights.length,
        userSupplements,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research-ai/analyze-interactions - Analyze supplement interactions
router.post('/analyze-interactions',
  body('supplementIds').isArray().notEmpty(),
  body('userId').optional().isString(),
  validateRequest,
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const { supplementIds, userId } = req.body;

      // Analyze interactions using Neo4j
      const interactions = await researchAIService.analyzeSupplementInteractions(supplementIds);

      // Get user profile for personalized analysis
      let userProfile = null;
      if (userId) {
        userProfile = await userProfileService.getUserProfile(userId);
      }

      // Generate interaction insights
      const interactionInsights = interactions.map(interaction => ({
        ...interaction,
        personalizedRisk: userProfile ? calculatePersonalizedRisk(interaction, userProfile) : null,
        recommendations: generateInteractionRecommendations(interaction),
      }));

      // Calculate overall interaction score
      const overallScore = calculateOverallInteractionScore(interactions);

      return res.json({
        success: true,
        data: {
          interactions: interactionInsights,
          overallScore,
          riskLevel: overallScore >= 8 ? 'low' : overallScore >= 6 ? 'moderate' : 'high',
          summary: generateInteractionSummary(interactions),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research-ai/monitor/:userId - Real-time research monitoring
router.get('/monitor/:userId',
  param('userId').isString().notEmpty(),
  validateRequest,
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const { userId } = req.params;

      // Get user profile
      const userProfile = await userProfileService.getUserProfile(userId);
      if (!userProfile) {
        return res.status(404).json({
          success: false,
          message: 'User profile not found',
        });
      }

      // Monitor research for user's supplements
      const userSupplements = userProfile.currentSupplements || [];
      const monitoringData = await researchAIService.monitorRealTimeResearch(userSupplements);

      return res.json({
        success: true,
        data: monitoringData,
        userSupplements,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research-ai/generate-report - Generate comprehensive research report
router.post('/generate-report',
  body('userId').isString().notEmpty(),
  body('supplements').optional().isArray(),
  body('timeframe').optional().isIn(['week', 'month', 'quarter', 'year']),
  body('includeInteractions').optional().isBoolean(),
  body('includePersonalized').optional().isBoolean(),
  validateRequest,
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const {
        userId,
        supplements,
        timeframe = 'month',
        includeInteractions = true,
        includePersonalized = true,
      } = req.body;

      // Get user profile
      const userProfile = await userProfileService.getUserProfile(userId);
      if (!userProfile) {
        return res.status(404).json({
          success: false,
          message: 'User profile not found',
        });
      }

      const targetSupplements = supplements || userProfile.currentSupplements || [];

      // Generate comprehensive research report
      const report = await generateComprehensiveReport(
        targetSupplements,
        userProfile,
        timeframe,
        includeInteractions,
        includePersonalized
      );

      return res.json({
        success: true,
        data: report,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
);

// Helper functions
function calculatePersonalizedRisk(interaction: any, userProfile: any): string {
  let riskLevel = interaction.safetyRating;

  // Adjust risk based on user profile
  if (userProfile.medicalConditions && userProfile.medicalConditions.length > 0) {
    riskLevel -= 1; // Increase caution for users with medical conditions
  }

  if (userProfile.profileData.age && userProfile.profileData.age > 65) {
    riskLevel -= 0.5; // Increase caution for older adults
  }

  if (riskLevel >= 8) return 'low';
  if (riskLevel >= 6) return 'moderate';
  return 'high';
}

function generateInteractionRecommendations(interaction: any): string[] {
  const recommendations = [];

  if (interaction.type === 'SYNERGIZES_WITH') {
    recommendations.push('Consider taking these supplements together for enhanced benefits');
    recommendations.push('Monitor for increased effects and adjust dosing if needed');
  } else if (interaction.type === 'ANTAGONIZES_WITH') {
    recommendations.push('Separate dosing by at least 2-4 hours');
    recommendations.push('Consider alternative supplements if interaction is significant');
  }

  if (interaction.evidenceLevel < 3) {
    recommendations.push('Limited evidence - monitor closely and consult healthcare provider');
  }

  return recommendations;
}

function calculateOverallInteractionScore(interactions: any[]): number {
  if (interactions.length === 0) return 10;

  const avgSafety = interactions.reduce((sum, i) => sum + i.safetyRating, 0) / interactions.length;
  const avgEvidence = interactions.reduce((sum, i) => sum + i.evidenceLevel, 0) / interactions.length;

  return Math.round((avgSafety + avgEvidence) / 2 * 10) / 10;
}

function generateInteractionSummary(interactions: any[]): string {
  const synergistic = interactions.filter(i => i.type === 'SYNERGIZES_WITH').length;
  const antagonistic = interactions.filter(i => i.type === 'ANTAGONIZES_WITH').length;
  const neutral = interactions.length - synergistic - antagonistic;

  return `Found ${interactions.length} interactions: ${synergistic} synergistic, ${antagonistic} antagonistic, ${neutral} neutral`;
}

async function generateComprehensiveReport(
  supplements: string[],
  userProfile: any,
  timeframe: string,
  includeInteractions: boolean,
  includePersonalized: boolean
): Promise<any> {
  const report = {
    summary: {
      supplementCount: supplements.length,
      timeframe,
      generatedAt: new Date().toISOString(),
    },
    supplements: [] as any[],
    interactions: [] as any[],
    insights: [] as any[],
    recommendations: [] as any[],
  };

  // Add supplement-specific research
  for (const supplement of supplements) {
    const papers = await researchAIService.searchResearchPapers({
      supplement,
      minEvidenceLevel: 3,
      maxResults: 10,
    });

    report.supplements.push({
      name: supplement,
      researchCount: papers.length,
      avgQuality: papers.reduce((sum, p) => sum + p.qualityScore, 0) / papers.length,
      keyFindings: papers.slice(0, 3).map(p => p.findings.primary),
    });
  }

  // Add interaction analysis if requested
  if (includeInteractions) {
    const interactions = await researchAIService.analyzeSupplementInteractions(supplements);
    report.interactions = interactions;
  }

  // Add personalized insights if requested
  if (includePersonalized) {
    const allPapers = await Promise.all(
      supplements.map(supp =>
        researchAIService.searchResearchPapers({ supplement: supp, maxResults: 5 })
      )
    );
    const insights = await researchAIService.generateAIInsights(allPapers.flat(), userProfile);
    report.insights = insights;
  }

  return report;
}

export default router;
