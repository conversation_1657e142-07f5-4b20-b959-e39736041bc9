import { Router, Request, Response } from 'express';
import { MongoClient } from 'mongodb';
import { UserService, User, HealthProfile, HealthGoal, BiometricData, LabResults } from '../services/UserService';
import { logger } from '../utils/logger';

const router = Router();

// Initialize UserService (this would typically be done via dependency injection)
let userService: UserService;

// Middleware to initialize UserService
router.use(async (req: Request, res: Response, next: Function) => {
  if (!userService) {
    try {
      const mongoClient = new MongoClient(process.env.MONGODB_URI || 'mongodb://localhost:27017');
      await mongoClient.connect();
      const db = mongoClient.db('suplementor');
      userService = new UserService(db);
    } catch (error) {
      logger.error('Failed to initialize UserService:', error);
      return res.status(500).json({ error: 'Database connection failed' });
    }
  }
  return next();
});

// Create new user
router.post('/', async (req: Request, res: Response) => {
  try {
    const userData: Partial<User> = req.body;
    
    // Validate required fields
    if (!userData.email || !userData.passwordHash || !userData.profile) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const user = await userService.createUser(userData);
    
    // Remove password hash from response
    const { passwordHash, ...userResponse } = user;
    
    return res.status(201).json({
      success: true,
      data: userResponse,
      message: 'User created successfully'
    });
  } catch (error) {
    logger.error('Error creating user:', error);
    return res.status(500).json({ error: 'Failed to create user' });
  }
});

// Get user by ID
router.get('/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Remove password hash from response
    const { passwordHash, ...userResponse } = user;
    
    return res.json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    logger.error('Error fetching user:', error);
    return res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Update health profile
router.put('/:userId/health-profile', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const profileUpdate: Partial<HealthProfile> = req.body;
    
    const updatedUser = await userService.updateHealthProfile(userId, profileUpdate);
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.json({
      success: true,
      data: updatedUser.healthProfile,
      message: 'Health profile updated successfully'
    });
  } catch (error) {
    logger.error('Error updating health profile:', error);
    return res.status(500).json({ error: 'Failed to update health profile' });
  }
});

// Add health goal
router.post('/:userId/health-goals', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const goal: HealthGoal = req.body;
    
    // Validate goal data
    if (!goal.id || !goal.category || !goal.description) {
      return res.status(400).json({ error: 'Missing required goal fields' });
    }

    const updatedUser = await userService.addHealthGoal(userId, goal);
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(201).json({
      success: true,
      data: goal,
      message: 'Health goal added successfully'
    });
  } catch (error) {
    logger.error('Error adding health goal:', error);
    return res.status(500).json({ error: 'Failed to add health goal' });
  }
});

// Add biometric data
router.post('/:userId/biometric-data', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const biometricData: BiometricData = req.body;
    
    // Validate biometric data
    if (!biometricData.timestamp) {
      biometricData.timestamp = new Date();
    }

    const updatedUser = await userService.addBiometricData(userId, biometricData);
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(201).json({
      success: true,
      data: biometricData,
      message: 'Biometric data added successfully'
    });
  } catch (error) {
    logger.error('Error adding biometric data:', error);
    return res.status(500).json({ error: 'Failed to add biometric data' });
  }
});

// Add lab results
router.post('/:userId/lab-results', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const labResults: LabResults = req.body;
    
    // Validate lab results
    if (!labResults.id || !labResults.testDate || !labResults.testType || !labResults.results) {
      return res.status(400).json({ error: 'Missing required lab result fields' });
    }

    const updatedUser = await userService.addLabResults(userId, labResults);
    
    if (!updatedUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(201).json({
      success: true,
      data: labResults,
      message: 'Lab results added successfully'
    });
  } catch (error) {
    logger.error('Error adding lab results:', error);
    return res.status(500).json({ error: 'Failed to add lab results' });
  }
});

// Calculate health score
router.post('/:userId/calculate-health-score', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const healthScore = await userService.calculateHealthScore(userId);
    
    return res.json({
      success: true,
      data: {
        healthScore,
        calculatedAt: new Date(),
      },
      message: 'Health score calculated successfully'
    });
  } catch (error) {
    logger.error('Error calculating health score:', error);
    return res.status(500).json({ error: 'Failed to calculate health score' });
  }
});

// Get user analytics
router.get('/:userId/analytics', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Calculate additional analytics
    const analytics = {
      ...user.analytics,
      profileCompleteness: calculateProfileCompleteness(user.healthProfile),
      recentActivity: getRecentActivity(user.healthProfile),
      goalProgress: calculateGoalProgress(user.healthProfile.goals),
    };

    return res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error fetching user analytics:', error);
    return res.status(500).json({ error: 'Failed to fetch user analytics' });
  }
});

// Helper functions
function calculateProfileCompleteness(profile: HealthProfile): number {
  let completedFields = 0;
  let totalFields = 0;

  // Demographics
  totalFields += 5;
  if (profile.demographics.age > 0) completedFields++;
  if (profile.demographics.gender !== 'other') completedFields++;
  if (profile.demographics.weight > 0) completedFields++;
  if (profile.demographics.height > 0) completedFields++;
  if (profile.demographics.bmi > 0) completedFields++;

  // Lifestyle
  totalFields += 5;
  if (profile.lifestyle.exerciseFrequency > 0) completedFields++;
  if (profile.lifestyle.diet !== 'omnivore') completedFields++;
  if (profile.lifestyle.sleep.averageHours > 0) completedFields++;
  if (profile.lifestyle.stress.level > 0) completedFields++;
  if (profile.lifestyle.environment.location) completedFields++;

  // Goals
  totalFields += 1;
  if (profile.goals.length > 0) completedFields++;

  // Lab results
  totalFields += 1;
  if (profile.labResults.length > 0) completedFields++;

  // Biometric data
  totalFields += 1;
  if (profile.biometricData.length > 0) completedFields++;

  return Math.round((completedFields / totalFields) * 100);
}

function getRecentActivity(profile: HealthProfile): any {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentBiometrics = profile.biometricData.filter(
    data => new Date(data.timestamp) > thirtyDaysAgo
  );

  const recentLabs = profile.labResults.filter(
    lab => new Date(lab.testDate) > thirtyDaysAgo
  );

  return {
    biometricEntries: recentBiometrics.length,
    labResults: recentLabs.length,
    lastActivity: recentBiometrics.length > 0 
      ? Math.max(...recentBiometrics.map(b => new Date(b.timestamp).getTime()))
      : null,
  };
}

function calculateGoalProgress(goals: HealthGoal[]): any {
  const totalGoals = goals.length;
  const achievedGoals = goals.filter(g => g.status === 'achieved').length;
  const inProgressGoals = goals.filter(g => g.status === 'in_progress').length;
  const notStartedGoals = goals.filter(g => g.status === 'not_started').length;

  return {
    total: totalGoals,
    achieved: achievedGoals,
    inProgress: inProgressGoals,
    notStarted: notStartedGoals,
    completionRate: totalGoals > 0 ? Math.round((achievedGoals / totalGoals) * 100) : 0,
  };
}

export default router;
