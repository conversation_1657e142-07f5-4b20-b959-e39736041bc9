import { Router, Request, Response } from 'express';
import { performanceMonitoringService } from '../services/PerformanceMonitoringService';
import { enhancedCacheService } from '../services/EnhancedCacheService';
import { databaseConnectionManager } from '../services/DatabaseConnectionManager';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/performance/stats
 * Get performance statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const timeRange = parseInt(req.query.timeRange as string) || 3600000; // Default 1 hour
    const stats = performanceMonitoringService.getPerformanceStats(timeRange);
    
    res.json({
      success: true,
      data: stats,
      timeRange,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get performance stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance statistics'
    });
  }
});

/**
 * GET /api/performance/system
 * Get current system metrics
 */
router.get('/system', async (req: Request, res: Response) => {
  try {
    const systemMetrics = await performanceMonitoringService.getCurrentSystemMetrics();
    
    res.json({
      success: true,
      data: systemMetrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get system metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system metrics'
    });
  }
});

/**
 * GET /api/performance/cache
 * Get cache statistics and metrics
 */
router.get('/cache', async (req: Request, res: Response) => {
  try {
    const cacheStats = enhancedCacheService.getCacheStats();
    const cacheMetrics = enhancedCacheService.getCacheMetrics(100);
    
    res.json({
      success: true,
      data: {
        stats: cacheStats,
        metrics: cacheMetrics
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get cache metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve cache metrics'
    });
  }
});

/**
 * GET /api/performance/database
 * Get database connection and health information
 */
router.get('/database', async (req: Request, res: Response) => {
  try {
    const connectionStats = await databaseConnectionManager.getConnectionPoolStats();
    const databaseHealth = databaseConnectionManager.getDatabaseHealth();
    
    res.json({
      success: true,
      data: {
        connections: connectionStats,
        health: databaseHealth
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get database metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve database metrics'
    });
  }
});

/**
 * GET /api/performance/alerts
 * Get performance alerts
 */
router.get('/alerts', async (req: Request, res: Response) => {
  try {
    const resolved = req.query.resolved === 'true';
    const alerts = performanceMonitoringService.getAlerts(resolved);
    
    res.json({
      success: true,
      data: alerts,
      count: alerts.length,
      resolved,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get performance alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance alerts'
    });
  }
});

/**
 * POST /api/performance/alerts/:alertId/resolve
 * Resolve a performance alert
 */
router.post('/alerts/:alertId/resolve', async (req: Request, res: Response) => {
  try {
    const { alertId } = req.params;
    performanceMonitoringService.resolveAlert(alertId);
    
    res.json({
      success: true,
      message: 'Alert resolved successfully',
      alertId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to resolve alert:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve alert'
    });
  }
});

/**
 * GET /api/performance/report
 * Get comprehensive performance report
 */
router.get('/report', async (req: Request, res: Response) => {
  try {
    const timeRange = parseInt(req.query.timeRange as string) || 3600000; // Default 1 hour
    const report = performanceMonitoringService.getPerformanceReport(timeRange);
    
    res.json({
      success: true,
      data: report,
      timeRange,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to generate performance report:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate performance report'
    });
  }
});

/**
 * POST /api/performance/cache/clear
 * Clear all caches
 */
router.post('/cache/clear', async (req: Request, res: Response) => {
  try {
    await enhancedCacheService.clear();
    
    res.json({
      success: true,
      message: 'All caches cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to clear cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache'
    });
  }
});

/**
 * POST /api/performance/cache/invalidate
 * Invalidate cache by tags
 */
// @ts-ignore: Express route handler return type
router.post('/cache/invalidate', async (req: Request, res: Response) => {
  try {
    const { tags } = req.body;
    
    if (!tags || !Array.isArray(tags)) {
      return res.status(400).json({
        success: false,
        error: 'Tags array is required'
      });
    }
    
    await enhancedCacheService.invalidateByTags(tags);
    
    res.json({
      success: true,
      message: 'Cache invalidated successfully',
      tags,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to invalidate cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to invalidate cache'
    });
  }
});

/**
 * POST /api/performance/database/health-check
 * Trigger manual database health check
 */
router.post('/database/health-check', async (req: Request, res: Response) => {
  try {
    await databaseConnectionManager.performHealthChecks();
    const health = databaseConnectionManager.getDatabaseHealth();
    
    res.json({
      success: true,
      message: 'Health check completed',
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to perform health check:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform health check'
    });
  }
});

/**
 * GET /api/performance/metrics/export
 * Export performance metrics in various formats
 */
router.get('/metrics/export', async (req: Request, res: Response) => {
  try {
    const format = req.query.format as string || 'json';
    const timeRange = parseInt(req.query.timeRange as string) || 3600000;
    
    const report = performanceMonitoringService.getPerformanceReport(timeRange);
    const cacheStats = enhancedCacheService.getCacheStats();
    const systemMetrics = await performanceMonitoringService.getCurrentSystemMetrics();
    
    const exportData = {
      performance: report,
      cache: cacheStats,
      system: systemMetrics,
      exportTime: new Date().toISOString(),
      timeRange
    };
    
    switch (format.toLowerCase()) {
      case 'csv':
        // Convert to CSV format (simplified)
        const csvData = convertToCSV(exportData);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=performance-metrics.csv');
        res.send(csvData);
        break;
        
      case 'json':
      default:
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=performance-metrics.json');
        res.json(exportData);
        break;
    }
  } catch (error) {
    logger.error('Failed to export metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export metrics'
    });
  }
});

/**
 * GET /api/performance/dashboard
 * Get dashboard data for performance monitoring UI
 */
router.get('/dashboard', async (req: Request, res: Response) => {
  try {
    const timeRange = parseInt(req.query.timeRange as string) || 3600000;
    
    // Gather all dashboard data
    const [
      performanceStats,
      systemMetrics,
      cacheStats,
      databaseHealth,
      alerts
    ] = await Promise.all([
      performanceMonitoringService.getPerformanceStats(timeRange),
      performanceMonitoringService.getCurrentSystemMetrics(),
      enhancedCacheService.getCacheStats(),
      databaseConnectionManager.getDatabaseHealth(),
      performanceMonitoringService.getAlerts(false) // Unresolved alerts
    ]);
    
    const dashboardData = {
      overview: {
        totalRequests: performanceStats.total,
        averageResponseTime: performanceStats.averageResponseTime,
        successRate: performanceStats.successRate,
        errorRate: performanceStats.errorRate,
        activeAlerts: alerts.length
      },
      system: {
        cpu: systemMetrics.cpu,
        memory: systemMetrics.memory,
        uptime: process.uptime()
      },
      cache: {
        hitRate: cacheStats.hitRate,
        totalKeys: cacheStats.totalKeys,
        memoryUsage: cacheStats.memoryUsage
      },
      database: {
        health: databaseHealth,
        connections: await databaseConnectionManager.getConnectionPoolStats()
      },
      performance: {
        byType: performanceStats.byType,
        slowestOperations: performanceStats.slowestOperations.slice(0, 5),
        fastestOperations: performanceStats.fastestOperations.slice(0, 5)
      },
      alerts: alerts.slice(0, 10), // Latest 10 alerts
      timestamp: new Date().toISOString()
    };
    
    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Failed to get dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve dashboard data'
    });
  }
});

// Helper function to convert data to CSV (simplified)
function convertToCSV(data: any): string {
  const headers = ['timestamp', 'metric', 'value', 'type'];
  const rows = [headers.join(',')];
  
  // Add performance metrics
  if (data.performance && data.performance.byType) {
    Object.entries(data.performance.byType).forEach(([type, metrics]: [string, any]) => {
      rows.push([data.exportTime, 'averageTime', metrics.averageTime, type].join(','));
      rows.push([data.exportTime, 'successRate', metrics.successRate, type].join(','));
      rows.push([data.exportTime, 'errorRate', metrics.errorRate, type].join(','));
    });
  }
  
  // Add system metrics
  if (data.system) {
    rows.push([data.exportTime, 'memoryUsage', data.system.memory.percentage, 'system'].join(','));
    rows.push([data.exportTime, 'cpuUsage', data.system.cpu.usage, 'system'].join(','));
  }
  
  // Add cache metrics
  if (data.cache) {
    rows.push([data.exportTime, 'cacheHitRate', data.cache.hitRate, 'cache'].join(','));
    rows.push([data.exportTime, 'cacheKeys', data.cache.totalKeys, 'cache'].join(','));
  }
  
  return rows.join('\n');
}

export default router;
