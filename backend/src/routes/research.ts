import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { ResearchService, ResearchQuery } from '../services/ResearchService';
import { CrawlService, CrawlRequest } from '../services/CrawlService';
import { MedicalAIService, MedicalAnalysisRequest } from '../services/MedicalAIService';
import { logger } from '../utils/logger';

const router = Router();
const researchService = new ResearchService();
const crawlService = new CrawlService();
const medicalAIService = new MedicalAIService();

/**
 * POST /api/research/web-search
 * Perform web search using Brave/Tavily
 */
router.post('/web-search', [
  body('query').isString().isLength({ min: 1, max: 500 }),
  body('type').isIn(['web_search', 'academic', 'news', 'supplement_specific']),
  body('filters.maxResults').optional().isInt({ min: 1, max: 50 }),
  body('filters.timeRange').optional().isIn(['day', 'week', 'month', 'year']),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const researchQuery: ResearchQuery = {
      query: req.body.query,
      type: req.body.type || 'web_search',
      filters: req.body.filters || {},
      context: req.body.context
    };

    let results;
    
    // Choose search method based on type
    switch (researchQuery.type) {
      case 'supplement_specific':
        results = await researchService.searchSupplements(researchQuery);
        break;
      case 'academic':
        results = await researchService.searchTavily(researchQuery);
        break;
      default:
        results = await researchService.comprehensiveSearch(researchQuery);
    }

    return res.json({
      success: true,
      data: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Web search failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Search failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/research/crawl-website
 * Crawl website content using Crawl4AI
 */
router.post('/crawl-website', [
  body('url').isURL(),
  body('type').isIn(['single_page', 'recursive', 'sitemap', 'llms_txt']),
  body('options.maxDepth').optional().isInt({ min: 1, max: 5 }),
  body('options.maxPages').optional().isInt({ min: 1, max: 100 }),
  body('options.chunkSize').optional().isInt({ min: 100, max: 5000 }),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const crawlRequest: CrawlRequest = {
      url: req.body.url,
      type: req.body.type,
      options: req.body.options || {}
    };

    let results;

    switch (crawlRequest.type) {
      case 'single_page':
        results = await crawlService.crawlSinglePage(crawlRequest);
        break;
      case 'recursive':
        results = await crawlService.crawlRecursive(crawlRequest);
        break;
      case 'sitemap':
        results = await crawlService.crawlSitemap(crawlRequest);
        break;
      case 'llms_txt':
        results = await crawlService.crawlMarkdown(crawlRequest);
        break;
      default:
        results = await crawlService.crawlSinglePage(crawlRequest);
    }

    return res.json({
      success: true,
      data: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Website crawl failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Crawl failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/research/medical-analysis
 * Analyze content using Gemma3-4B-Medical
 */
router.post('/medical-analysis', [
  body('text').isString().isLength({ min: 10, max: 10000 }),
  body('analysisType').isIn(['supplement', 'interaction', 'side_effects', 'dosage', 'contraindications']),
  body('context').optional().isString(),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const analysisRequest: MedicalAnalysisRequest = {
      text: req.body.text,
      analysisType: req.body.analysisType,
      context: req.body.context
    };

    const analysis = await medicalAIService.analyzeSupplement(analysisRequest);

    return res.json({
      success: true,
      data: analysis,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Medical analysis failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/research/analyze-interactions
 * Analyze drug/supplement interactions
 */
router.post('/analyze-interactions', [
  body('substances').isArray({ min: 2, max: 10 }),
  body('substances.*').isString().isLength({ min: 1, max: 100 }),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const substances: string[] = req.body.substances;
    const interactions = await medicalAIService.analyzeInteractions(substances);

    return res.json({
      success: true,
      data: {
        substances,
        interactions,
        analysisTime: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Interaction analysis failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Interaction analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/research/comprehensive-research
 * Perform comprehensive research: search + crawl + analyze
 */
router.post('/comprehensive-research', [
  body('query').isString().isLength({ min: 1, max: 500 }),
  body('includeAnalysis').optional().isBoolean(),
  body('maxResults').optional().isInt({ min: 1, max: 20 }),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const query = req.body.query;
    const includeAnalysis = req.body.includeAnalysis || true;
    const maxResults = req.body.maxResults || 10;

    // Step 1: Web search
    const searchResults = await researchService.searchSupplements({
      query,
      type: 'supplement_specific',
      filters: { maxResults }
    });

    // Step 2: Crawl top results
    const crawlPromises = searchResults.results.slice(0, 3).map(result =>
      crawlService.crawlSinglePage({
        url: result.url,
        type: 'single_page',
        options: { chunkSize: 1000 }
      }).catch((error: any): null => {
        logger.warn(`Failed to crawl ${result.url}:`, error);
        return null;
      })
    );

    const crawlResults = (await Promise.all(crawlPromises)).filter(Boolean);

    // Step 3: Medical analysis (if requested)
    let medicalAnalysis = null;
    if (includeAnalysis && crawlResults.length > 0) {
      const combinedContent = crawlResults
        .map(result => result?.content || '')
        .join('\n\n')
        .substring(0, 8000); // Limit content size

      if (combinedContent.length > 50) {
        medicalAnalysis = await medicalAIService.analyzeSupplement({
          text: combinedContent,
          analysisType: 'supplement',
          context: `Research query: ${query}`
        }).catch((error: any): null => {
          logger.warn('Medical analysis failed:', error);
          return null;
        });
      }
    }

    return res.json({
      success: true,
      data: {
        query,
        searchResults,
        crawlResults,
        medicalAnalysis,
        summary: {
          totalSearchResults: searchResults.totalResults,
          crawledPages: crawlResults.length,
          analysisIncluded: !!medicalAnalysis
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Comprehensive research failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Research failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/research/crawl-progress/:crawlId
 * Get crawl progress
 */
router.get('/crawl-progress/:crawlId', async (req: Request, res: Response) => {
  try {
    const crawlId = req.params['crawlId'];
    const progress = await crawlService.getCrawlProgress(crawlId || '');

    return res.json({
      success: true,
      data: progress
    });

  } catch (error) {
    logger.error('Failed to get crawl progress:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get progress'
    });
  }
});

/**
 * DELETE /api/research/crawl/:crawlId
 * Cancel active crawl
 */
router.delete('/crawl/:crawlId', async (req: Request, res: Response) => {
  try {
    const crawlId = req.params['crawlId'];
    const cancelled = await crawlService.cancelCrawl(crawlId || '');

    return res.json({
      success: true,
      data: { cancelled }
    });

  } catch (error) {
    logger.error('Failed to cancel crawl:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to cancel crawl'
    });
  }
});

/**
 * GET /api/research/health
 * Health check for research services
 */
router.get('/health', async (_req: Request, res: Response) => {
  try {
    const [searchHealth, crawlHealth, medicalHealth] = await Promise.all([
      researchService.healthCheck(),
      crawlService.healthCheck(),
      medicalAIService.healthCheck()
    ]);

    const overallHealth = searchHealth.brave && searchHealth.tavily && crawlHealth && medicalHealth;

    return res.json({
      success: true,
      data: {
        overall: overallHealth,
        services: {
          search: searchHealth,
          crawl: crawlHealth,
          medicalAI: medicalHealth
        }
      }
    });

  } catch (error) {
    logger.error('Health check failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Health check failed'
    });
  }
});

export default router;
