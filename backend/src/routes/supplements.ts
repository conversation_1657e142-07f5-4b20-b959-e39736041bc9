import { Router, Request, Response } from 'express';
import { Driver } from 'neo4j-driver';
import { createClient, RedisClientType } from 'redis';
import { SupplementService, SearchQuery, Supplement } from '../services/SupplementService';
import { InteractionService } from '../services/InteractionService';
import { BudgetOptimizationService, OptimizationCriteria } from '../services/BudgetOptimizationService';
import { UserService } from '../services/UserService';
import { logger } from '../utils/logger';

const router = Router();

// Initialize services (this would typically be done via dependency injection)
let supplementService: SupplementService;
let interactionService: InteractionService;
let budgetOptimizationService: BudgetOptimizationService;

// Middleware to initialize services
router.use(async (req: Request, res: Response, next) => {
  if (!supplementService) {
    try {
      // Initialize Neo4j driver
      const neo4j = require('neo4j-driver');
      const neo4jDriver = neo4j.driver(
        process.env.NEO4J_URI || 'bolt://localhost:7687',
        neo4j.auth.basic(
          process.env.NEO4J_USER || 'neo4j',
          process.env.NEO4J_PASSWORD || 'password'
        )
      );

      // Initialize Redis client
      const redisClient: RedisClientType = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });
      await redisClient.connect();

      supplementService = new SupplementService(neo4jDriver, redisClient);
      interactionService = new InteractionService(neo4jDriver, redisClient);
      budgetOptimizationService = new BudgetOptimizationService();
    } catch (error) {
      logger.error('Failed to initialize services:', error);
      return res.status(500).json({ error: 'Service initialization failed' });
    }
  }
  return next();
});

// Search supplements
router.get('/search', async (req: Request, res: Response) => {
  try {
    const query: SearchQuery = {
      term: req.query.term as string,
      category: req.query.category as string,
      effects: req.query.effects ? (req.query.effects as string).split(',') : undefined,
      priceRange: req.query.minPrice && req.query.maxPrice ? {
        min: parseFloat(req.query.minPrice as string),
        max: parseFloat(req.query.maxPrice as string)
      } : undefined,
      safetyRating: req.query.safetyRating ? parseFloat(req.query.safetyRating as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0,
    };

    const supplements = await supplementService.searchSupplements(query);

    return res.json({
      success: true,
      data: supplements,
      pagination: {
        limit: query.limit,
        offset: query.offset,
        total: supplements.length,
      }
    });
  } catch (error) {
    logger.error('Error searching supplements:', error);
    return res.status(500).json({ error: 'Failed to search supplements' });
  }
});

// Get supplement by ID
router.get('/:supplementId', async (req: Request, res: Response) => {
  try {
    const { supplementId } = req.params;
    const supplement = await supplementService.getSupplementById(supplementId);

    if (!supplement) {
      return res.status(404).json({ error: 'Supplement not found' });
    }

    return res.json({
      success: true,
      data: supplement
    });
  } catch (error) {
    logger.error('Error fetching supplement:', error);
    return res.status(500).json({ error: 'Failed to fetch supplement' });
  }
});

// Get supplements by effect
router.get('/effects/:effectName', async (req: Request, res: Response) => {
  try {
    const { effectName } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

    const supplements = await supplementService.getSupplementsByEffect(effectName, limit);

    return res.json({
      success: true,
      data: supplements,
      meta: {
        effect: effectName,
        count: supplements.length,
      }
    });
  } catch (error) {
    logger.error('Error fetching supplements by effect:', error);
    return res.status(500).json({ error: 'Failed to fetch supplements by effect' });
  }
});

// Analyze supplement interactions
router.post('/analyze-interactions', async (req: Request, res: Response) => {
  try {
    const { supplementIds } = req.body;

    if (!Array.isArray(supplementIds) || supplementIds.length === 0) {
      return res.status(400).json({ error: 'Invalid supplement IDs provided' });
    }

    const interactions = await supplementService.getSupplementInteractions(supplementIds);

    return res.json({
      success: true,
      data: interactions,
      meta: {
        supplementCount: supplementIds.length,
        interactionCount: interactions.length,
      }
    });
  } catch (error) {
    logger.error('Error analyzing interactions:', error);
    return res.status(500).json({ error: 'Failed to analyze interactions' });
  }
});

// Comprehensive safety analysis
router.post('/safety-analysis', async (req: Request, res: Response) => {
  try {
    const { supplements, healthProfile } = req.body;

    if (!Array.isArray(supplements) || !healthProfile) {
      return res.status(400).json({ error: 'Invalid request data' });
    }

    const safetyAnalysis = await interactionService.analyzeSupplementStack(supplements, healthProfile);

    return res.json({
      success: true,
      data: safetyAnalysis,
      meta: {
        supplementCount: supplements.length,
        analysisTimestamp: new Date(),
      }
    });
  } catch (error) {
    logger.error('Error performing safety analysis:', error);
    return res.status(500).json({ error: 'Failed to perform safety analysis' });
  }
});

// Optimize supplement stack
router.post('/optimize-stack', async (req: Request, res: Response) => {
  try {
    const { availableSupplements, criteria, healthProfile, safetyAnalysis } = req.body;

    if (!Array.isArray(availableSupplements) || !criteria || !healthProfile) {
      return res.status(400).json({ error: 'Invalid optimization request data' });
    }

    const optimizedStack = await budgetOptimizationService.optimizeSupplementStack(
      availableSupplements,
      criteria as OptimizationCriteria,
      healthProfile,
      safetyAnalysis
    );

    return res.json({
      success: true,
      data: optimizedStack,
      meta: {
        optimizationTimestamp: new Date(),
        inputSupplementCount: availableSupplements.length,
        selectedSupplementCount: optimizedStack.supplements.length,
      }
    });
  } catch (error) {
    logger.error('Error optimizing supplement stack:', error);
    return res.status(500).json({ error: 'Failed to optimize supplement stack' });
  }
});

// Get personalized recommendations
router.post('/recommendations/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { preferences } = req.body;

    // This would integrate with UserService to get user profile
    // and generate personalized recommendations
    
    // For now, return a placeholder response
    const recommendations = {
      primary: [] as Supplement[],
      alternatives: [] as Supplement[],
      reasoning: [
        'Based on your health goals and profile',
        'Optimized for safety and effectiveness',
        'Considers your budget constraints',
      ],
    };

    return res.json({
      success: true,
      data: recommendations,
      meta: {
        userId,
        generatedAt: new Date(),
      }
    });
  } catch (error) {
    logger.error('Error generating recommendations:', error);
    return res.status(500).json({ error: 'Failed to generate recommendations' });
  }
});

// Get supplement categories
router.get('/categories', async (req: Request, res: Response) => {
  try {
    // This would query Neo4j for available categories
    const categories = [
      { id: 'vitamins', name: 'Vitamins', count: 150 },
      { id: 'minerals', name: 'Minerals', count: 85 },
      { id: 'herbs', name: 'Herbs & Botanicals', count: 200 },
      { id: 'amino-acids', name: 'Amino Acids', count: 45 },
      { id: 'probiotics', name: 'Probiotics', count: 30 },
      { id: 'omega-3', name: 'Omega-3 Fatty Acids', count: 25 },
      { id: 'antioxidants', name: 'Antioxidants', count: 60 },
      { id: 'adaptogens', name: 'Adaptogens', count: 40 },
    ];

    return res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    logger.error('Error fetching categories:', error);
    return res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get popular supplements
router.get('/popular', async (req: Request, res: Response) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    
    // This would query Neo4j for supplements ordered by popularity
    const query: SearchQuery = {
      term: '',
      limit,
      offset: 0,
    };

    const supplements = await supplementService.searchSupplements(query);

    return res.json({
      success: true,
      data: supplements.slice(0, limit),
      meta: {
        type: 'popular',
        count: supplements.length,
      }
    });
  } catch (error) {
    logger.error('Error fetching popular supplements:', error);
    return res.status(500).json({ error: 'Failed to fetch popular supplements' });
  }
});

// Get supplement trends
router.get('/trends', async (req: Request, res: Response) => {
  try {
    // This would analyze recent search patterns and supplement popularity
    const trends = {
      trending: [
        { name: 'Vitamin D3', change: '+15%', category: 'vitamins' },
        { name: 'Magnesium Glycinate', change: '+12%', category: 'minerals' },
        { name: 'Ashwagandha', change: '+8%', category: 'adaptogens' },
      ],
      seasonal: [
        { name: 'Immune Support', reason: 'Winter season' },
        { name: 'Mood Support', reason: 'Seasonal changes' },
      ],
      research: [
        { name: 'NMN', reason: 'New longevity research' },
        { name: 'Lion\'s Mane', reason: 'Cognitive health studies' },
      ],
    };

    return res.json({
      success: true,
      data: trends,
      meta: {
        generatedAt: new Date(),
        period: 'last_30_days',
      }
    });
  } catch (error) {
    logger.error('Error fetching trends:', error);
    return res.status(500).json({ error: 'Failed to fetch trends' });
  }
});

export default router;
