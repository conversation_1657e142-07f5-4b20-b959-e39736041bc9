import { Request, Response, NextFunction } from 'express';
import { performanceMonitoringService } from '../services/PerformanceMonitoringService';
import { logger } from '../utils/logger';

export interface PerformanceRequest extends Request {
  performanceMetricId?: string;
  startTime?: number;
}

/**
 * Middleware to automatically track API performance
 */
export const performanceMiddleware = (req: PerformanceRequest, res: Response, next: NextFunction): void => {
  // Start tracking this request
  const metricId = performanceMonitoringService.trackApiRequest(req.path, req.method);
  req.performanceMetricId = metricId;
  req.startTime = Date.now();

  // Track response
  const originalSend = res.send;
  res.send = function(data) {
    if (req.performanceMetricId) {
      const success = res.statusCode < 400;
      const error = success ? undefined : `HTTP ${res.statusCode}`;
      
      performanceMonitoringService.endMetric(req.performanceMetricId, success, error, {
        statusCode: res.statusCode,
        responseSize: data ? data.length : 0,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    return originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware to track database operations
 */
export const databasePerformanceWrapper = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  operationName: string,
  database: string = 'unknown'
) => {
  return async (...args: T): Promise<R> => {
    const metricId = performanceMonitoringService.trackDatabaseQuery(operationName, database);
    
    try {
      const result = await operation(...args);
      performanceMonitoringService.endMetric(metricId, true, undefined, {
        operationName,
        database,
        argsCount: args.length
      });
      return result;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
        operationName,
        database,
        argsCount: args.length
      });
      throw error;
    }
  };
};

/**
 * Middleware to track cache operations
 */
export const cachePerformanceWrapper = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  operationName: string
) => {
  return async (...args: T): Promise<R> => {
    const key = args.length > 0 ? String(args[0]) : 'unknown';
    const metricId = performanceMonitoringService.trackCacheOperation(operationName, key);
    
    try {
      const result = await operation(...args);
      performanceMonitoringService.endMetric(metricId, true, undefined, {
        operationName,
        key,
        resultSize: result ? JSON.stringify(result).length : 0
      });
      return result;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
        operationName,
        key
      });
      throw error;
    }
  };
};

/**
 * Middleware to track AI operations
 */
export const aiPerformanceWrapper = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  operationName: string,
  model?: string
) => {
  return async (...args: T): Promise<R> => {
    const metricId = performanceMonitoringService.trackAIOperation(operationName, model);
    
    try {
      const result = await operation(...args);
      performanceMonitoringService.endMetric(metricId, true, undefined, {
        operationName,
        model,
        argsCount: args.length,
        resultSize: result ? JSON.stringify(result).length : 0
      });
      return result;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
        operationName,
        model
      });
      throw error;
    }
  };
};

/**
 * Middleware to track external API calls
 */
export const externalApiPerformanceWrapper = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  serviceName: string,
  endpoint: string
) => {
  return async (...args: T): Promise<R> => {
    const metricId = performanceMonitoringService.trackExternalAPI(serviceName, endpoint);
    
    try {
      const result = await operation(...args);
      performanceMonitoringService.endMetric(metricId, true, undefined, {
        serviceName,
        endpoint,
        argsCount: args.length,
        resultSize: result ? JSON.stringify(result).length : 0
      });
      return result;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
        serviceName,
        endpoint
      });
      throw error;
    }
  };
};

/**
 * Decorator for automatic performance tracking
 */
export function trackPerformance(
  type: 'database' | 'cache' | 'ai' | 'external',
  name: string,
  additionalInfo?: string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let metricId: string;

      switch (type) {
        case 'database':
          metricId = performanceMonitoringService.trackDatabaseQuery(name, additionalInfo || 'unknown');
          break;
        case 'cache':
          metricId = performanceMonitoringService.trackCacheOperation(name, args[0] || 'unknown');
          break;
        case 'ai':
          metricId = performanceMonitoringService.trackAIOperation(name, additionalInfo);
          break;
        case 'external':
          metricId = performanceMonitoringService.trackExternalAPI(name, additionalInfo || 'unknown');
          break;
        default:
          throw new Error(`Unknown performance tracking type: ${type}`);
      }

      try {
        const result = await originalMethod.apply(this, args);
        performanceMonitoringService.endMetric(metricId, true, undefined, {
          method: propertyKey,
          type,
          name,
          additionalInfo,
          argsCount: args.length
        });
        return result;
      } catch (error) {
        performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
          method: propertyKey,
          type,
          name,
          additionalInfo,
          argsCount: args.length
        });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Middleware for request rate limiting with performance tracking
 */
export const rateLimitWithPerformance = (windowMs: number, max: number) => {
  const requests = new Map<string, number[]>();

  return (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old requests
    if (requests.has(key)) {
      const userRequests = requests.get(key)!.filter(time => time > windowStart);
      requests.set(key, userRequests);
    } else {
      requests.set(key, []);
    }

    const userRequests = requests.get(key)!;

    if (userRequests.length >= max) {
      // Rate limit exceeded
      const metricId = performanceMonitoringService.trackApiRequest(req.path, req.method);
      performanceMonitoringService.endMetric(metricId, false, 'Rate limit exceeded', {
        statusCode: 429,
        ip: req.ip,
        requestCount: userRequests.length
      });

      res.status(429).json({
        error: 'Too many requests',
        retryAfter: Math.ceil(windowMs / 1000)
      });
      return;
    }

    // Add current request
    userRequests.push(now);
    requests.set(key, userRequests);

    next();
  };
};

/**
 * Middleware for request compression with performance tracking
 */
export const compressionWithPerformance = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const originalSize = data ? data.length : 0;
      
      // Simple compression simulation (in real implementation, use actual compression)
      const compressionRatio = req.get('Accept-Encoding')?.includes('gzip') ? 0.7 : 1.0;
      const compressedSize = Math.floor(originalSize * compressionRatio);
      
      // Track compression performance
      if (originalSize > 1024) { // Only track for responses > 1KB
        logger.debug('Response compression', {
          path: req.path,
          originalSize,
          compressedSize,
          compressionRatio: (1 - compressionRatio) * 100
        });
      }

      return originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Middleware for memory usage monitoring
 */
export const memoryMonitoringMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const memBefore = process.memoryUsage();
  
  res.on('finish', () => {
    const memAfter = process.memoryUsage();
    const memDiff = {
      rss: memAfter.rss - memBefore.rss,
      heapUsed: memAfter.heapUsed - memBefore.heapUsed,
      heapTotal: memAfter.heapTotal - memBefore.heapTotal,
      external: memAfter.external - memBefore.external
    };

    // Log significant memory changes
    if (Math.abs(memDiff.heapUsed) > 10 * 1024 * 1024) { // 10MB threshold
      logger.warn('Significant memory change detected', {
        path: req.path,
        method: req.method,
        memoryDiff: memDiff,
        statusCode: res.statusCode
      });
    }
  });

  next();
};

/**
 * Error handling middleware with performance tracking
 */
export const errorTrackingMiddleware = (error: Error, req: PerformanceRequest, res: Response, next: NextFunction) => {
  // Track the error in performance metrics
  if (req.performanceMetricId) {
    performanceMonitoringService.endMetric(req.performanceMetricId, false, error.message, {
      errorName: error.name,
      errorStack: error.stack?.substring(0, 500), // Truncate stack trace
      statusCode: res.statusCode || 500
    });
  }

  // Log the error
  logger.error('Request error', {
    path: req.path,
    method: req.method,
    error: error.message,
    stack: error.stack,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Send error response
  if (!res.headersSent) {
    res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }

  next(error);
};

/**
 * Health check middleware with performance metrics
 */
export const healthCheckMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  if (req.path === '/health' || req.path === '/api/health') {
    const metricId = performanceMonitoringService.trackApiRequest('health-check', 'GET');
    
    try {
      const systemMetrics = await performanceMonitoringService.getCurrentSystemMetrics();
      const performanceStats = performanceMonitoringService.getPerformanceStats(300000); // Last 5 minutes
      const alerts = performanceMonitoringService.getAlerts(false); // Unresolved alerts

      const healthStatus = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
        system: {
          memory: {
            usage: `${(systemMetrics.memory.percentage).toFixed(1)}%`,
            used: `${(systemMetrics.memory.used / 1024 / 1024 / 1024).toFixed(2)}GB`,
            total: `${(systemMetrics.memory.total / 1024 / 1024 / 1024).toFixed(2)}GB`
          },
          cpu: {
            usage: `${systemMetrics.cpu.usage.toFixed(1)}%`,
            loadAverage: systemMetrics.cpu.loadAverage.map(load => load.toFixed(2))
          },
          cache: {
            hitRate: `${systemMetrics.cache.hitRate.toFixed(1)}%`,
            keys: systemMetrics.cache.totalKeys
          }
        },
        performance: {
          requestsPerMinute: systemMetrics.api.requestsPerMinute,
          averageResponseTime: `${performanceStats.averageResponseTime.toFixed(2)}ms`,
          successRate: `${performanceStats.successRate.toFixed(1)}%`,
          errorRate: `${performanceStats.errorRate.toFixed(1)}%`
        },
        alerts: alerts.length
      };

      // Determine overall health status
      if (alerts.some(a => a.type === 'critical')) {
        healthStatus.status = 'critical';
      } else if (alerts.length > 0 || systemMetrics.memory.percentage > 90 || systemMetrics.cpu.usage > 90) {
        healthStatus.status = 'warning';
      }

      performanceMonitoringService.endMetric(metricId, true, undefined, {
        healthStatus: healthStatus.status,
        alertCount: alerts.length
      });

      res.status(healthStatus.status === 'critical' ? 503 : 200).json(healthStatus);
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Health check failed');
      
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      });
    }
  } else {
    next();
  }
};
