import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { createServer } from 'http';

// Import configurations and middleware
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { requestLogger } from '@/middleware/requestLogger';

// Import enhanced performance services
import { enhancedCacheService } from '@/services/EnhancedCacheService';
import { performanceMonitoringService } from '@/services/PerformanceMonitoringService';
import { databaseConnectionManager } from '@/services/DatabaseConnectionManager';
import {
  performanceMiddleware,
  healthCheckMiddleware,
  memoryMonitoringMiddleware,
  errorTrackingMiddleware,
  compressionWithPerformance,
  rateLimitWithPerformance
} from '@/middleware/performanceMiddleware';

// Import database connections (legacy - will be replaced by DatabaseConnectionManager)
import { connectNeo4j } from '@/config/neo4j';
import { connectWeaviate } from '@/config/weaviate';
import { connectRedis } from '@/config/redis';
import { connectMongoDB } from '@/config/mongodb';

// Import routes
import { graphRoutes } from '@/routes/graph';
import { ragRoutes } from '@/routes/rag';
import { aiRoutes } from '@/routes/ai';
import { healthRoutes } from '@/routes/health';
import { uploadRoutes } from '@/routes/upload';
import researchRoutes from '@/routes/research';
import researchAIRoutes from '@/routes/researchAI';
import aguiRoutes, { initializeAGUIWebSocket } from '@/routes/agui';
import crawlRoutes from '@/routes/crawl';
import userProfileRoutes from '@/routes/userProfile';
import crewaiRoutes from '@/routes/crewai';
import enhancedResearchRoutes from '@/routes/enhancedResearch';
import oracleRoutes from '@/routes/oracle';
import monitoringRoutes from '@/routes/monitoring';
import performanceRoutes from '@/routes/performance';
import realtimeRoutes from '@/routes/realtime';
import enhancedAiRoutes from '@/routes/enhanced-ai';
import medicalResearchRoutes from '@/routes/medical-research';
import { GraphWebSocketService } from '@/services/GraphWebSocketService';
import EnhancedWebSocketService from '@/services/EnhancedWebSocketService';
import RealTimeResearchOrchestrator from '@/services/RealTimeResearchOrchestrator';
import CollaborativeWorkspaceManager from '@/services/CollaborativeWorkspaceManager';

// Load environment variables
dotenv.config();

class Application {
  public app: express.Application;
  public server: any;
  private port: number;
  private graphWebSocketService: GraphWebSocketService;
  private enhancedWebSocketService?: EnhancedWebSocketService;
  private realTimeOrchestrator?: RealTimeResearchOrchestrator;
  private collaborativeWorkspaceManager?: CollaborativeWorkspaceManager;

  constructor() {
    this.app = express();
    this.port = config.port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.corsOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Enhanced rate limiting with performance tracking
    this.app.use('/api/', rateLimitWithPerformance(15 * 60 * 1000, config.isDevelopment ? 1000 : 100));

    // Body parsing middleware
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Enhanced compression with performance tracking
    this.app.use(compressionWithPerformance());

    // Performance monitoring middleware (must be early in the chain)
    this.app.use(performanceMiddleware);

    // Memory monitoring middleware
    this.app.use(memoryMonitoringMiddleware);

    // Logging middleware
    if (config.isDevelopment) {
      this.app.use(morgan('dev'));
    } else {
      this.app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
    }

    // Custom request logging
    this.app.use(requestLogger);

    // Enhanced health check endpoint with performance metrics
    this.app.use(healthCheckMiddleware);
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/graph', graphRoutes);
    this.app.use('/api/rag', ragRoutes);
    this.app.use('/api/ai', aiRoutes);
    this.app.use('/api/upload', uploadRoutes);
    this.app.use('/api/research', researchRoutes);
    this.app.use('/api/research-ai', researchAIRoutes);
    this.app.use('/api/enhanced-research', enhancedResearchRoutes);
    this.app.use('/api/oracle', oracleRoutes);
    this.app.use('/api/monitoring', monitoringRoutes);
    this.app.use('/api/performance', performanceRoutes);
    this.app.use('/api/realtime', realtimeRoutes);
    this.app.use('/api/enhanced-ai', enhancedAiRoutes);
    this.app.use('/api/medical-research', medicalResearchRoutes);
    this.app.use('/api/crawl', crawlRoutes);
    this.app.use('/api/profile', userProfileRoutes);
    this.app.use('/api/crewai', crewaiRoutes);
    this.app.use('/agui', aguiRoutes);

    // API documentation
    this.app.get('/api', (_req, res) => {
      res.json({
        name: 'Suplementor Knowledge Graph API',
        version: '1.0.0',
        description: 'API for managing supplement knowledge graphs with AI integration',
        endpoints: {
          health: '/api/health',
          graph: '/api/graph',
          rag: '/api/rag',
          ai: '/api/ai',
          upload: '/api/upload',
          research: '/api/research',
          researchAI: '/api/research-ai',
          enhancedResearch: '/api/enhanced-research',
          oracle: '/api/oracle',
          monitoring: '/api/monitoring',
          performance: '/api/performance',
          realtime: '/api/realtime',
          enhancedAi: '/api/enhanced-ai',
          medicalResearch: '/api/medical-research',
          profile: '/api/profile',
          crewai: '/api/crewai',
          agui: '/agui',
        },
        websockets: {
          agui: '/agui/ws',
        },
        documentation: '/api/docs',
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Enhanced error tracking middleware
    this.app.use(errorTrackingMiddleware);

    // Global error handler
    this.app.use(errorHandler);
  }

  private async initializeDatabases(): Promise<void> {
    try {
      logger.info('🚀 Initializing enhanced database connections with pooling...');

      // Initialize enhanced database connection manager
      await databaseConnectionManager.initializeConnections();

      // Initialize enhanced cache service
      logger.info('🔄 Initializing enhanced cache service...');
      // Cache service is already initialized in constructor

      logger.info('📊 Starting performance monitoring...');
      // Performance monitoring service is already initialized

      // Warm up cache with frequently accessed data
      await this.warmUpCache();

      logger.info('✅ All enhanced database services initialized successfully');
    } catch (error) {
      logger.error('❌ Enhanced database initialization failed:', error);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      // Initialize database connections
      await this.initializeDatabases();

      // Start HTTP server
      this.server = createServer(this.app);

      // Initialize AG-UI WebSocket server
      initializeAGUIWebSocket(this.server);

      // Initialize WebSocket services
      this.graphWebSocketService = new GraphWebSocketService(this.server);
      logger.info('🔌 Graph WebSocket service initialized');

      // Initialize Enhanced WebSocket service for real-time collaboration
      this.enhancedWebSocketService = new EnhancedWebSocketService(this.server);
      logger.info('🚀 Enhanced WebSocket service initialized');

      // Initialize Collaborative Workspace Manager
      this.collaborativeWorkspaceManager = new CollaborativeWorkspaceManager();
      logger.info('🤝 Collaborative Workspace Manager initialized');

      // Initialize Real-Time Research Orchestrator
      const { MCPResearchOrchestrator } = await import('@/services/MCPResearchOrchestrator');
      const ResearchAIServiceModule = await import('@/services/ResearchAIService');
      const mcpOrchestrator = new MCPResearchOrchestrator();
      const aiService = new ResearchAIServiceModule.default();
      this.realTimeOrchestrator = new RealTimeResearchOrchestrator(mcpOrchestrator, aiService);
      logger.info('🤖 Real-Time Research Orchestrator initialized');

      this.server.listen(this.port, () => {
        logger.info(`🚀 Server running on port ${this.port}`);
        logger.info(`📊 Environment: ${config.nodeEnv}`);
        logger.info(`🔗 API URL: http://localhost:${this.port}/api`);
        logger.info(`🔌 AG-UI WebSocket: ws://localhost:${this.port}/agui/ws`);
        logger.info(`💚 Health check: http://localhost:${this.port}/health`);
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  private async warmUpCache(): Promise<void> {
    try {
      logger.info('🔥 Warming up cache with frequently accessed data...');

      // Example cache warmup data - customize based on your application needs
      const warmupData = [
        {
          key: 'system:config',
          data: { version: '1.0.0', features: ['ai', 'research', 'graph'] },
          options: { ttl: 3600, priority: 'high' as const, tags: ['system'] }
        },
        {
          key: 'research:popular_supplements',
          data: ['Vitamin D', 'Magnesium', 'Omega-3', 'Vitamin B12', 'Zinc'],
          options: { ttl: 1800, priority: 'medium' as const, tags: ['research'] }
        }
      ];

      await enhancedCacheService.warmCache(warmupData);
      logger.info('✅ Cache warmup completed');
    } catch (error) {
      logger.warn('⚠️ Cache warmup failed:', error);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`📴 Received ${signal}. Starting enhanced graceful shutdown...`);

      if (this.server) {
        this.server.close(async () => {
          logger.info('🔌 HTTP server closed');

          try {
            // Close enhanced database connections
            await databaseConnectionManager.shutdown();
            logger.info('🔌 Enhanced database connections closed');

            // Shutdown enhanced cache service
            await enhancedCacheService.shutdown();
            logger.info('🔌 Enhanced cache service closed');

            // Close WebSocket services
            if (this.graphWebSocketService) {
              // WebSocket service cleanup would go here
              logger.info('🔌 Graph WebSocket service closed');
            }

            if (this.enhancedWebSocketService) {
              // Enhanced WebSocket service cleanup would go here
              logger.info('🔌 Enhanced WebSocket service closed');
            }

            // Shutdown Real-Time Research Orchestrator
            if (this.realTimeOrchestrator) {
              this.realTimeOrchestrator.shutdown();
              logger.info('🤖 Real-Time Research Orchestrator closed');
            }

            // Shutdown Collaborative Workspace Manager
            if (this.collaborativeWorkspaceManager) {
              // Workspace manager cleanup would go here
              logger.info('🤝 Collaborative Workspace Manager closed');
            }

            logger.info('✅ Enhanced graceful shutdown completed');
            process.exit(0);
          } catch (error) {
            logger.error('❌ Error during enhanced graceful shutdown:', error);
            process.exit(1);
          }
        });
      } else {
        process.exit(0);
      }
    };

    // Handle different termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('❌ Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }
}

// Create and start the application
const app = new Application();

// Start the server
app.start().catch((error) => {
  logger.error('❌ Failed to start application:', error);
  process.exit(1);
});

export default app;
