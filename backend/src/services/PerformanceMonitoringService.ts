import { performance } from 'perf_hooks';
import { logger } from '../utils/logger';
import { enhancedCacheService } from './EnhancedCacheService';

export interface PerformanceMetric {
  id: string;
  name: string;
  type: 'api' | 'database' | 'cache' | 'ai' | 'external';
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    free: number;
    total: number;
    percentage: number;
  };
  cache: {
    hitRate: number;
    totalKeys: number;
    memoryUsage: number;
  };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
  };
  database: {
    activeConnections: number;
    queryTime: number;
    errorRate: number;
  };
  timestamp: Date;
}

export interface PerformanceAlert {
  id: string;
  type: 'warning' | 'critical';
  metric: string;
  threshold: number;
  currentValue: number;
  message: string;
  timestamp: Date;
  resolved?: boolean;
}

/**
 * Advanced performance monitoring service
 */
export class PerformanceMonitoringService {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private completedMetrics: PerformanceMetric[] = [];
  private systemMetricsHistory: SystemMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private thresholds: Record<string, number> = {
    apiResponseTime: 1000, // 1 second
    memoryUsage: 80, // 80%
    cpuUsage: 80, // 80%
    errorRate: 5, // 5%
    cacheHitRate: 70, // 70%
    dbQueryTime: 500 // 500ms
  };

  constructor() {
    this.startSystemMonitoring();
    this.startAlertMonitoring();
  }

  /**
   * Start tracking a performance metric
   */
  startMetric(name: string, type: PerformanceMetric['type'], metadata?: Record<string, any>): string {
    const id = `${type}_${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const metric: PerformanceMetric = {
      id,
      name,
      type,
      startTime: performance.now(),
      success: false,
      metadata,
      timestamp: new Date()
    };

    this.metrics.set(id, metric);
    return id;
  }

  /**
   * End tracking a performance metric
   */
  endMetric(id: string, success: boolean = true, error?: string, additionalMetadata?: Record<string, any>): void {
    const metric = this.metrics.get(id);
    if (!metric) {
      logger.warn(`Performance metric not found: ${id}`);
      return;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    metric.error = error;
    
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    // Move to completed metrics
    this.completedMetrics.push(metric);
    this.metrics.delete(id);

    // Keep only last 10000 completed metrics
    if (this.completedMetrics.length > 10000) {
      this.completedMetrics = this.completedMetrics.slice(-10000);
    }

    // Check for performance alerts
    this.checkPerformanceThresholds(metric);

    logger.debug(`Performance metric completed: ${metric.name} (${metric.duration?.toFixed(2)}ms)`);
  }

  /**
   * Track API request performance
   */
  trackApiRequest(endpoint: string, method: string): string {
    return this.startMetric(`${method} ${endpoint}`, 'api', { endpoint, method });
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(query: string, database: string): string {
    return this.startMetric(`${database} query`, 'database', { 
      query: query.substring(0, 100), // Truncate long queries
      database 
    });
  }

  /**
   * Track cache operation performance
   */
  trackCacheOperation(operation: string, key: string): string {
    return this.startMetric(`cache ${operation}`, 'cache', { operation, key });
  }

  /**
   * Track AI operation performance
   */
  trackAIOperation(operation: string, model?: string): string {
    return this.startMetric(`AI ${operation}`, 'ai', { operation, model });
  }

  /**
   * Track external API call performance
   */
  trackExternalAPI(service: string, endpoint: string): string {
    return this.startMetric(`${service} API`, 'external', { service, endpoint });
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(timeRange: number = 3600000): any { // Default 1 hour
    const cutoff = Date.now() - timeRange;
    const recentMetrics = this.completedMetrics.filter(m => m.timestamp.getTime() > cutoff);

    const stats = {
      total: recentMetrics.length,
      byType: {} as Record<string, any>,
      averageResponseTime: 0,
      successRate: 0,
      errorRate: 0,
      slowestOperations: [] as any[],
      fastestOperations: [] as any[]
    };

    if (recentMetrics.length === 0) return stats;

    // Group by type
    const typeGroups = recentMetrics.reduce((acc, metric) => {
      if (!acc[metric.type]) acc[metric.type] = [];
      acc[metric.type].push(metric);
      return acc;
    }, {} as Record<string, PerformanceMetric[]>);

    // Calculate stats by type
    Object.entries(typeGroups).forEach(([type, metrics]) => {
      const durations = metrics.filter(m => m.duration).map(m => m.duration!);
      const successCount = metrics.filter(m => m.success).length;

      stats.byType[type] = {
        count: metrics.length,
        averageTime: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
        minTime: durations.length > 0 ? Math.min(...durations) : 0,
        maxTime: durations.length > 0 ? Math.max(...durations) : 0,
        successRate: (successCount / metrics.length) * 100,
        errorRate: ((metrics.length - successCount) / metrics.length) * 100
      };
    });

    // Overall stats
    const allDurations = recentMetrics.filter(m => m.duration).map(m => m.duration!);
    const successCount = recentMetrics.filter(m => m.success).length;

    stats.averageResponseTime = allDurations.length > 0 ? 
      allDurations.reduce((a, b) => a + b, 0) / allDurations.length : 0;
    stats.successRate = (successCount / recentMetrics.length) * 100;
    stats.errorRate = ((recentMetrics.length - successCount) / recentMetrics.length) * 100;

    // Slowest and fastest operations
    const sortedByDuration = recentMetrics
      .filter(m => m.duration)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0));

    stats.slowestOperations = sortedByDuration.slice(0, 10).map(m => ({
      name: m.name,
      type: m.type,
      duration: m.duration,
      timestamp: m.timestamp
    }));

    stats.fastestOperations = sortedByDuration.slice(-10).reverse().map(m => ({
      name: m.name,
      type: m.type,
      duration: m.duration,
      timestamp: m.timestamp
    }));

    return stats;
  }

  /**
   * Get current system metrics
   */
  async getCurrentSystemMetrics(): Promise<SystemMetrics> {
    const process = await import('process');
    const os = await import('os');

    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    const cacheStats = enhancedCacheService.getCacheStats();
    const perfStats = this.getPerformanceStats(60000); // Last minute

    return {
      cpu: {
        usage: await this.getCPUUsage(),
        loadAverage: os.loadavg()
      },
      memory: {
        used: usedMem,
        free: freeMem,
        total: totalMem,
        percentage: (usedMem / totalMem) * 100
      },
      cache: {
        hitRate: cacheStats.hitRate,
        totalKeys: cacheStats.totalKeys,
        memoryUsage: cacheStats.memoryUsage
      },
      api: {
        requestsPerMinute: this.getRequestsPerMinute(),
        averageResponseTime: perfStats.averageResponseTime,
        errorRate: perfStats.errorRate
      },
      database: {
        activeConnections: await this.getDatabaseConnections(),
        queryTime: perfStats.byType.database?.averageTime || 0,
        errorRate: perfStats.byType.database?.errorRate || 0
      },
      timestamp: new Date()
    };
  }

  /**
   * Get performance alerts
   */
  getAlerts(resolved: boolean = false): PerformanceAlert[] {
    return this.alerts.filter(alert => alert.resolved === resolved);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      logger.info(`Performance alert resolved: ${alert.message}`);
    }
  }

  /**
   * Get performance report
   */
  getPerformanceReport(timeRange: number = 3600000): any {
    const stats = this.getPerformanceStats(timeRange);
    const recentAlerts = this.alerts.filter(a => 
      a.timestamp.getTime() > Date.now() - timeRange && !a.resolved
    );

    return {
      summary: {
        totalOperations: stats.total,
        averageResponseTime: stats.averageResponseTime,
        successRate: stats.successRate,
        errorRate: stats.errorRate,
        activeAlerts: recentAlerts.length
      },
      byType: stats.byType,
      slowestOperations: stats.slowestOperations,
      fastestOperations: stats.fastestOperations,
      alerts: recentAlerts,
      systemMetrics: this.systemMetricsHistory.slice(-60), // Last hour
      recommendations: this.generateRecommendations(stats)
    };
  }

  // Private helper methods
  private async getCPUUsage(): Promise<number> {
    // Simplified CPU usage calculation
    const os = await import('os');
    const cpus = os.cpus();
    
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });

    return 100 - (totalIdle / totalTick) * 100;
  }

  private getRequestsPerMinute(): number {
    const oneMinuteAgo = Date.now() - 60000;
    const recentApiMetrics = this.completedMetrics.filter(m => 
      m.type === 'api' && m.timestamp.getTime() > oneMinuteAgo
    );
    return recentApiMetrics.length;
  }

  private async getDatabaseConnections(): Promise<number> {
    // This would integrate with actual database connection pools
    // For now, return a mock value
    return Math.floor(Math.random() * 20) + 5;
  }

  private checkPerformanceThresholds(metric: PerformanceMetric): void {
    if (!metric.duration) return;

    // Check API response time
    if (metric.type === 'api' && metric.duration > this.thresholds.apiResponseTime) {
      this.createAlert('warning', 'apiResponseTime', this.thresholds.apiResponseTime, metric.duration,
        `Slow API response: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
    }

    // Check database query time
    if (metric.type === 'database' && metric.duration > this.thresholds.dbQueryTime) {
      this.createAlert('warning', 'dbQueryTime', this.thresholds.dbQueryTime, metric.duration,
        `Slow database query: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
    }
  }

  private createAlert(
    type: PerformanceAlert['type'],
    metric: string,
    threshold: number,
    currentValue: number,
    message: string
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      metric,
      threshold,
      currentValue,
      message,
      timestamp: new Date(),
      resolved: false
    };

    this.alerts.push(alert);
    
    // Keep only last 1000 alerts
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(-1000);
    }

    logger.warn(`Performance alert: ${message}`);
  }

  private generateRecommendations(stats: any): string[] {
    const recommendations: string[] = [];

    if (stats.errorRate > 5) {
      recommendations.push('High error rate detected. Review error logs and implement better error handling.');
    }

    if (stats.averageResponseTime > 1000) {
      recommendations.push('Slow average response time. Consider implementing caching or optimizing database queries.');
    }

    if (stats.byType.database?.averageTime > 500) {
      recommendations.push('Database queries are slow. Consider adding indexes or optimizing query structure.');
    }

    if (stats.byType.cache?.errorRate > 2) {
      recommendations.push('Cache errors detected. Check Redis connection and configuration.');
    }

    return recommendations;
  }

  private startSystemMonitoring(): void {
    // Collect system metrics every minute
    setInterval(async () => {
      try {
        const metrics = await this.getCurrentSystemMetrics();
        this.systemMetricsHistory.push(metrics);

        // Keep only last 24 hours of data
        if (this.systemMetricsHistory.length > 1440) {
          this.systemMetricsHistory = this.systemMetricsHistory.slice(-1440);
        }

        // Check system thresholds
        this.checkSystemThresholds(metrics);
      } catch (error) {
        logger.error('Failed to collect system metrics:', error);
      }
    }, 60000);
  }

  private startAlertMonitoring(): void {
    // Check for resolved alerts every 5 minutes
    setInterval(() => {
      const currentTime = Date.now();
      const fiveMinutesAgo = currentTime - 300000;

      // Auto-resolve old alerts that haven't been manually resolved
      this.alerts.forEach(alert => {
        if (!alert.resolved && alert.timestamp.getTime() < fiveMinutesAgo) {
          // Check if the condition still exists
          const recentMetrics = this.completedMetrics.filter(m => 
            m.timestamp.getTime() > fiveMinutesAgo
          );

          let shouldResolve = false;

          if (alert.metric === 'apiResponseTime') {
            const recentApiMetrics = recentMetrics.filter(m => m.type === 'api');
            const avgTime = recentApiMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / recentApiMetrics.length;
            shouldResolve = avgTime < alert.threshold;
          }

          if (shouldResolve) {
            alert.resolved = true;
            logger.info(`Auto-resolved alert: ${alert.message}`);
          }
        }
      });
    }, 300000);
  }

  private checkSystemThresholds(metrics: SystemMetrics): void {
    if (metrics.memory.percentage > this.thresholds.memoryUsage) {
      this.createAlert('critical', 'memoryUsage', this.thresholds.memoryUsage, metrics.memory.percentage,
        `High memory usage: ${metrics.memory.percentage.toFixed(1)}%`);
    }

    if (metrics.cpu.usage > this.thresholds.cpuUsage) {
      this.createAlert('warning', 'cpuUsage', this.thresholds.cpuUsage, metrics.cpu.usage,
        `High CPU usage: ${metrics.cpu.usage.toFixed(1)}%`);
    }

    if (metrics.cache.hitRate < this.thresholds.cacheHitRate) {
      this.createAlert('warning', 'cacheHitRate', this.thresholds.cacheHitRate, metrics.cache.hitRate,
        `Low cache hit rate: ${metrics.cache.hitRate.toFixed(1)}%`);
    }
  }
}

export const performanceMonitoringService = new PerformanceMonitoringService();
