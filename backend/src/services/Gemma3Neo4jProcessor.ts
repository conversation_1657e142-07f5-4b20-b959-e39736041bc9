import { executeNeo4jQuery, executeNeo4jTransaction } from '@/config/neo4j';
import { cacheGet, cacheSet } from '@/config/redis';
import { logger, logError } from '@/utils/logger';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

interface Gemma3Entity {
  id: string;
  type: 'SUPPLEMENT' | 'INGREDIENT' | 'CONDITION' | 'MECHANISM' | 'DOSAGE' | 'STUDY' | 'EFFECT' | 'POPULATION';
  name: string;
  properties: {
    description: string;
    confidence: number;
    context: string;
    aliases?: string[];
    category?: string;
  };
}

interface Gemma3Relationship {
  id: string;
  type: string;
  source_entity: string;
  target_entity: string;
  properties: {
    strength: 'weak' | 'moderate' | 'strong';
    evidence_level: 'anecdotal' | 'observational' | 'clinical_trial' | 'meta_analysis';
    confidence: number;
    mechanism?: string;
    dosage_dependent?: boolean;
    population_specific?: string;
    temporal_aspect?: 'acute' | 'chronic' | 'long_term';
    context: string;
  };
}

interface SafetyProfile {
  supplement_id: string;
  overall_risk_level: 'low' | 'moderate' | 'high';
  contraindications: Array<{
    condition: string;
    severity: 'absolute' | 'relative';
    reason: string;
    evidence_level: string;
  }>;
  interactions: Array<{
    interacting_substance: string;
    interaction_type: string;
    severity: 'minor' | 'moderate' | 'major';
    mechanism: string;
    management: string;
  }>;
  adverse_effects: Array<{
    effect: string;
    frequency: 'rare' | 'uncommon' | 'common';
    severity: 'mild' | 'moderate' | 'severe';
    dosage_related: boolean;
    reversible: boolean;
  }>;
}

interface ProcessingResult {
  entities: Gemma3Entity[];
  relationships: Gemma3Relationship[];
  safety_profile: SafetyProfile;
  confidence_score: number;
  processing_time: number;
  source_text: string;
}

export class Gemma3Neo4jProcessor {
  private gemmaEndpoint: string;
  private maxRetries: number = 3;
  private retryDelay: number = 1000;

  constructor(gemmaEndpoint: string = 'http://localhost:11434') {
    this.gemmaEndpoint = gemmaEndpoint;
  }

  /**
   * Complete pipeline: Text → Gemma3 Analysis → Neo4j Graph
   */
  async processTextToGraph(text: string, sourceId?: string): Promise<any> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting Gemma3 → Neo4j processing pipeline', { textLength: text.length });

      // Step 1: Process text with Gemma3
      const gemmaResult = await this.processWithGemma3(text);
      
      // Step 2: Create Neo4j graph from Gemma3 output
      const graphResult = await this.createNeo4jGraph(gemmaResult, sourceId);
      
      const totalTime = Date.now() - startTime;
      
      logger.info('Processing pipeline completed', {
        entitiesCreated: graphResult.entities_created,
        relationshipsCreated: graphResult.relationships_created,
        processingTime: totalTime
      });

      return {
        ...graphResult,
        gemma_confidence: gemmaResult.confidence_score,
        total_processing_time: totalTime,
        pipeline_success: true
      };

    } catch (error) {
      logError('Processing pipeline failed', error, { textLength: text.length });
      throw error;
    }
  }

  /**
   * Process text using Gemma3 with comprehensive prompts
   */
  public async processWithGemma3(text: string): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      // Step 1: Extract entities
      const entities = await this.extractEntities(text);
      
      // Step 2: Extract relationships
      const relationships = await this.extractRelationships(text, entities);
      
      // Step 3: Analyze safety
      const safetyProfile = await this.analyzeSafety(text);
      
      // Step 4: Calculate confidence
      const confidence = this.calculateOverallConfidence(entities, relationships);
      
      return {
        entities,
        relationships,
        safety_profile: safetyProfile,
        confidence_score: confidence,
        processing_time: Date.now() - startTime,
        source_text: text
      };

    } catch (error) {
      logError('Gemma3 processing failed', error);
      throw new Error(`Gemma3 processing failed: ${error}`);
    }
  }

  /**
   * Extract entities using Gemma3 with enhanced prompting
   */
  private async extractEntities(text: string): Promise<Gemma3Entity[]> {
    const prompt = `
You are an expert medical AI analyzing supplement research with exceptional precision. Your task is to extract ALL relevant entities with high accuracy and detailed context.

ENTITY TYPES & CRITERIA:
- SUPPLEMENT: Any supplement, vitamin, mineral, herb, botanical extract, nutraceutical
  * Include brand names, generic names, and common names
  * Note formulation types (capsule, tablet, liquid, powder)
  * Identify standardization levels (e.g., "95% curcumin extract")

- INGREDIENT: Active compounds, nutrients, phytochemicals, excipients
  * Include chemical names, CAS numbers if mentioned
  * Note concentration levels and bioactive forms
  * Identify synergistic compounds (e.g., piperine with curcumin)

- CONDITION: Health conditions, diseases, symptoms, biomarkers
  * Include ICD codes if mentioned
  * Note severity levels and subtypes
  * Identify both primary and secondary conditions

- MECHANISM: Biological pathways, molecular targets, physiological processes
  * Include enzyme names, receptor types, cellular pathways
  * Note upstream and downstream effects
  * Identify tissue-specific mechanisms

- DOSAGE: Specific dosing information with complete context
  * Include amount, frequency, duration, timing
  * Note population-specific dosing
  * Identify bioavailability enhancers

- STUDY: Research studies, clinical trials, meta-analyses
  * Include study design, sample size, duration
  * Note primary and secondary endpoints
  * Identify study quality indicators

- EFFECT: Therapeutic effects, side effects, outcomes, biomarker changes
  * Include magnitude of effect and statistical significance
  * Note time to onset and duration
  * Identify dose-response relationships

- POPULATION: Target demographics, patient groups, study populations
  * Include age ranges, gender, health status
  * Note inclusion/exclusion criteria
  * Identify vulnerable populations

CONFIDENCE SCORING CRITERIA:
- 0.9-1.0: Explicitly stated with clear context and supporting details
- 0.7-0.8: Clearly implied with good contextual evidence
- 0.5-0.6: Mentioned but with limited context or ambiguity
- 0.3-0.4: Weakly implied or uncertain interpretation
- 0.1-0.2: Speculative or very uncertain

TEXT TO ANALYZE:
${text}

RETURN PRECISE JSON:
{
  "entities": [
    {
      "id": "entity_type_sequential_number",
      "type": "ENTITY_TYPE",
      "name": "precise_entity_name",
      "properties": {
        "description": "comprehensive_description_with_context",
        "confidence": confidence_score_based_on_criteria,
        "context": "exact_surrounding_text_excerpt",
        "aliases": ["alternative_names", "synonyms", "abbreviations"],
        "category": "specific_subcategory",
        "evidenceLevel": "strength_of_evidence_in_text",
        "quantitativeData": "any_numerical_values_or_measurements",
        "qualitativeDescriptors": ["adjectives", "qualifiers", "modifiers"],
        "relationshipHints": ["potential_connections_to_other_entities"]
      }
    }
  ]
}

CRITICAL REQUIREMENTS:
- Extract EVERY entity, even if mentioned briefly
- Assign realistic confidence scores based on context clarity
- Include exact text excerpts for context verification
- Identify numerical values, percentages, and measurements
- Note qualitative descriptors that indicate strength or certainty
- Suggest potential relationships for later processing
- Ensure entity names are precise and unambiguous`;

    const response = await this.callGemma3(prompt);
    return this.parseEntitiesResponse(response);
  }

  /**
   * Extract relationships using Gemma3 with enhanced context analysis
   */
  private async extractRelationships(text: string, entities: Gemma3Entity[]): Promise<Gemma3Relationship[]> {
    const entitiesContext = entities.map(e =>
      `${e.id}: ${e.name} (${e.type}) - ${e.properties.description || 'No description'}`
    ).join('\n');

    const prompt = `
You are an expert medical AI analyzing complex relationships between supplement entities. Your task is to identify ALL meaningful relationships with precise characterization and high confidence scoring.

AVAILABLE ENTITIES:
${entitiesContext}

RELATIONSHIP TYPES & CRITERIA:

THERAPEUTIC RELATIONSHIPS:
- TREATS: Direct therapeutic effect on condition
  * Requires evidence of clinical benefit
  * Note primary vs secondary treatment effects
  * Consider dose-response relationships

- PREVENTS: Prophylactic or preventive effect
  * Include risk reduction percentages if mentioned
  * Note primary vs secondary prevention
  * Consider population-specific prevention

- SUPPORTS: General health support or maintenance
  * Include nutritional support relationships
  * Note synergistic support effects
  * Consider long-term vs acute support

INTERACTION RELATIONSHIPS:
- ENHANCES: Positive interaction increasing effectiveness
  * Include bioavailability enhancement
  * Note synergistic mechanisms
  * Consider timing-dependent enhancement

- INHIBITS: Negative interaction reducing effectiveness
  * Include competitive inhibition
  * Note antagonistic effects
  * Consider dose-dependent inhibition

- SYNERGISTIC_WITH: Combined effect greater than sum of parts
  * Requires evidence of synergy
  * Note specific mechanisms of synergy
  * Consider optimal ratios or combinations

- CONTRAINDICATED_WITH: Should not be combined
  * Include safety contraindications
  * Note severity of contraindication
  * Consider population-specific contraindications

MECHANISTIC RELATIONSHIPS:
- ACTS_VIA: Mechanism of action relationship
  * Include molecular targets and pathways
  * Note tissue-specific mechanisms
  * Consider upstream and downstream effects

- AFFECTS_ABSORPTION: Bioavailability relationships
  * Include enhancement or reduction of absorption
  * Note timing dependencies
  * Consider food interactions

- METABOLIZED_BY: Metabolic pathway relationships
  * Include enzyme systems involved
  * Note genetic polymorphism effects
  * Consider drug-nutrient interactions

RESEARCH RELATIONSHIPS:
- STUDIED_IN: Research conducted in specific populations
  * Include study design and quality
  * Note sample sizes and duration
  * Consider generalizability

- DEMONSTRATED_IN: Effects shown in specific studies
  * Include statistical significance
  * Note effect sizes and clinical relevance
  * Consider study limitations

CONFIDENCE SCORING FOR RELATIONSHIPS:
- 0.9-1.0: Explicitly stated with clear causal language and supporting data
- 0.7-0.8: Strongly implied with good contextual evidence and logical connection
- 0.5-0.6: Moderately supported with some evidence but potential ambiguity
- 0.3-0.4: Weakly implied or based on indirect evidence
- 0.1-0.2: Speculative or very uncertain connection

STRENGTH ASSESSMENT:
- STRONG: Clear, direct, well-documented relationship with quantitative data
- MODERATE: Well-supported relationship with qualitative or limited quantitative data
- WEAK: Suggested relationship with minimal or indirect evidence

EVIDENCE LEVEL ASSESSMENT:
- META_ANALYSIS: Systematic review or meta-analysis mentioned
- CLINICAL_TRIAL: Randomized controlled trial or clinical study
- OBSERVATIONAL: Cohort, case-control, or cross-sectional study
- ANECDOTAL: Case reports, expert opinion, or traditional use

TEXT TO ANALYZE:
${text}

RETURN COMPREHENSIVE JSON:
{
  "relationships": [
    {
      "id": "relationship_type_sequential_number",
      "type": "RELATIONSHIP_TYPE",
      "source_entity": "source_entity_id",
      "target_entity": "target_entity_id",
      "properties": {
        "strength": "weak|moderate|strong",
        "evidence_level": "anecdotal|observational|clinical_trial|meta_analysis",
        "confidence": confidence_score_based_on_criteria,
        "mechanism": "detailed_biological_mechanism_if_mentioned",
        "context": "exact_text_excerpt_supporting_relationship",
        "dosage_dependent": true_or_false,
        "population_specific": "specific_population_if_applicable",
        "temporal_aspect": "acute|chronic|long_term",
        "quantitative_data": "any_numerical_data_supporting_relationship",
        "clinical_significance": "high|moderate|low|unknown",
        "safety_implications": ["any_safety_considerations"],
        "study_details": "study_design_sample_size_duration_if_mentioned",
        "effect_magnitude": "percentage_or_fold_change_if_mentioned",
        "onset_time": "time_to_effect_if_mentioned",
        "duration_of_effect": "how_long_effect_lasts_if_mentioned"
      }
    }
  ]
}

CRITICAL REQUIREMENTS:
- Only create relationships explicitly stated or strongly implied in the text
- Assign confidence scores based on strength of textual evidence
- Include exact text excerpts that support each relationship
- Note any quantitative data (percentages, fold changes, p-values)
- Identify dose-dependent and population-specific relationships
- Consider temporal aspects (immediate vs long-term effects)
- Assess clinical significance and safety implications
- Ensure source and target entities exist in the provided entity list`;

    const response = await this.callGemma3(prompt);
    return this.parseRelationshipsResponse(response);
  }

  /**
   * Analyze safety using Gemma3
   */
  private async analyzeSafety(text: string): Promise<SafetyProfile> {
    const prompt = `
Analyze safety information in the text.

TEXT: ${text}

Return JSON:
{
  "safety_profile": {
    "supplement_id": "supplement_id",
    "overall_risk_level": "low|moderate|high",
    "contraindications": [
      {
        "condition": "condition_name",
        "severity": "absolute|relative",
        "reason": "explanation",
        "evidence_level": "evidence_quality"
      }
    ],
    "interactions": [
      {
        "interacting_substance": "substance_name",
        "interaction_type": "interaction_type",
        "severity": "minor|moderate|major",
        "mechanism": "mechanism",
        "management": "management_strategy"
      }
    ],
    "adverse_effects": [
      {
        "effect": "effect_description",
        "frequency": "rare|uncommon|common",
        "severity": "mild|moderate|severe",
        "dosage_related": true|false,
        "reversible": true|false
      }
    ]
  }
}`;

    const response = await this.callGemma3(prompt);
    return this.parseSafetyResponse(response);
  }

  /**
   * Call Gemma3 API with retry logic
   */
  private async callGemma3(prompt: string): Promise<string> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await axios.post(`${this.gemmaEndpoint}/api/generate`, {
          model: 'gemma3:4b',
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.1,
            top_p: 0.9,
            max_tokens: 4000
          }
        }, {
          timeout: 30000
        });

        return response.data.response;

      } catch (error) {
        logger.warn(`Gemma3 API attempt ${attempt} failed`, { error: error.message });
        
        if (attempt === this.maxRetries) {
          throw new Error(`Gemma3 API failed after ${this.maxRetries} attempts: ${error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
      }
    }
    
    throw new Error('Gemma3 API failed - should not reach here');
  }

  /**
   * Create Neo4j graph from Gemma3 results
   */
  private async createNeo4jGraph(result: ProcessingResult, sourceId?: string): Promise<any> {
    try {
      // Step 1: Create entities
      const entityMapping = await this.createEntities(result.entities);
      
      // Step 2: Create relationships
      const relationshipIds = await this.createRelationships(result.relationships, entityMapping);
      
      // Step 3: Create safety profiles
      const safetyIds = await this.createSafetyProfiles(result.safety_profile, entityMapping);
      
      // Step 4: Create source document
      const documentId = await this.createSourceDocument(result, sourceId);
      
      return {
        entities_created: Object.keys(entityMapping).length,
        relationships_created: relationshipIds.length,
        safety_profiles_created: safetyIds.length,
        source_document_id: documentId,
        entity_mapping: entityMapping
      };

    } catch (error) {
      logError('Neo4j graph creation failed', error);
      throw error;
    }
  }

  /**
   * Create entity nodes in Neo4j
   */
  private async createEntities(entities: Gemma3Entity[]): Promise<Record<string, string>> {
    const entityMapping: Record<string, string> = {};
    
    for (const entity of entities) {
      const neo4jId = uuidv4();
      entityMapping[entity.id] = neo4jId;
      
      const query = `
        CREATE (n:${entity.type} {
          id: $id,
          name: $name,
          description: $description,
          confidence: $confidence,
          context: $context,
          aliases: $aliases,
          category: $category,
          created_at: datetime(),
          source: 'GEMMA3_EXTRACTION'
        })
        RETURN n.id as created_id
      `;
      
      await executeNeo4jQuery(query, {
        id: neo4jId,
        name: entity.name,
        description: entity.properties.description,
        confidence: entity.properties.confidence,
        context: entity.properties.context,
        aliases: entity.properties.aliases || [],
        category: entity.properties.category || ''
      });
    }
    
    return entityMapping;
  }

  /**
   * Create relationship edges in Neo4j
   */
  private async createRelationships(
    relationships: Gemma3Relationship[], 
    entityMapping: Record<string, string>
  ): Promise<string[]> {
    const relationshipIds: string[] = [];
    
    for (const rel of relationships) {
      const sourceId = entityMapping[rel.source_entity];
      const targetId = entityMapping[rel.target_entity];
      
      if (!sourceId || !targetId) {
        logger.warn('Skipping relationship - missing entity mapping', { 
          relationship: rel.id,
          sourceFound: !!sourceId,
          targetFound: !!targetId
        });
        continue;
      }
      
      const relationshipId = uuidv4();
      relationshipIds.push(relationshipId);
      
      const query = `
        MATCH (source {id: $sourceId}), (target {id: $targetId})
        CREATE (source)-[r:${rel.type} {
          id: $relationshipId,
          strength: $strength,
          evidence_level: $evidenceLevel,
          confidence: $confidence,
          mechanism: $mechanism,
          context: $context,
          created_at: datetime(),
          source: 'GEMMA3_EXTRACTION'
        }]->(target)
        RETURN r.id as created_id
      `;
      
      await executeNeo4jQuery(query, {
        sourceId,
        targetId,
        relationshipId,
        strength: rel.properties.strength,
        evidenceLevel: rel.properties.evidence_level,
        confidence: rel.properties.confidence,
        mechanism: rel.properties.mechanism || '',
        context: rel.properties.context
      });
    }
    
    return relationshipIds;
  }

  /**
   * Create safety profile nodes
   */
  private async createSafetyProfiles(
    safetyProfile: SafetyProfile, 
    entityMapping: Record<string, string>
  ): Promise<string[]> {
    // Implementation for safety profiles
    return [];
  }

  /**
   * Create source document node
   */
  private async createSourceDocument(result: ProcessingResult, sourceId?: string): Promise<string> {
    const documentId = sourceId || uuidv4();
    
    const query = `
      CREATE (doc:SourceDocument {
        id: $id,
        content: $content,
        processing_time: $processingTime,
        confidence_score: $confidenceScore,
        entities_extracted: $entitiesCount,
        relationships_extracted: $relationshipsCount,
        created_at: datetime(),
        processor: 'GEMMA3_NEO4J_PIPELINE'
      })
      RETURN doc.id as created_id
    `;
    
    await executeNeo4jQuery(query, {
      id: documentId,
      content: result.source_text.substring(0, 1000), // Truncate for storage
      processingTime: result.processing_time,
      confidenceScore: result.confidence_score,
      entitiesCount: result.entities.length,
      relationshipsCount: result.relationships.length
    });
    
    return documentId;
  }

  // Helper methods for parsing Gemma3 responses
  private parseEntitiesResponse(response: string): Gemma3Entity[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.entities || [];
    } catch {
      return this.fallbackEntityParsing(response);
    }
  }

  private parseRelationshipsResponse(response: string): Gemma3Relationship[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.relationships || [];
    } catch {
      return [];
    }
  }

  private parseSafetyResponse(response: string): SafetyProfile {
    try {
      const parsed = JSON.parse(response);
      return parsed.safety_profile || this.getDefaultSafetyProfile();
    } catch {
      return this.getDefaultSafetyProfile();
    }
  }

  private fallbackEntityParsing(response: string): Gemma3Entity[] {
    // Basic fallback parsing logic
    return [];
  }

  private getDefaultSafetyProfile(): SafetyProfile {
    return {
      supplement_id: '',
      overall_risk_level: 'moderate',
      contraindications: [],
      interactions: [],
      adverse_effects: []
    };
  }

  private calculateOverallConfidence(entities: Gemma3Entity[], relationships: Gemma3Relationship[]): number {
    const allConfidences = [
      ...entities.map(e => e.properties.confidence),
      ...relationships.map(r => r.properties.confidence)
    ];
    
    if (allConfidences.length === 0) return 0.5;
    
    return allConfidences.reduce((sum, conf) => sum + conf, 0) / allConfidences.length;
  }
}
