import { Driver, Session } from 'neo4j-driver';
import { RedisClientType } from 'redis';
import { logger } from '../utils/logger';

export interface Supplement {
  id: string;
  name: string;
  category: string;
  description: string;
  bioavailability: number;
  commonDosage: string;
  safetyRating: number;
  price?: number;
  brand?: string;
  ingredients: Ingredient[];
  effects: Effect[];
  interactions: Interaction[];
  researchCount: number;
  popularity: number;
}

export interface Ingredient {
  id: string;
  name: string;
  amount: number;
  unit: string;
  bioavailability: number;
  molecularWeight?: number;
  cas?: string; // Chemical Abstracts Service number
}

export interface Effect {
  id: string;
  name: string;
  category: string;
  mechanism: string;
  strength: number; // 0-1 scale
  evidenceLevel: number; // 1-5 scale
  studiesCount: number;
  onsetTime?: string;
  duration?: string;
}

export interface Interaction {
  id: string;
  supplementId: string;
  targetId: string;
  targetType: 'supplement' | 'medication' | 'condition';
  severity: 'mild' | 'moderate' | 'severe' | 'critical';
  mechanism: string;
  evidenceLevel: number;
  description: string;
  recommendation: string;
}

export interface SearchQuery {
  term: string;
  category?: string;
  effects?: string[];
  priceRange?: { min: number; max: number };
  safetyRating?: number;
  limit?: number;
  offset?: number;
}

export interface SupplementRecommendation {
  supplement: Supplement;
  score: number;
  reasoning: string[];
  warnings: string[];
  dosageRecommendation: string;
  timingRecommendation: string;
}

export class SupplementService {
  private neo4jDriver: Driver;
  private redisClient: RedisClientType;

  constructor(neo4jDriver: Driver, redisClient: RedisClientType) {
    this.neo4jDriver = neo4jDriver;
    this.redisClient = redisClient;
  }

  async searchSupplements(query: SearchQuery): Promise<Supplement[]> {
    const cacheKey = `search:${JSON.stringify(query)}`;
    
    try {
      // Check cache first
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        logger.info('Returning cached search results');
        return JSON.parse(cached);
      }

      const session = this.neo4jDriver.session();
      
      // Build dynamic Cypher query
      let cypherQuery = `
        MATCH (s:Supplement)
        OPTIONAL MATCH (s)-[:CONTAINS]->(i:Ingredient)
        OPTIONAL MATCH (s)-[:HAS_EFFECT]->(e:Effect)
        WHERE 1=1
      `;

      const parameters: any = {};

      if (query.term) {
        cypherQuery += ` AND (s.name CONTAINS $term OR i.name CONTAINS $term OR e.name CONTAINS $term)`;
        parameters.term = query.term;
      }

      if (query.category) {
        cypherQuery += ` AND s.category = $category`;
        parameters.category = query.category;
      }

      if (query.safetyRating) {
        cypherQuery += ` AND s.safetyRating >= $safetyRating`;
        parameters.safetyRating = query.safetyRating;
      }

      if (query.priceRange) {
        cypherQuery += ` AND s.price >= $minPrice AND s.price <= $maxPrice`;
        parameters.minPrice = query.priceRange.min;
        parameters.maxPrice = query.priceRange.max;
      }

      cypherQuery += `
        RETURN s, 
               collect(DISTINCT i) as ingredients,
               collect(DISTINCT e) as effects
        ORDER BY s.popularity DESC, s.safetyRating DESC
        SKIP $offset
        LIMIT $limit
      `;

      parameters.offset = query.offset || 0;
      parameters.limit = query.limit || 20;

      const result = await session.run(cypherQuery, parameters);
      const supplements = this.mapNeo4jResults(result.records);

      await session.close();

      // Cache results for 1 hour
      await this.redisClient.setEx(cacheKey, 3600, JSON.stringify(supplements));

      logger.info(`Found ${supplements.length} supplements for query: ${query.term}`);
      return supplements;

    } catch (error) {
      logger.error('Error searching supplements:', error);
      throw new Error('Failed to search supplements');
    }
  }

  async getSupplementById(id: string): Promise<Supplement | null> {
    const cacheKey = `supplement:${id}`;
    
    try {
      // Check cache first
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const session = this.neo4jDriver.session();
      
      const cypherQuery = `
        MATCH (s:Supplement {id: $id})
        OPTIONAL MATCH (s)-[:CONTAINS {amount: amount, unit: unit}]->(i:Ingredient)
        OPTIONAL MATCH (s)-[:HAS_EFFECT {strength: strength, evidence_level: evidenceLevel}]->(e:Effect)
        OPTIONAL MATCH (s)-[:INTERACTS_WITH {severity: severity, mechanism: mechanism}]->(target)
        RETURN s,
               collect(DISTINCT {
                 ingredient: i,
                 amount: amount,
                 unit: unit
               }) as ingredients,
               collect(DISTINCT {
                 effect: e,
                 strength: strength,
                 evidenceLevel: evidenceLevel
               }) as effects,
               collect(DISTINCT {
                 target: target,
                 severity: severity,
                 mechanism: mechanism
               }) as interactions
      `;

      const result = await session.run(cypherQuery, { id });
      
      if (result.records.length === 0) {
        await session.close();
        return null;
      }

      const supplement = this.mapSingleSupplementResult(result.records[0]);
      await session.close();

      // Cache for 2 hours
      await this.redisClient.setEx(cacheKey, 7200, JSON.stringify(supplement));

      return supplement;

    } catch (error) {
      logger.error('Error fetching supplement:', error);
      throw new Error('Failed to fetch supplement');
    }
  }

  async getSupplementInteractions(supplementIds: string[]): Promise<Interaction[]> {
    const cacheKey = `interactions:${supplementIds.sort().join(',')}`;
    
    try {
      // Check cache first
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const session = this.neo4jDriver.session();
      
      const cypherQuery = `
        MATCH (s1:Supplement)-[r:INTERACTS_WITH]-(s2:Supplement)
        WHERE s1.id IN $supplementIds AND s2.id IN $supplementIds
        RETURN s1.id as supplement1,
               s2.id as supplement2,
               r.severity as severity,
               r.mechanism as mechanism,
               r.evidence_level as evidenceLevel,
               r.description as description,
               r.recommendation as recommendation
        ORDER BY 
          CASE r.severity 
            WHEN 'critical' THEN 4
            WHEN 'severe' THEN 3
            WHEN 'moderate' THEN 2
            WHEN 'mild' THEN 1
            ELSE 0
          END DESC
      `;

      const result = await session.run(cypherQuery, { supplementIds });
      const interactions = result.records.map(record => ({
        id: `${record.get('supplement1')}-${record.get('supplement2')}`,
        supplementId: record.get('supplement1'),
        targetId: record.get('supplement2'),
        targetType: 'supplement' as const,
        severity: record.get('severity'),
        mechanism: record.get('mechanism'),
        evidenceLevel: record.get('evidenceLevel'),
        description: record.get('description'),
        recommendation: record.get('recommendation'),
      }));

      await session.close();

      // Cache for 30 minutes
      await this.redisClient.setEx(cacheKey, 1800, JSON.stringify(interactions));

      logger.info(`Found ${interactions.length} interactions for supplements: ${supplementIds.join(', ')}`);
      return interactions;

    } catch (error) {
      logger.error('Error fetching interactions:', error);
      throw new Error('Failed to fetch interactions');
    }
  }

  async getSupplementsByEffect(effectName: string, limit: number = 10): Promise<Supplement[]> {
    const cacheKey = `effect:${effectName}:${limit}`;
    
    try {
      // Check cache first
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const session = this.neo4jDriver.session();
      
      const cypherQuery = `
        MATCH (s:Supplement)-[r:HAS_EFFECT]->(e:Effect)
        WHERE e.name CONTAINS $effectName
        OPTIONAL MATCH (s)-[:CONTAINS]->(i:Ingredient)
        RETURN s,
               r.strength as effectStrength,
               r.evidence_level as evidenceLevel,
               collect(DISTINCT i) as ingredients
        ORDER BY r.strength DESC, r.evidence_level DESC, s.popularity DESC
        LIMIT $limit
      `;

      const result = await session.run(cypherQuery, { effectName, limit });
      const supplements = this.mapNeo4jResults(result.records);

      await session.close();

      // Cache for 1 hour
      await this.redisClient.setEx(cacheKey, 3600, JSON.stringify(supplements));

      return supplements;

    } catch (error) {
      logger.error('Error fetching supplements by effect:', error);
      throw new Error('Failed to fetch supplements by effect');
    }
  }

  private mapNeo4jResults(records: any[]): Supplement[] {
    return records.map(record => {
      const supplement = record.get('s').properties;
      const ingredients = record.get('ingredients') || [];
      const effects = record.get('effects') || [];

      return {
        id: supplement.id,
        name: supplement.name,
        category: supplement.category,
        description: supplement.description,
        bioavailability: supplement.bioavailability,
        commonDosage: supplement.commonDosage,
        safetyRating: supplement.safetyRating,
        price: supplement.price,
        brand: supplement.brand,
        researchCount: supplement.researchCount || 0,
        popularity: supplement.popularity || 0,
        ingredients: ingredients.map((i: any) => ({
          id: i.id,
          name: i.name,
          amount: i.amount || 0,
          unit: i.unit || 'mg',
          bioavailability: i.bioavailability || 1,
        })),
        effects: effects.map((e: any) => ({
          id: e.id,
          name: e.name,
          category: e.category,
          mechanism: e.mechanism,
          strength: e.strength || 0,
          evidenceLevel: e.evidenceLevel || 1,
          studiesCount: e.studiesCount || 0,
        })),
        interactions: [] as any[], // Will be populated separately if needed
      };
    });
  }

  private mapSingleSupplementResult(record: any): Supplement {
    const supplement = record.get('s').properties;
    const ingredients = record.get('ingredients') || [];
    const effects = record.get('effects') || [];
    const interactions = record.get('interactions') || [];

    return {
      id: supplement.id,
      name: supplement.name,
      category: supplement.category,
      description: supplement.description,
      bioavailability: supplement.bioavailability,
      commonDosage: supplement.commonDosage,
      safetyRating: supplement.safetyRating,
      price: supplement.price,
      brand: supplement.brand,
      researchCount: supplement.researchCount || 0,
      popularity: supplement.popularity || 0,
      ingredients: ingredients.map((item: any) => ({
        id: item.ingredient.properties.id,
        name: item.ingredient.properties.name,
        amount: item.amount || 0,
        unit: item.unit || 'mg',
        bioavailability: item.ingredient.properties.bioavailability || 1,
      })),
      effects: effects.map((item: any) => ({
        id: item.effect.properties.id,
        name: item.effect.properties.name,
        category: item.effect.properties.category,
        mechanism: item.effect.properties.mechanism,
        strength: item.strength || 0,
        evidenceLevel: item.evidenceLevel || 1,
        studiesCount: item.effect.properties.studiesCount || 0,
      })),
      interactions: interactions.map((item: any) => ({
        id: `${supplement.id}-${item.target.properties.id}`,
        supplementId: supplement.id,
        targetId: item.target.properties.id,
        targetType: 'supplement' as const,
        severity: item.severity,
        mechanism: item.mechanism,
        evidenceLevel: 3,
        description: `Interaction between ${supplement.name} and ${item.target.properties.name}`,
        recommendation: 'Consult healthcare provider',
      })),
    };
  }
}
