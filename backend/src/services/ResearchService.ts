import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

export interface ResearchQuery {
  query: string;
  type: 'web_search' | 'academic' | 'news' | 'supplement_specific';
  filters?: {
    timeRange?: 'day' | 'week' | 'month' | 'year';
    domains?: string[];
    excludeDomains?: string[];
    maxResults?: number;
  };
  context?: string;
}

export interface ResearchResult {
  title: string;
  url: string;
  snippet: string;
  content?: string;
  publishedDate?: string;
  domain: string;
  relevanceScore: number;
  medicalRelevance?: number;
  extractedEntities?: string[];
}

export interface ResearchResponse {
  query: string;
  results: ResearchResult[];
  totalResults: number;
  searchTime: number;
  suggestions?: string[];
  relatedQueries?: string[];
  metadata?: {
    processingTime?: number;
    searchDepth?: string;
    sourcesAnalyzed?: number;
    totalResults?: number;
    searchTime?: number;
    sources?: string[];
  };
}

export class ResearchService {
  private braveClient: AxiosInstance;
  private tavilyClient: AxiosInstance;

  constructor() {
    // Brave Search API client
    this.braveClient = axios.create({
      baseURL: 'https://api.search.brave.com/res/v1',
      headers: {
        'X-Subscription-Token': process.env['BRAVE_API_KEY'] || '',
        'Accept': 'application/json',
      },
      timeout: 15000,
    });

    // Tavily API client
    this.tavilyClient = axios.create({
      baseURL: 'https://api.tavily.com',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 20000,
    });
  }

  /**
   * Perform web search using Brave Search API
   */
  async searchWeb(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const startTime = Date.now();
      
      const params = {
        q: this.enhanceQuery(query),
        count: query.filters?.maxResults || 10,
        offset: 0,
        mkt: 'en-US',
        safesearch: 'moderate',
        freshness: query.filters?.timeRange || undefined,
        text_decorations: false,
        spellcheck: true,
      };

      const response = await this.braveClient.get('/web/search', { params });
      const searchTime = Date.now() - startTime;

      const results = this.processBraveResults(response.data.web?.results || []);
      
      return {
        query: query.query,
        results,
        totalResults: response.data.web?.total_count || 0,
        searchTime,
        suggestions: response.data.query?.spellcheck_off ? [response.data.query.original] : [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Brave search failed:', error);
      throw new Error('Web search failed');
    }
  }

  /**
   * Perform advanced research using Tavily
   */
  async searchTavily(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const startTime = Date.now();

      const requestBody = {
        api_key: process.env['TAVILY_API_KEY'],
        query: this.enhanceQuery(query),
        search_depth: query.type === 'academic' ? 'advanced' : 'basic',
        include_answer: true,
        include_raw_content: true,
        max_results: query.filters?.maxResults || 10,
        include_domains: query.filters?.domains || [],
        exclude_domains: query.filters?.excludeDomains || [],
        include_images: false,
        include_image_descriptions: false,
      };

      const response = await this.tavilyClient.post('/search', requestBody);
      const searchTime = Date.now() - startTime;

      const results = this.processTavilyResults(response.data.results || []);

      return {
        query: query.query,
        results,
        totalResults: results.length,
        searchTime,
        suggestions: [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Tavily search failed:', error);
      throw new Error('Advanced search failed');
    }
  }

  /**
   * Perform supplement-specific research with advanced Tavily integration
   */
  async searchSupplements(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      // Enhance query for supplement research
      const supplementQuery = {
        ...query,
        query: this.enhanceSupplementQuery(query.query),
        filters: {
          ...query.filters,
          domains: [
            'pubmed.ncbi.nlm.nih.gov',
            'examine.com',
            'consumerlab.com',
            'ods.od.nih.gov',
            'nccih.nih.gov',
            'webmd.com',
            'mayoclinic.org',
            'healthline.com',
            ...(query.filters?.domains || [])
          ]
        }
      };

      // Use enhanced Tavily search with advanced features
      return await this.searchTavilyAdvanced(supplementQuery);

    } catch (error) {
      logger.error('Supplement search failed:', error);
      throw new Error('Supplement research failed');
    }
  }

  /**
   * Advanced Tavily search with comprehensive data collection
   */
  async searchTavilyAdvanced(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const startTime = Date.now();

      // Phase 1: Basic search
      const searchResults = await this.performTavilySearch(query);

      // Phase 2: Deep crawling of top results
      const crawlResults = await this.performTavilyCrawling(searchResults.results.slice(0, 5));

      // Phase 3: Content extraction and analysis
      const extractedContent = await this.performTavilyExtraction(crawlResults);

      // Phase 4: Site mapping for comprehensive coverage
      const siteMap = await this.performTavilySiteMapping(query);

      // Combine all results
      const combinedResults = this.combineResearchResults(
        query, // Pass query here
        searchResults,
        crawlResults,
        extractedContent,
        siteMap
      );

      const processingTime = Date.now() - startTime;

      logger.info(`Advanced Tavily research completed in ${processingTime}ms`);

      return {
        query: query.query,
        results: combinedResults.results,
        totalResults: combinedResults.totalResults,
        searchTime: combinedResults.searchTime,
        suggestions: combinedResults.suggestions,
        relatedQueries: combinedResults.relatedQueries,
        metadata: {
          processingTime,
          searchDepth: 'advanced',
          sourcesAnalyzed: combinedResults.results.length,
          totalResults: combinedResults.totalResults,
          searchTime: combinedResults.searchTime,
          sources: combinedResults.metadata?.sources // Pass sources from combinedResults metadata
        }
      };

    } catch (error) {
      logger.error('Advanced Tavily search failed:', error);
      // Fallback to basic search
      return await this.searchTavily(query);
    }
  }

  /**
   * Perform Tavily search with enhanced parameters
   */
  private async performTavilySearch(query: ResearchQuery): Promise<any> {
    // This would integrate with Tavily MCP search
    // Implementation depends on MCP integration
    return {
      results: [],
      metadata: {
        totalResults: 0,
        searchTime: Date.now()
      }
    };
  }

  /**
   * Perform deep crawling using Tavily crawl
   */
  private async performTavilyCrawling(urls: string[]): Promise<any> {
    const crawlResults = [];

    for (const url of urls) {
      try {
        // Use Tavily crawl for deep content extraction
        const crawlResult = await this.tavilyCrawlSite(url);
        crawlResults.push(crawlResult);
      } catch (error) {
        logger.warn(`Failed to crawl ${url}:`, error);
      }
    }

    return crawlResults;
  }

  /**
   * Extract content using Tavily extract
   */
  private async performTavilyExtraction(crawlResults: any[]): Promise<any> {
    const extractedContent = [];

    for (const crawlResult of crawlResults) {
      try {
        // Use Tavily extract for content processing
        const extracted = await this.tavilyExtractContent(crawlResult.urls);
        extractedContent.push(extracted);
      } catch (error) {
        logger.warn(`Failed to extract content:`, error);
      }
    }

    return extractedContent;
  }

  /**
   * Perform site mapping using Tavily map
   */
  private async performTavilySiteMapping(query: ResearchQuery): Promise<any> {
    const relevantDomains = query.filters?.domains || [];
    const siteMap = [];

    for (const domain of relevantDomains.slice(0, 3)) {
      try {
        // Use Tavily map for site structure analysis
        const mapResult = await this.tavilyMapSite(`https://${domain}`);
        siteMap.push(mapResult);
      } catch (error) {
        logger.warn(`Failed to map ${domain}:`, error);
      }
    }

    return siteMap;
  }

  /**
   * Tavily crawl integration
   */
  private async tavilyCrawlSite(url: string): Promise<any> {
    // This would use the Tavily MCP crawl function
    // For now, return mock data
    return {
      url,
      pages: [],
      metadata: {
        crawlTime: Date.now(),
        pagesFound: 0
      }
    };
  }

  /**
   * Tavily extract integration
   */
  private async tavilyExtractContent(urls: string[]): Promise<any> {
    // This would use the Tavily MCP extract function
    // For now, return mock data
    return {
      urls,
      content: [],
      metadata: {
        extractTime: Date.now(),
        contentLength: 0
      }
    };
  }

  /**
   * Tavily map integration
   */
  private async tavilyMapSite(url: string): Promise<any> {
    // This would use the Tavily MCP map function
    // For now, return mock data
    return {
      url,
      structure: [],
      metadata: {
        mapTime: Date.now(),
        pagesDiscovered: 0
      }
    };
  }

  /**
   * Combine results from all research phases
   */
  private combineResearchResults(
    query: ResearchQuery, // Add query parameter
    searchResults: any,
    crawlResults: any,
    extractedContent: any,
    siteMap: any
  ): ResearchResponse {
    return {
      query: query.query, // Add query property
      results: [
        ...searchResults.results,
        ...this.processCrawlResults(crawlResults),
        ...this.processExtractedContent(extractedContent),
        ...this.processSiteMapResults(siteMap)
      ],
      totalResults: searchResults.totalResults + this.processCrawlResults(crawlResults).length + this.processExtractedContent(extractedContent).length + this.processSiteMapResults(siteMap).length,
      searchTime: searchResults.searchTime,
      suggestions: searchResults.suggestions,
      relatedQueries: searchResults.relatedQueries,
      metadata: {
        sources: ['tavily_search', 'tavily_crawl', 'tavily_extract', 'tavily_map']
      }
    };
  }

  private processCrawlResults(crawlResults: any[]): any[] {
    // Process crawl results into standard format
    return [];
  }

  private processExtractedContent(extractedContent: any[]): any[] {
    // Process extracted content into standard format
    return [];
  }

  private processSiteMapResults(siteMap: any[]): any[] {
    // Process site map results into standard format
    return [];
  }

  /**
   * Combine multiple search sources
   */
  async comprehensiveSearch(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const [braveResults, tavilyResults] = await Promise.allSettled([
        this.searchWeb(query),
        this.searchTavily(query)
      ]);

      const combinedResults: ResearchResult[] = [];
      
      if (braveResults.status === 'fulfilled') {
        combinedResults.push(...braveResults.value.results);
      }
      
      if (tavilyResults.status === 'fulfilled') {
        combinedResults.push(...tavilyResults.value.results);
      }

      // Remove duplicates and sort by relevance
      const uniqueResults = this.deduplicateResults(combinedResults);
      const sortedResults = uniqueResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

      return {
        query: query.query,
        results: sortedResults.slice(0, query.filters?.maxResults || 20),
        totalResults: sortedResults.length,
        searchTime: 0,
        suggestions: [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Comprehensive search failed:', error);
      throw new Error('Research failed');
    }
  }

  /**
   * Enhance query for better medical/supplement results
   */
  private enhanceQuery(query: ResearchQuery): string {
    let enhanced = query.query;

    if (query.type === 'supplement_specific') {
      enhanced += ' supplement benefits side effects dosage clinical studies';
    } else if (query.type === 'academic') {
      enhanced += ' research study clinical trial pubmed';
    } else if (query.type === 'news') {
      enhanced += ' latest news recent developments';
    }

    return enhanced;
  }

  /**
   * Enhance query specifically for supplement research
   */
  private enhanceSupplementQuery(query: string): string {
    const supplementKeywords = [
      'supplement',
      'benefits',
      'side effects',
      'dosage',
      'clinical studies',
      'safety',
      'interactions',
      'efficacy',
      'research'
    ];

    return `${query} ${supplementKeywords.join(' ')}`;
  }

  /**
   * Process Brave search results
   */
  private processBraveResults(results: any[]): ResearchResult[] {
    return results.map(result => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.description || '',
      publishedDate: result.age || undefined,
      domain: this.extractDomain(result.url || ''),
      relevanceScore: this.calculateRelevance(result),
      medicalRelevance: this.calculateMedicalRelevance(result)
    }));
  }

  /**
   * Process Tavily search results
   */
  private processTavilyResults(results: any[]): ResearchResult[] {
    return results.map(result => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.content || '',
      content: result.raw_content || undefined,
      publishedDate: result.published_date || undefined,
      domain: this.extractDomain(result.url || ''),
      relevanceScore: result.score || 0.5,
      medicalRelevance: this.calculateMedicalRelevance(result)
    }));
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * Calculate relevance score
   */
  private calculateRelevance(result: any): number {
    let score = 0.5;
    
    // Boost score for medical domains
    const medicalDomains = ['pubmed', 'nih.gov', 'mayoclinic', 'webmd', 'examine.com'];
    if (medicalDomains.some(domain => result.url?.includes(domain))) {
      score += 0.3;
    }

    // Boost for recent content
    if (result.age && result.age.includes('day')) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate medical relevance score
   */
  private calculateMedicalRelevance(result: any): number {
    const medicalKeywords = [
      'clinical', 'study', 'research', 'trial', 'supplement', 'vitamin',
      'mineral', 'dosage', 'side effects', 'benefits', 'safety'
    ];

    const text = `${result.title} ${result.description || result.content || ''}`.toLowerCase();
    const matches = medicalKeywords.filter(keyword => text.includes(keyword));
    
    return matches.length / medicalKeywords.length;
  }

  /**
   * Remove duplicate results
   */
  private deduplicateResults(results: ResearchResult[]): ResearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = result.url;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Generate related queries
   */
  private generateRelatedQueries(query: string): string[] {
    const baseQuery = query.toLowerCase();
    const related = [
      `${baseQuery} benefits`,
      `${baseQuery} side effects`,
      `${baseQuery} dosage`,
      `${baseQuery} interactions`,
      `${baseQuery} clinical studies`
    ];

    return related.filter(q => q !== baseQuery);
  }

  /**
   * Gather comprehensive supplement research data
   */
  async gatherSupplementResearch(
    supplementName: string,
    options: {
      includeInteractions?: boolean;
      researchDepth?: 'basic' | 'comprehensive';
    } = {}
  ): Promise<any> {
    try {
      const query: ResearchQuery = {
        query: supplementName,
        type: 'supplement_specific',
        filters: {
          maxResults: options.researchDepth === 'comprehensive' ? 20 : 10,
          timeRange: 'year'
        }
      };

      const researchResults = await this.searchSupplements(query);

      // If interactions are requested, perform additional search
      let interactionData = null;
      if (options.includeInteractions) {
        const interactionQuery: ResearchQuery = {
          query: `${supplementName} drug interactions contraindications`,
          type: 'supplement_specific',
          filters: {
            maxResults: 10,
            domains: ['drugs.com', 'webmd.com', 'mayoclinic.org', 'nih.gov']
          }
        };

        const interactionResults = await this.searchSupplements(interactionQuery);
        interactionData = interactionResults.results;
      }

      return {
        supplementName,
        researchResults: researchResults.results,
        totalResults: researchResults.totalResults,
        searchTime: researchResults.searchTime,
        interactions: interactionData,
        relatedQueries: researchResults.relatedQueries,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error(`Failed to gather research for ${supplementName}:`, error);
      throw new Error(`Research gathering failed for ${supplementName}`);
    }
  }

  /**
   * Health check for research services
   */
  async healthCheck(): Promise<{ brave: boolean; tavily: boolean }> {
    const checks = await Promise.allSettled([
      this.braveClient.get('/web/search?q=test&count=1'),
      this.tavilyClient.post('/search', {
        api_key: process.env['TAVILY_API_KEY'],
        query: 'test',
        max_results: 1
      })
    ]);

    return {
      brave: checks[0].status === 'fulfilled',
      tavily: checks[1].status === 'fulfilled'
    };
  }
}
