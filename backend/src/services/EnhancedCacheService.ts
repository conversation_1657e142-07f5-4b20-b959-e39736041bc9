import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean; // Enable compression for large data
  tags?: string[]; // Cache tags for invalidation
  priority?: 'low' | 'medium' | 'high'; // Cache priority
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
  lastUpdated: Date;
}

export interface CacheMetrics {
  operationType: 'get' | 'set' | 'delete' | 'invalidate';
  key: string;
  hit: boolean;
  executionTime: number;
  dataSize?: number;
  timestamp: Date;
}

/**
 * Enhanced multi-layer caching service with intelligent strategies
 * L1: In-memory cache (fastest)
 * L2: Redis cache (persistent)
 * L3: Database fallback
 */
export class EnhancedCacheService {
  private redisClient: RedisClientType;
  private memoryCache: Map<string, { data: any; expires: number; priority: string }>;
  private cacheStats: CacheStats;
  private metrics: CacheMetrics[];
  private maxMemorySize: number;
  private compressionThreshold: number;

  constructor() {
    this.memoryCache = new Map();
    this.metrics = [];
    this.maxMemorySize = 100 * 1024 * 1024; // 100MB
    this.compressionThreshold = 1024; // 1KB
    this.cacheStats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalKeys: 0,
      memoryUsage: 0,
      lastUpdated: new Date()
    };
    this.initializeRedisClient();
    this.startCacheCleanup();
  }

  /**
   * Initialize Redis client with enhanced configuration
   */
  private async initializeRedisClient(): Promise<void> {
    try {
      this.redisClient = createClient({
        url: config.redis.url,
        socket: {
          connectTimeout: 5000,
          reconnectStrategy: (retries) => Math.min(retries * 50, 500)
        },
        // database: config.redis.database || 0 // config.redis does not have a database property
      });

      this.redisClient.on('error', (err) => {
        logger.error('Enhanced Redis Cache Error:', err);
      });

      this.redisClient.on('connect', () => {
        logger.info('✅ Enhanced Redis Cache connected');
      });

      await this.redisClient.connect();
    } catch (error) {
      logger.error('Failed to initialize Enhanced Redis Cache:', error);
    }
  }

  /**
   * Get data from cache with multi-layer strategy
   */
  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    const startTime = Date.now();
    let hit = false;
    let data: T | null = null;

    try {
      // L1: Check memory cache first
      const memoryData = this.getFromMemory<T>(key);
      if (memoryData !== null) {
        data = memoryData;
        hit = true;
        logger.debug(`Cache hit (L1): ${key}`);
      } else {
        // L2: Check Redis cache
        const redisData = await this.getFromRedis<T>(key);
        if (redisData !== null) {
          data = redisData;
          hit = true;
          // Promote to L1 cache
          this.setInMemory(key, data, options);
          logger.debug(`Cache hit (L2): ${key}`);
        }
      }

      // Update statistics
      if (hit) {
        this.cacheStats.hits++;
      } else {
        this.cacheStats.misses++;
      }

      this.updateCacheStats();
      this.recordMetric('get', key, hit, Date.now() - startTime);

      return data;
    } catch (error) {
      logger.error('Cache get error:', error, { key });
      this.cacheStats.misses++;
      return null;
    }
  }

  /**
   * Set data in cache with intelligent distribution
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now();
    const dataSize = this.calculateDataSize(data);

    try {
      const ttl = options.ttl || 3600; // Default 1 hour
      const priority = options.priority || 'medium';

      // Always set in Redis (L2)
      await this.setInRedis(key, data, ttl, options);

      // Set in memory (L1) based on priority and size
      if (this.shouldCacheInMemory(dataSize, priority)) {
        this.setInMemory(key, data, options);
      }

      // Handle cache tags for invalidation
      if (options.tags && options.tags.length > 0) {
        await this.addCacheTags(key, options.tags);
      }

      this.recordMetric('set', key, true, Date.now() - startTime, dataSize);
      logger.debug(`Cache set: ${key} (${dataSize} bytes)`);
    } catch (error) {
      logger.error('Cache set error:', error, { key });
    }
  }

  /**
   * Delete from all cache layers
   */
  async delete(key: string): Promise<void> {
    const startTime = Date.now();

    try {
      // Remove from memory cache
      this.memoryCache.delete(key);

      // Remove from Redis
      if (this.redisClient?.isReady) {
        await this.redisClient.del(key);
      }

      this.recordMetric('delete', key, true, Date.now() - startTime);
      logger.debug(`Cache deleted: ${key}`);
    } catch (error) {
      logger.error('Cache delete error:', error, { key });
    }
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    const startTime = Date.now();

    try {
      for (const tag of tags) {
        const keys = await this.getKeysByTag(tag);
        for (const key of keys) {
          await this.delete(key);
        }
        // Remove tag mapping
        if (this.redisClient?.isReady) {
          await this.redisClient.del(`tag:${tag}`);
        }
      }

      this.recordMetric('invalidate', `tags:${tags.join(',')}`, true, Date.now() - startTime);
      logger.info(`Cache invalidated by tags: ${tags.join(', ')}`);
    } catch (error) {
      logger.error('Cache invalidation error:', error, { tags });
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): CacheStats {
    this.updateCacheStats();
    return { ...this.cacheStats };
  }

  /**
   * Get cache metrics for monitoring
   */
  getCacheMetrics(limit: number = 100): CacheMetrics[] {
    return this.metrics.slice(-limit);
  }

  /**
   * Warm cache with frequently accessed data
   */
  async warmCache(warmupData: Array<{ key: string; data: any; options?: CacheOptions }>): Promise<void> {
    logger.info(`Starting cache warmup with ${warmupData.length} items`);

    const promises = warmupData.map(async ({ key, data, options }) => {
      try {
        await this.set(key, data, { ...options, priority: 'high' });
      } catch (error) {
        logger.warn(`Cache warmup failed for key: ${key}`, error);
      }
    });

    await Promise.allSettled(promises);
    logger.info('Cache warmup completed');
  }

  /**
   * Clear all caches
   */
  async clear(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear Redis cache
      if (this.redisClient?.isReady) {
        await this.redisClient.flushDb();
      }

      // Reset statistics
      this.cacheStats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalKeys: 0,
        memoryUsage: 0,
        lastUpdated: new Date()
      };

      logger.info('All caches cleared');
    } catch (error) {
      logger.error('Cache clear error:', error);
    }
  }

  // Private helper methods
  private getFromMemory<T>(key: string): T | null {
    const cached = this.memoryCache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    } else if (cached) {
      this.memoryCache.delete(key);
    }
    return null;
  }

  private async getFromRedis<T>(key: string): Promise<T | null> {
    if (!this.redisClient?.isReady) return null;

    try {
      const data = await this.redisClient.get(key);
      if (data) {
        return JSON.parse(data);
      }
    } catch (error) {
      logger.warn('Redis get error:', error, { key });
    }
    return null;
  }

  private setInMemory<T>(key: string, data: T, options: CacheOptions = {}): void {
    const ttl = (options.ttl || 3600) * 1000; // Convert to milliseconds
    const priority = options.priority || 'medium';
    
    // Check memory limits before adding
    if (this.getMemoryUsage() > this.maxMemorySize) {
      this.evictLowPriorityItems();
    }

    this.memoryCache.set(key, {
      data,
      expires: Date.now() + ttl,
      priority
    });
  }

  private async setInRedis<T>(key: string, data: T, ttl: number, options: CacheOptions = {}): Promise<void> {
    if (!this.redisClient?.isReady) return;

    try {
      let serializedData = JSON.stringify(data);
      
      // Compress large data if enabled
      if (options.compress && serializedData.length > this.compressionThreshold) {
        // Compression logic would go here
        logger.debug(`Compressing data for key: ${key}`);
      }

      await this.redisClient.setEx(key, ttl, serializedData);
    } catch (error) {
      logger.warn('Redis set error:', error, { key });
    }
  }

  private shouldCacheInMemory(dataSize: number, priority: string): boolean {
    // Don't cache very large items in memory
    if (dataSize > 1024 * 1024) return false; // 1MB limit
    
    // Always cache high priority items
    if (priority === 'high') return true;
    
    // Cache medium priority if memory usage is reasonable
    if (priority === 'medium' && this.getMemoryUsage() < this.maxMemorySize * 0.8) {
      return true;
    }
    
    return false;
  }

  private calculateDataSize(data: any): number {
    return JSON.stringify(data).length;
  }

  private getMemoryUsage(): number {
    let totalSize = 0;
    for (const [key, value] of this.memoryCache) {
      totalSize += key.length + this.calculateDataSize(value.data);
    }
    return totalSize;
  }

  private evictLowPriorityItems(): void {
    const lowPriorityKeys = Array.from(this.memoryCache.entries())
      .filter(([_, value]) => value.priority === 'low')
      .map(([key]) => key);

    lowPriorityKeys.forEach(key => this.memoryCache.delete(key));
    
    if (lowPriorityKeys.length > 0) {
      logger.debug(`Evicted ${lowPriorityKeys.length} low priority cache items`);
    }
  }

  private async addCacheTags(key: string, tags: string[]): Promise<void> {
    if (!this.redisClient?.isReady) return;

    try {
      for (const tag of tags) {
        await this.redisClient.sAdd(`tag:${tag}`, key);
      }
    } catch (error) {
      logger.warn('Failed to add cache tags:', error, { key, tags });
    }
  }

  private async getKeysByTag(tag: string): Promise<string[]> {
    if (!this.redisClient?.isReady) return [];

    try {
      return await this.redisClient.sMembers(`tag:${tag}`);
    } catch (error) {
      logger.warn('Failed to get keys by tag:', error, { tag });
      return [];
    }
  }

  private updateCacheStats(): void {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    this.cacheStats.hitRate = total > 0 ? (this.cacheStats.hits / total) * 100 : 0;
    this.cacheStats.totalKeys = this.memoryCache.size;
    this.cacheStats.memoryUsage = this.getMemoryUsage();
    this.cacheStats.lastUpdated = new Date();
  }

  private recordMetric(
    operationType: CacheMetrics['operationType'],
    key: string,
    hit: boolean,
    executionTime: number,
    dataSize?: number
  ): void {
    this.metrics.push({
      operationType,
      key,
      hit,
      executionTime,
      dataSize,
      timestamp: new Date()
    });

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  private startCacheCleanup(): void {
    // Clean expired items every 5 minutes
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of this.memoryCache) {
        if (value.expires <= now) {
          this.memoryCache.delete(key);
        }
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    try {
      if (this.redisClient?.isReady) {
        await this.redisClient.quit();
      }
      this.memoryCache.clear();
      logger.info('Enhanced Cache Service shutdown completed');
    } catch (error) {
      logger.error('Cache shutdown error:', error);
    }
  }
}

export const enhancedCacheService = new EnhancedCacheService();
