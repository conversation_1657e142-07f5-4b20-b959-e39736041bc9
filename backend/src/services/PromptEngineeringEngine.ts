import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';

export interface PromptTemplate {
  id: string;
  name: string;
  category: 'research' | 'analysis' | 'synthesis' | 'validation' | 'creative' | 'technical';
  template: string;
  variables: string[];
  description: string;
  examples: PromptExample[];
  performance: PromptPerformance;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

export interface PromptExample {
  input: Record<string, string>;
  expectedOutput: string;
  actualOutput?: string;
  quality?: number;
  timestamp?: Date;
}

export interface PromptPerformance {
  usageCount: number;
  averageQuality: number;
  averageConfidence: number;
  averageResponseTime: number;
  successRate: number;
  userRatings: number[];
  lastOptimized: Date;
}

export interface PromptOptimization {
  originalPrompt: string;
  optimizedPrompt: string;
  improvements: string[];
  expectedImprovement: number;
  testResults?: PromptTestResult[];
  confidence: number;
  reasoning: string;
}

export interface PromptTestResult {
  prompt: string;
  input: Record<string, string>;
  output: string;
  quality: number;
  confidence: number;
  responseTime: number;
  modelId: string;
}

export interface PromptContext {
  domain: string;
  taskType: string;
  userLevel: 'beginner' | 'intermediate' | 'expert';
  language: string;
  tone: 'formal' | 'casual' | 'technical' | 'friendly';
  outputFormat: 'text' | 'json' | 'markdown' | 'html';
  constraints: string[];
  requirements: string[];
}

/**
 * Intelligent Prompt Engineering Engine with self-optimization capabilities
 */
export class PromptEngineeringEngine extends EventEmitter {
  private templates: Map<string, PromptTemplate> = new Map();
  private optimizationHistory: Map<string, PromptOptimization[]> = new Map();
  private abTests: Map<string, any> = new Map();
  private feedbackData: Map<string, any[]> = new Map();

  constructor() {
    super();
    this.initializeBaseTemplates();
    this.startOptimizationLoop();
    logger.info('🎯 Prompt Engineering Engine initialized with self-optimization');
  }

  /**
   * Initialize base prompt templates
   */
  private initializeBaseTemplates(): void {
    const baseTemplates: Omit<PromptTemplate, 'performance' | 'createdAt' | 'updatedAt' | 'version'>[] = [
      {
        id: 'research_comprehensive',
        name: 'Comprehensive Research',
        category: 'research',
        template: `You are an expert researcher conducting a comprehensive analysis on {{topic}}.

Context: {{context}}
Research Focus: {{focus}}
Target Audience: {{audience}}

Please provide a thorough research analysis that includes:
1. Key findings and insights
2. Supporting evidence and sources
3. Potential implications
4. Areas for further investigation

Requirements:
- Use credible sources and evidence
- Maintain objectivity and balance
- Provide clear, actionable insights
- Format findings in a structured manner

Research Query: {{query}}`,
        variables: ['topic', 'context', 'focus', 'audience', 'query'],
        description: 'Comprehensive research template for in-depth analysis',
        examples: [
          {
            input: {
              topic: 'Vitamin D supplementation',
              context: 'Health and nutrition research',
              focus: 'Immune system benefits',
              audience: 'Healthcare professionals',
              query: 'What are the evidence-based benefits of vitamin D for immune function?'
            },
            expectedOutput: 'Structured research analysis with evidence-based findings'
          }
        ],
        metadata: { difficulty: 'intermediate', estimatedTime: '5-10 minutes' }
      },
      {
        id: 'analysis_critical',
        name: 'Critical Analysis',
        category: 'analysis',
        template: `Perform a critical analysis of the following content with focus on {{analysisType}}.

Content to Analyze: {{content}}
Analysis Framework: {{framework}}
Key Questions: {{questions}}

Please provide:
1. Strengths and weaknesses
2. Logical consistency
3. Evidence quality
4. Potential biases or limitations
5. Alternative perspectives
6. Conclusions and recommendations

Maintain analytical rigor and provide specific examples to support your assessment.`,
        variables: ['analysisType', 'content', 'framework', 'questions'],
        description: 'Critical analysis template for evaluating content quality',
        examples: [
          {
            input: {
              analysisType: 'scientific validity',
              content: 'Research paper on supplement efficacy',
              framework: 'Evidence-based medicine',
              questions: 'Is the methodology sound? Are conclusions supported?'
            },
            expectedOutput: 'Detailed critical analysis with specific assessments'
          }
        ],
        metadata: { difficulty: 'advanced', estimatedTime: '10-15 minutes' }
      },
      {
        id: 'synthesis_comprehensive',
        name: 'Knowledge Synthesis',
        category: 'synthesis',
        template: `Synthesize the following information into a coherent {{outputType}}.

Source Materials: {{sources}}
Synthesis Goal: {{goal}}
Target Format: {{format}}
Key Themes: {{themes}}

Create a synthesis that:
1. Integrates key insights from all sources
2. Identifies patterns and connections
3. Resolves conflicts or contradictions
4. Provides new understanding or perspectives
5. Maintains accuracy and attribution

Focus on creating value through integration rather than simple summarization.`,
        variables: ['outputType', 'sources', 'goal', 'format', 'themes'],
        description: 'Synthesis template for combining multiple information sources',
        examples: [
          {
            input: {
              outputType: 'comprehensive report',
              sources: 'Multiple research papers on nutrition',
              goal: 'Create unified understanding',
              format: 'structured report',
              themes: 'efficacy, safety, interactions'
            },
            expectedOutput: 'Integrated synthesis with new insights'
          }
        ],
        metadata: { difficulty: 'advanced', estimatedTime: '15-20 minutes' }
      },
      {
        id: 'validation_factual',
        name: 'Fact Validation',
        category: 'validation',
        template: `Validate the factual accuracy of the following claims using rigorous fact-checking methods.

Claims to Validate: {{claims}}
Validation Criteria: {{criteria}}
Required Evidence Level: {{evidenceLevel}}

For each claim, provide:
1. Verification status (Verified/Partially Verified/Unverified/False)
2. Supporting evidence and sources
3. Confidence level (0-100%)
4. Limitations or caveats
5. Alternative interpretations if applicable

Maintain strict standards for evidence quality and source credibility.`,
        variables: ['claims', 'criteria', 'evidenceLevel'],
        description: 'Fact validation template for verifying information accuracy',
        examples: [
          {
            input: {
              claims: 'Vitamin C prevents common cold',
              criteria: 'Peer-reviewed research',
              evidenceLevel: 'High-quality RCTs'
            },
            expectedOutput: 'Detailed fact-check with evidence assessment'
          }
        ],
        metadata: { difficulty: 'expert', estimatedTime: '10-20 minutes' }
      }
    ];

    baseTemplates.forEach(template => {
      this.templates.set(template.id, {
        ...template,
        performance: {
          usageCount: 0,
          averageQuality: 0,
          averageConfidence: 0,
          averageResponseTime: 0,
          successRate: 0,
          userRatings: [],
          lastOptimized: new Date()
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      });
    });
  }

  /**
   * Generate optimized prompt based on context and requirements
   */
  public async generateOptimizedPrompt(
    basePrompt: string,
    context: PromptContext,
    variables: Record<string, string> = {}
  ): Promise<string> {
    const metricId = performanceMonitoringService.trackAIOperation('generate_prompt', 'optimization');

    try {
      // Check cache first
      const cacheKey = `optimized_prompt:${this.hashPromptContext(basePrompt, context)}`;
      const cached = await enhancedCacheService.get<string>(cacheKey);

      if (cached) {
        performanceMonitoringService.endMetric(metricId, true);
        return this.interpolateVariables(cached, variables);
      }

      // Find best matching template
      const template = await this.findBestTemplate(context);

      // Optimize prompt based on context
      const optimizedPrompt = await this.optimizePrompt(
        template ? template.template : basePrompt,
        context
      );

      // Cache result
      await enhancedCacheService.set(cacheKey, optimizedPrompt, {
        ttl: 3600,
        tags: ['optimized_prompts', context.taskType, context.domain]
      });

      performanceMonitoringService.endMetric(metricId, true);

      return this.interpolateVariables(optimizedPrompt, variables);
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Prompt optimization failed');
      throw error;
    }
  }

  /**
   * Find best matching template for given context
   */
  private async findBestTemplate(context: PromptContext): Promise<PromptTemplate | null> {
    const templates = Array.from(this.templates.values());

    // Score templates based on context match
    const scoredTemplates = templates.map(template => {
      let score = 0;

      // Category match
      if (template.category === context.taskType) {
        score += 50;
      }

      // Performance score
      score += template.performance.averageQuality * 0.3;
      score += template.performance.successRate * 0.2;

      // Usage frequency (popular templates get slight boost)
      if (template.performance.usageCount > 10) {
        score += 10;
      }

      return { template, score };
    });

    // Sort by score and return best match
    scoredTemplates.sort((a, b) => b.score - a.score);

    return scoredTemplates.length > 0 ? scoredTemplates[0].template : null;
  }

  /**
   * Optimize prompt based on context and best practices
   */
  private async optimizePrompt(basePrompt: string, context: PromptContext): Promise<string> {
    let optimizedPrompt = basePrompt;

    // Apply context-specific optimizations
    optimizedPrompt = this.applyToneOptimization(optimizedPrompt, context.tone);
    optimizedPrompt = this.applyFormatOptimization(optimizedPrompt, context.outputFormat);
    optimizedPrompt = this.applyConstraintOptimization(optimizedPrompt, context.constraints);
    optimizedPrompt = this.applyRequirementOptimization(optimizedPrompt, context.requirements);
    optimizedPrompt = this.applyUserLevelOptimization(optimizedPrompt, context.userLevel);

    // Apply general best practices
    optimizedPrompt = this.applyBestPractices(optimizedPrompt);

    return optimizedPrompt;
  }

  /**
   * Apply tone-specific optimizations
   */
  private applyToneOptimization(prompt: string, tone: string): string {
    const toneInstructions = {
      formal: 'Maintain a professional and formal tone throughout your response.',
      casual: 'Use a conversational and approachable tone.',
      technical: 'Use precise technical language appropriate for experts.',
      friendly: 'Adopt a warm and helpful tone while remaining informative.'
    };

    const instruction = toneInstructions[tone as keyof typeof toneInstructions];
    if (instruction && !prompt.includes(instruction)) {
      return `${prompt}\n\nTone: ${instruction}`;
    }

    return prompt;
  }

  /**
   * Apply output format optimizations
   */
  private applyFormatOptimization(prompt: string, format: string): string {
    const formatInstructions = {
      json: 'Provide your response in valid JSON format.',
      markdown: 'Format your response using Markdown syntax for better readability.',
      html: 'Structure your response using appropriate HTML tags.',
      text: 'Provide a clear, well-structured text response.'
    };

    const instruction = formatInstructions[format as keyof typeof formatInstructions];
    if (instruction && !prompt.toLowerCase().includes(format)) {
      return `${prompt}\n\nOutput Format: ${instruction}`;
    }

    return prompt;
  }

  /**
   * Apply constraint optimizations
   */
  private applyConstraintOptimization(prompt: string, constraints: string[]): string {
    if (constraints.length === 0) return prompt;

    const constraintText = constraints.map(c => `- ${c}`).join('\n');
    return `${prompt}\n\nConstraints:\n${constraintText}`;
  }

  /**
   * Apply requirement optimizations
   */
  private applyRequirementOptimization(prompt: string, requirements: string[]): string {
    if (requirements.length === 0) return prompt;

    const requirementText = requirements.map(r => `- ${r}`).join('\n');
    return `${prompt}\n\nRequirements:\n${requirementText}`;
  }

  /**
   * Apply user level optimizations
   */
  private applyUserLevelOptimization(prompt: string, userLevel: string): string {
    const levelInstructions = {
      beginner: 'Explain concepts clearly and avoid excessive jargon. Provide context for technical terms.',
      intermediate: 'Assume moderate familiarity with the topic. Balance detail with accessibility.',
      expert: 'Use appropriate technical language and focus on advanced insights and nuances.'
    };

    const instruction = levelInstructions[userLevel as keyof typeof levelInstructions];
    if (instruction) {
      return `${prompt}\n\nAudience Level: ${instruction}`;
    }

    return prompt;
  }

  /**
   * Apply general best practices
   */
  private applyBestPractices(prompt: string): string {
    let optimized = prompt;

    // Ensure clear structure
    if (!optimized.includes('Please provide') && !optimized.includes('Your task is')) {
      optimized = `Your task is to ${optimized}`;
    }

    // Add thinking instruction for complex tasks
    if (optimized.length > 500 && !optimized.includes('think step by step')) {
      optimized = `${optimized}\n\nPlease think step by step and provide a thorough response.`;
    }

    // Ensure specific output request
    if (!optimized.includes('provide') && !optimized.includes('generate') && !optimized.includes('create')) {
      optimized = `${optimized}\n\nPlease provide a comprehensive response.`;
    }

    return optimized;
  }

  /**
   * Interpolate variables in prompt template
   */
  private interpolateVariables(prompt: string, variables: Record<string, string>): string {
    let result = prompt;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    });

    // Remove any remaining unmatched variables
    result = result.replace(/{{[^}]+}}/g, '[VARIABLE_NOT_PROVIDED]');

    return result;
  }

  /**
   * Test prompt performance with A/B testing
   */
  public async testPromptPerformance(
    promptA: string,
    promptB: string,
    testInputs: Record<string, string>[],
    modelId: string
  ): Promise<{ winner: 'A' | 'B' | 'tie'; results: any }> {
    const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      const resultsA: PromptTestResult[] = [];
      const resultsB: PromptTestResult[] = [];

      // Test both prompts with same inputs
      for (const input of testInputs) {
        const [resultA, resultB] = await Promise.all([
          this.executePromptTest(promptA, input, modelId),
          this.executePromptTest(promptB, input, modelId)
        ]);

        resultsA.push(resultA);
        resultsB.push(resultB);
      }

      // Calculate average scores
      const avgQualityA = resultsA.reduce((sum, r) => sum + r.quality, 0) / resultsA.length;
      const avgQualityB = resultsB.reduce((sum, r) => sum + r.quality, 0) / resultsB.length;
      const avgConfidenceA = resultsA.reduce((sum, r) => sum + r.confidence, 0) / resultsA.length;
      const avgConfidenceB = resultsB.reduce((sum, r) => sum + r.confidence, 0) / resultsB.length;

      // Determine winner
      const scoreA = (avgQualityA * 0.7) + (avgConfidenceA * 0.3);
      const scoreB = (avgQualityB * 0.7) + (avgConfidenceB * 0.3);

      let winner: 'A' | 'B' | 'tie';
      if (Math.abs(scoreA - scoreB) < 5) {
        winner = 'tie';
      } else {
        winner = scoreA > scoreB ? 'A' : 'B';
      }

      const results = {
        testId,
        promptA: { avgQuality: avgQualityA, avgConfidence: avgConfidenceA, score: scoreA },
        promptB: { avgQuality: avgQualityB, avgConfidence: avgConfidenceB, score: scoreB },
        detailedResults: { resultsA, resultsB },
        winner,
        improvement: Math.abs(scoreA - scoreB)
      };

      // Store test results
      this.abTests.set(testId, results);

      this.emit('ab_test_completed', { testId, results });

      return { winner, results };
    } catch (error) {
      logger.error('A/B test failed:', error);
      throw error;
    }
  }

  /**
   * Execute single prompt test
   */
  private async executePromptTest(
    prompt: string,
    input: Record<string, string>,
    modelId: string
  ): Promise<PromptTestResult> {
    const startTime = Date.now();

    // This would integrate with the actual AI service
    // For now, we'll simulate the response
    const simulatedOutput = `Test response for prompt: ${prompt.substring(0, 50)}...`;
    const responseTime = Date.now() - startTime;

    return {
      prompt,
      input,
      output: simulatedOutput,
      quality: Math.random() * 40 + 60, // Simulate 60-100 quality
      confidence: Math.random() * 30 + 70, // Simulate 70-100 confidence
      responseTime,
      modelId
    };
  }

  /**
   * Record feedback for prompt performance
   */
  public recordFeedback(
    promptId: string,
    rating: number,
    feedback: string,
    metadata: Record<string, any> = {}
  ): void {
    if (!this.feedbackData.has(promptId)) {
      this.feedbackData.set(promptId, []);
    }

    this.feedbackData.get(promptId)!.push({
      rating,
      feedback,
      metadata,
      timestamp: new Date()
    });

    // Update template performance if it exists
    const template = this.templates.get(promptId);
    if (template) {
      template.performance.userRatings.push(rating);
      template.updatedAt = new Date();
    }

    this.emit('feedback_recorded', { promptId, rating, feedback });
  }

  /**
   * Get prompt analytics
   */
  public getPromptAnalytics(promptId?: string): any {
    if (promptId) {
      const template = this.templates.get(promptId);
      const feedback = this.feedbackData.get(promptId) || [];

      return {
        template,
        feedback,
        analytics: {
          totalUsage: template?.performance.usageCount || 0,
          averageRating: feedback.length > 0 ?
            feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : 0,
          lastUsed: template?.performance.lastOptimized
        }
      };
    }

    // Return overall analytics
    const templates = Array.from(this.templates.values());
    const totalUsage = templates.reduce((sum, t) => sum + t.performance.usageCount, 0);
    const avgQuality = templates.reduce((sum, t) => sum + t.performance.averageQuality, 0) / templates.length;

    return {
      totalTemplates: templates.length,
      totalUsage,
      averageQuality: avgQuality,
      topPerformers: templates
        .sort((a, b) => b.performance.averageQuality - a.performance.averageQuality)
        .slice(0, 5)
        .map(t => ({ id: t.id, name: t.name, quality: t.performance.averageQuality }))
    };
  }

  // Helper methods
  private hashPromptContext(prompt: string, context: PromptContext): string {
    const key = `${prompt}_${context.taskType}_${context.domain}_${context.tone}_${context.outputFormat}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  }

  private startOptimizationLoop(): void {
    // Run optimization analysis every hour
    setInterval(() => {
      this.analyzeAndOptimizeTemplates();
    }, 3600000);
  }

  private async analyzeAndOptimizeTemplates(): Promise<void> {
    try {
      for (const [templateId, template] of this.templates) {
        // Only optimize templates with sufficient usage data
        if (template.performance.usageCount > 10) {
          const feedback = this.feedbackData.get(templateId) || [];

          // Check if optimization is needed
          const avgRating = feedback.length > 0 ?
            feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : 0;

          if (avgRating < 7 || template.performance.averageQuality < 80) {
            logger.info(`Scheduling optimization for template: ${template.name}`);
            // Schedule optimization (would be implemented with actual AI optimization)
          }
        }
      }
    } catch (error) {
      logger.error('Template optimization analysis failed:', error);
    }
  }

  // Public API methods
  public getTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  public getTemplate(id: string): PromptTemplate | null {
    return this.templates.get(id) || null;
  }

  public async createTemplate(template: Omit<PromptTemplate, 'performance' | 'createdAt' | 'updatedAt' | 'version'>): Promise<string> {
    const newTemplate: PromptTemplate = {
      ...template,
      performance: {
        usageCount: 0,
        averageQuality: 0,
        averageConfidence: 0,
        averageResponseTime: 0,
        successRate: 0,
        userRatings: [],
        lastOptimized: new Date()
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };

    this.templates.set(template.id, newTemplate);

    this.emit('template_created', { templateId: template.id });

    return template.id;
  }
}

export default PromptEngineeringEngine;