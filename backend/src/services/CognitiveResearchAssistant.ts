import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';
import EnhancedAIService from './EnhancedAIService';
import PromptEngineeringEngine, { PromptContext } from './PromptEngineeringEngine';
import AdaptiveResearchManager from './AdaptiveResearchManager';

export interface UserIntent {
  type: 'research' | 'analysis' | 'synthesis' | 'validation' | 'exploration' | 'comparison';
  confidence: number;
  entities: string[];
  keywords: string[];
  domain: string;
  complexity: 'beginner' | 'intermediate' | 'expert';
  timeframe?: string;
  scope: 'narrow' | 'broad' | 'comprehensive';
}

export interface ConversationContext {
  userId: string;
  sessionId: string;
  conversationHistory: ConversationTurn[];
  userProfile: UserProfile;
  currentTopic?: string;
  activeResearch?: string[];
  preferences: UserPreferences;
  learningData: LearningData;
}

export interface ConversationTurn {
  id: string;
  timestamp: Date;
  userMessage: string;
  assistantResponse: string;
  intent: UserIntent;
  actions: AssistantAction[];
  feedback?: UserFeedback;
  metadata: Record<string, any>;
}

export interface UserProfile {
  userId: string;
  expertise: Record<string, 'beginner' | 'intermediate' | 'expert'>;
  interests: string[];
  researchHistory: string[];
  preferredComplexity: 'beginner' | 'intermediate' | 'expert';
  communicationStyle: 'concise' | 'detailed' | 'technical' | 'friendly';
  learningGoals: string[];
  lastActive: Date;
}

export interface UserPreferences {
  responseLength: 'short' | 'medium' | 'long';
  includeSourceLinks: boolean;
  explanationLevel: 'basic' | 'intermediate' | 'advanced';
  visualizations: boolean;
  realTimeUpdates: boolean;
  collaborativeMode: boolean;
}

export interface LearningData {
  successfulQueries: string[];
  challengingTopics: string[];
  preferredSources: string[];
  feedbackPatterns: Record<string, number>;
  improvementAreas: string[];
  lastLearningUpdate: Date;
}

export interface AssistantAction {
  type: 'research' | 'analysis' | 'synthesis' | 'recommendation' | 'clarification' | 'learning';
  description: string;
  parameters: Record<string, any>;
  result?: any;
  confidence: number;
  timestamp: Date;
}

export interface UserFeedback {
  rating: number; // 1-5
  helpful: boolean;
  accurate: boolean;
  complete: boolean;
  comments?: string;
  suggestions?: string[];
  timestamp: Date;
}

export interface ResearchRecommendation {
  type: 'topic_expansion' | 'methodology_improvement' | 'source_suggestion' | 'analysis_deepening';
  title: string;
  description: string;
  reasoning: string;
  confidence: number;
  priority: 'low' | 'medium' | 'high';
  estimatedValue: number;
  actionable: boolean;
}

/**
 * Cognitive Research Assistant with natural language understanding and learning capabilities
 */
export class CognitiveResearchAssistant extends EventEmitter {
  private aiService: EnhancedAIService;
  private promptEngine: PromptEngineeringEngine;
  private strategyManager: AdaptiveResearchManager;
  private userContexts: Map<string, ConversationContext> = new Map();
  private intentClassifier: Map<string, any> = new Map();
  private learningEngine: Map<string, any> = new Map();

  constructor(
    aiService: EnhancedAIService,
    promptEngine: PromptEngineeringEngine,
    strategyManager: AdaptiveResearchManager
  ) {
    super();
    this.aiService = aiService;
    this.promptEngine = promptEngine;
    this.strategyManager = strategyManager;
    this.initializeIntentClassifier();
    this.startLearningLoop();
    logger.info('🧠 Cognitive Research Assistant initialized with learning capabilities');
  }

  /**
   * Initialize intent classification patterns
   */
  private initializeIntentClassifier(): void {
    const intentPatterns = {
      research: [
        'research', 'find information', 'look up', 'investigate', 'study', 'explore',
        'what is', 'tell me about', 'information on', 'details about'
      ],
      analysis: [
        'analyze', 'examine', 'evaluate', 'assess', 'compare', 'contrast',
        'pros and cons', 'advantages', 'disadvantages', 'strengths', 'weaknesses'
      ],
      synthesis: [
        'summarize', 'combine', 'integrate', 'synthesize', 'merge', 'consolidate',
        'bring together', 'overall picture', 'comprehensive view'
      ],
      validation: [
        'verify', 'check', 'validate', 'confirm', 'fact-check', 'is it true',
        'accurate', 'reliable', 'trustworthy', 'evidence'
      ],
      exploration: [
        'explore', 'discover', 'brainstorm', 'possibilities', 'alternatives',
        'what if', 'potential', 'opportunities', 'new ideas'
      ],
      comparison: [
        'compare', 'versus', 'vs', 'difference', 'similar', 'better',
        'which is', 'choose between', 'options'
      ]
    };

    this.intentClassifier.set('patterns', intentPatterns);
  }

  /**
   * Process user message and generate intelligent response
   */
  public async processMessage(
    userId: string,
    sessionId: string,
    message: string,
    context?: Partial<ConversationContext>
  ): Promise<{
    response: string;
    actions: AssistantAction[];
    recommendations: ResearchRecommendation[];
    intent: UserIntent;
  }> {
    const metricId = performanceMonitoringService.trackAIOperation('process_message', 'cognitive_assistant');

    try {
      // Get or create conversation context
      const conversationContext = await this.getOrCreateContext(userId, sessionId, context);

      // Understand user intent
      const intent = await this.classifyIntent(message, conversationContext);

      // Generate contextual response
      const response = await this.generateContextualResponse(message, intent, conversationContext);

      // Determine and execute actions
      const actions = await this.determineActions(intent, conversationContext);

      // Generate recommendations
      const recommendations = await this.generateRecommendations(intent, conversationContext);

      // Update conversation history
      const turn: ConversationTurn = {
        id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        userMessage: message,
        assistantResponse: response,
        intent,
        actions,
        metadata: { sessionId, processingTime: Date.now() }
      };

      conversationContext.conversationHistory.push(turn);

      // Update user learning data
      await this.updateLearningData(conversationContext, intent, actions);

      // Cache context
      await this.cacheContext(conversationContext);

      performanceMonitoringService.endMetric(metricId, true);

      this.emit('message_processed', {
        userId,
        sessionId,
        intent,
        actionsCount: actions.length,
        recommendationsCount: recommendations.length
      });

      return { response, actions, recommendations, intent };
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Message processing failed');
      throw error;
    }
  }

  /**
   * Classify user intent from message
   */
  private async classifyIntent(message: string, context: ConversationContext): Promise<UserIntent> {
    const patterns = this.intentClassifier.get('patterns');
    const messageLower = message.toLowerCase();

    // Score each intent type
    const intentScores: Record<string, number> = {};

    for (const [intentType, keywords] of Object.entries(patterns)) {
      let score = 0;
      (keywords as string[]).forEach(keyword => {
        if (messageLower.includes(keyword)) {
          score += keyword.length; // Longer keywords get higher weight
        }
      });
      intentScores[intentType] = score;
    }

    // Find highest scoring intent
    const topIntent = Object.entries(intentScores).reduce((a, b) => a[1] > b[1] ? a : b);

    // Extract entities and keywords
    const entities = this.extractEntities(message);
    const keywords = this.extractKeywords(message);
    const domain = await this.identifyDomain(message, context);
    const complexity = this.assessComplexity(message, context);
    const scope = this.determineScope(message);

    return {
      type: topIntent[0] as UserIntent['type'],
      confidence: Math.min(topIntent[1] * 10, 100), // Convert to percentage
      entities,
      keywords,
      domain,
      complexity,
      scope
    };
  }

  /**
   * Generate contextual response based on intent and history
   */
  private async generateContextualResponse(
    message: string,
    intent: UserIntent,
    context: ConversationContext
  ): Promise<string> {
    // Build context-aware prompt
    const promptContext = {
      domain: intent.domain,
      taskType: intent.type,
      userLevel: context.userProfile.preferredComplexity,
      language: 'english',
      tone: (context.userProfile.communicationStyle === 'technical' ? 'technical' : 'friendly') as 'technical' | 'friendly',
      outputFormat: 'text' as PromptContext['outputFormat'],
      constraints: [] as any[],
      requirements: [
        'Be helpful and informative',
        'Consider conversation history',
        'Adapt to user expertise level',
        'Provide actionable insights'
      ]
    };

    // Add conversation history context
    const recentHistory = context.conversationHistory.slice(-3);
    const historyContext = recentHistory.map(turn =>
      `User: ${turn.userMessage}\nAssistant: ${turn.assistantResponse}`
    ).join('\n\n');

    const variables = {
      user_message: message,
      intent_type: intent.type,
      domain: intent.domain,
      entities: intent.entities.join(', '),
      keywords: intent.keywords.join(', '),
      conversation_history: historyContext,
      user_expertise: context.userProfile.preferredComplexity,
      communication_style: context.userProfile.communicationStyle
    };

    // Generate optimized prompt
    const optimizedPrompt = await this.promptEngine.generateOptimizedPrompt(
      this.getBasePromptForIntent(intent.type),
      promptContext,
      variables
    );

    // Generate response using AI service
    let aiTaskType: 'research' | 'analysis' | 'synthesis' | 'validation' | 'technical' | 'creative';
    if (intent.type === 'exploration') {
      aiTaskType = 'creative';
    } else if (intent.type === 'comparison') {
      aiTaskType = 'analysis';
    } else {
      aiTaskType = intent.type;
    }

    const aiResponse = await this.aiService.generateResponse(optimizedPrompt, {
      taskType: aiTaskType,
      priority: 'medium',
      requiresFactualAccuracy: intent.type === 'validation' || intent.type === 'research',
      requiresCreativity: intent.type === 'exploration',
      domain: intent.domain,
      userId: context.userId,
      sessionId: context.sessionId
    });

    return aiResponse.finalResponse;
  }

  /**
   * Determine actions to take based on intent
   */
  private async determineActions(intent: UserIntent, context: ConversationContext): Promise<AssistantAction[]> {
    const actions: AssistantAction[] = [];

    // Research action
    if (intent.type === 'research' || intent.type === 'exploration') {
      actions.push({
        type: 'research',
        description: `Conduct ${intent.type} on ${intent.entities.join(', ')}`,
        parameters: {
          entities: intent.entities,
          keywords: intent.keywords,
          domain: intent.domain,
          complexity: intent.complexity
        },
        confidence: intent.confidence,
        timestamp: new Date()
      });
    }

    // Analysis action
    if (intent.type === 'analysis' || intent.type === 'comparison') {
      actions.push({
        type: 'analysis',
        description: `Perform ${intent.type} analysis`,
        parameters: {
          analysisType: intent.type,
          entities: intent.entities,
          domain: intent.domain
        },
        confidence: intent.confidence,
        timestamp: new Date()
      });
    }

    // Learning action (always included for continuous improvement)
    actions.push({
      type: 'learning',
      description: 'Update user learning profile',
      parameters: {
        intent,
        userProfile: context.userProfile,
        conversationTurn: context.conversationHistory.length
      },
      confidence: 100,
      timestamp: new Date()
    });

    return actions;
  }

  /**
   * Generate intelligent recommendations
   */
  private async generateRecommendations(
    intent: UserIntent,
    context: ConversationContext
  ): Promise<ResearchRecommendation[]> {
    const recommendations: ResearchRecommendation[] = [];

    // Topic expansion recommendation
    if (intent.scope === 'narrow' && intent.entities.length > 0) {
      recommendations.push({
        type: 'topic_expansion',
        title: 'Expand Research Scope',
        description: `Consider exploring related topics to ${intent.entities[0]}`,
        reasoning: 'Broader research often reveals valuable connections and insights',
        confidence: 75,
        priority: 'medium',
        estimatedValue: 80,
        actionable: true
      });
    }

    // Methodology improvement
    if (context.userProfile.preferredComplexity === 'beginner' && intent.complexity === 'expert') {
      recommendations.push({
        type: 'methodology_improvement',
        title: 'Simplify Research Approach',
        description: 'Break down complex topics into smaller, manageable parts',
        reasoning: 'Matches your current expertise level for better understanding',
        confidence: 85,
        priority: 'high',
        estimatedValue: 90,
        actionable: true
      });
    }

    // Source suggestion
    if (intent.type === 'validation' || intent.type === 'research') {
      recommendations.push({
        type: 'source_suggestion',
        title: 'Diversify Information Sources',
        description: 'Include academic papers, expert opinions, and recent studies',
        reasoning: 'Multiple source types provide comprehensive perspective',
        confidence: 80,
        priority: 'medium',
        estimatedValue: 85,
        actionable: true
      });
    }

    return recommendations;
  }

  /**
   * Update user learning data based on interaction
   */
  private async updateLearningData(
    context: ConversationContext,
    intent: UserIntent,
    actions: AssistantAction[]
  ): Promise<void> {
    const learningData = context.learningData;

    // Track successful queries
    if (intent.confidence > 70) {
      learningData.successfulQueries.push(intent.entities.join(' '));
    }

    // Identify challenging topics
    if (intent.complexity === 'expert') {
      learningData.challengingTopics.push(intent.domain);
    }

    // Update domain expertise
    if (intent.domain in context.userProfile.expertise) {
      // Gradually increase expertise based on successful interactions
      const currentLevel = context.userProfile.expertise[intent.domain];
      if (intent.confidence > 80 && actions.length > 0) {
        if (currentLevel === 'beginner') {
          context.userProfile.expertise[intent.domain] = 'intermediate';
        } else if (currentLevel === 'intermediate') {
          context.userProfile.expertise[intent.domain] = 'expert';
        }
      }
    } else {
      context.userProfile.expertise[intent.domain] = 'beginner';
    }

    learningData.lastLearningUpdate = new Date();
  }

  // Helper methods
  private async getOrCreateContext(
    userId: string,
    sessionId: string,
    partialContext?: Partial<ConversationContext>
  ): Promise<ConversationContext> {
    const contextKey = `${userId}_${sessionId}`;

    if (this.userContexts.has(contextKey)) {
      return this.userContexts.get(contextKey)!;
    }

    // Try to load from cache
    const cached = await enhancedCacheService.get<ConversationContext>(`context:${contextKey}`);
    if (cached) {
      this.userContexts.set(contextKey, cached);
      return cached;
    }

    // Create new context
    const newContext: ConversationContext = {
      userId,
      sessionId,
      conversationHistory: [],
      userProfile: {
        userId,
        expertise: {},
        interests: [],
        researchHistory: [],
        preferredComplexity: 'intermediate',
        communicationStyle: 'friendly',
        learningGoals: [],
        lastActive: new Date()
      },
      preferences: {
        responseLength: 'medium',
        includeSourceLinks: true,
        explanationLevel: 'intermediate',
        visualizations: false,
        realTimeUpdates: true,
        collaborativeMode: false
      },
      learningData: {
        successfulQueries: [],
        challengingTopics: [],
        preferredSources: [],
        feedbackPatterns: {},
        improvementAreas: [],
        lastLearningUpdate: new Date()
      },
      ...partialContext
    };

    this.userContexts.set(contextKey, newContext);
    return newContext;
  }

  private async cacheContext(context: ConversationContext): Promise<void> {
    const contextKey = `${context.userId}_${context.sessionId}`;
    await enhancedCacheService.set(`context:${contextKey}`, context, {
      ttl: 7200, // 2 hours
      tags: ['user_contexts', context.userId]
    });
  }

  private extractEntities(message: string): string[] {
    // Simple entity extraction - in production, use NLP library
    const words = message.toLowerCase().split(/\s+/);
    const entities: string[] = [];

    // Look for capitalized words (potential entities)
    const originalWords = message.split(/\s+/);
    originalWords.forEach(word => {
      if (word.length > 2 && /^[A-Z]/.test(word)) {
        entities.push(word);
      }
    });

    // Look for quoted phrases
    const quotedMatches = message.match(/"([^"]+)"/g);
    if (quotedMatches) {
      entities.push(...quotedMatches.map(match => match.replace(/"/g, '')));
    }

    return [...new Set(entities)]; // Remove duplicates
  }

  private extractKeywords(message: string): string[] {
    // Simple keyword extraction
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);

    const words = message.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));

    return [...new Set(words)];
  }

  private async identifyDomain(message: string, context: ConversationContext): Promise<string> {
    // Simple domain identification based on keywords
    const domainKeywords = {
      health: ['health', 'medical', 'medicine', 'vitamin', 'supplement', 'nutrition', 'diet', 'wellness'],
      technology: ['technology', 'software', 'computer', 'AI', 'machine learning', 'programming', 'code'],
      science: ['research', 'study', 'experiment', 'scientific', 'biology', 'chemistry', 'physics'],
      business: ['business', 'marketing', 'finance', 'economics', 'management', 'strategy'],
      education: ['education', 'learning', 'teaching', 'school', 'university', 'course']
    };

    const messageLower = message.toLowerCase();
    let bestDomain = 'general';
    let bestScore = 0;

    for (const [domain, keywords] of Object.entries(domainKeywords)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (messageLower.includes(keyword) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestDomain = domain;
      }
    }

    return bestDomain;
  }

  private assessComplexity(message: string, context: ConversationContext): UserIntent['complexity'] {
    const complexityIndicators = {
      beginner: ['what is', 'simple', 'basic', 'easy', 'quick'],
      intermediate: ['explain', 'how does', 'compare', 'analyze'],
      expert: ['comprehensive', 'detailed', 'in-depth', 'thorough', 'advanced', 'technical', 'scientific', 'research', 'methodology', 'statistical']
    };

    const messageLower = message.toLowerCase();
    let bestComplexity: UserIntent['complexity'] = 'intermediate';
    let bestScore = 0;

    for (const [complexity, indicators] of Object.entries(complexityIndicators)) {
      const score = indicators.reduce((sum, indicator) => {
        return sum + (messageLower.includes(indicator) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestComplexity = complexity as UserIntent['complexity'];
      }
    }

    // Consider user's preferred complexity if no strong indicator found
    if (bestScore === 0) {
      return context.userProfile.preferredComplexity;
    }

    return bestComplexity;
  }

  private determineScope(message: string): UserIntent['scope'] {
    const scopeIndicators = {
      narrow: ['specific', 'particular', 'exact', 'precise', 'focused'],
      broad: ['general', 'overview', 'broad', 'wide', 'extensive'],
      comprehensive: ['comprehensive', 'complete', 'thorough', 'all', 'everything']
    };

    const messageLower = message.toLowerCase();
    let bestScope: UserIntent['scope'] = 'broad';
    let bestScore = 0;

    for (const [scope, indicators] of Object.entries(scopeIndicators)) {
      const score = indicators.reduce((sum, indicator) => {
        return sum + (messageLower.includes(indicator) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestScope = scope as UserIntent['scope'];
      }
    }

    return bestScope;
  }

  private getBasePromptForIntent(intentType: UserIntent['type']): string {
    const basePrompts = {
      research: 'You are a research assistant helping to find comprehensive information about {{entities}}. Consider the user\'s {{user_expertise}} level and provide {{communication_style}} explanations.',
      analysis: 'You are an analytical expert helping to examine and evaluate {{entities}}. Provide structured analysis considering {{domain}} context.',
      synthesis: 'You are a synthesis specialist helping to combine and integrate information about {{entities}}. Create coherent understanding from multiple perspectives.',
      validation: 'You are a fact-checking expert helping to verify information about {{entities}}. Provide evidence-based validation with source credibility assessment.',
      exploration: 'You are an exploration guide helping to discover new insights about {{entities}}. Encourage creative thinking and novel connections.',
      comparison: 'You are a comparison specialist helping to evaluate differences and similarities between {{entities}}. Provide balanced comparative analysis.'
    };

    return basePrompts[intentType] || basePrompts.research;
  }

  private startLearningLoop(): void {
    // Analyze learning patterns every 30 minutes
    setInterval(() => {
      this.analyzeLearningPatterns();
    }, 1800000);
  }

  private async analyzeLearningPatterns(): Promise<void> {
    try {
      for (const context of this.userContexts.values()) {
        // Analyze user progress and adaptation
        const recentHistory = context.conversationHistory.slice(-10);
        if (recentHistory.length > 5) {
          const avgConfidence = recentHistory.reduce((sum, turn) => sum + turn.intent.confidence, 0) / recentHistory.length;

          if (avgConfidence > 80) {
            // User is doing well, suggest more challenging topics
            context.learningData.improvementAreas.push('ready_for_advanced_topics');
          } else if (avgConfidence < 60) {
            // User might need simpler approaches
            context.learningData.improvementAreas.push('needs_simplified_approach');
          }
        }
      }
    } catch (error) {
      logger.error('Learning pattern analysis failed:', error);
    }
  }

  // Public API methods
  public async recordFeedback(
    userId: string,
    sessionId: string,
    turnId: string,
    feedback: UserFeedback
  ): Promise<void> {
    const contextKey = `${userId}_${sessionId}`;
    const context = this.userContexts.get(contextKey);

    if (context) {
      const turn = context.conversationHistory.find(t => t.id === turnId);
      if (turn) {
        turn.feedback = feedback;

        // Update learning data based on feedback
        const domain = turn.intent.domain;
        if (!context.learningData.feedbackPatterns[domain]) {
          context.learningData.feedbackPatterns[domain] = 0;
        }
        context.learningData.feedbackPatterns[domain] += feedback.rating;

        await this.cacheContext(context);

        this.emit('feedback_received', {
          userId,
          sessionId,
          turnId,
          feedback
        });
      }
    }
  }

  public getUserProfile(userId: string, sessionId: string): UserProfile | null {
    const contextKey = `${userId}_${sessionId}`;
    const context = this.userContexts.get(contextKey);
    return context ? context.userProfile : null;
  }

  public getConversationHistory(userId: string, sessionId: string, limit: number = 10): ConversationTurn[] {
    const contextKey = `${userId}_${sessionId}`;
    const context = this.userContexts.get(contextKey);
    return context ? context.conversationHistory.slice(-limit) : [];
  }

  public getLearningInsights(userId: string, sessionId: string): any {
    const contextKey = `${userId}_${sessionId}`;
    const context = this.userContexts.get(contextKey);

    if (!context) return null;

    return {
      expertise: context.userProfile.expertise,
      successfulQueries: context.learningData.successfulQueries.length,
      challengingTopics: context.learningData.challengingTopics,
      improvementAreas: context.learningData.improvementAreas,
      feedbackPatterns: context.learningData.feedbackPatterns,
      conversationCount: context.conversationHistory.length
    };
  }
}

export default CognitiveResearchAssistant;