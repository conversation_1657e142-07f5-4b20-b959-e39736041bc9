import { executeNeo4jQuery, executeNeo4jTransaction } from '@/config/neo4j';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';
import { DatabaseError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';

interface SupplementData {
  id?: string;
  name: string;
  description?: string;
  brand?: string;
  commonNames?: string[];
  category?: string;
  dosageRange?: string;
  safetyLevel?: string;
  mechanismOfAction?: string;
  therapeuticClass?: string;
}

interface IngredientData {
  id?: string;
  name: string;
  chemicalName?: string;
  alternativeNames?: string[];
  description?: string;
  molecularWeight?: number;
  bioavailability?: number;
}

interface StudyData {
  id?: string;
  title: string;
  authors?: string[];
  journal?: string;
  year?: number;
  abstract?: string;
  evidenceLevel?: string;
  studyType?: string;
  participantCount?: number;
  doi?: string;
}

export class DataMigrationService {
  
  /**
   * Migrate supplement data from various sources
   */
  async migrateSupplementData(dataSource: string, format: 'csv' | 'json'): Promise<any> {
    const startTime = Date.now();
    
    try {
      logger.info(`Starting supplement data migration from ${dataSource} (${format})`);
      
      let supplementData: SupplementData[];
      
      if (format === 'csv') {
        supplementData = await this.parseCsvFile(dataSource);
      } else {
        supplementData = await this.parseJsonFile(dataSource);
      }
      
      const migrationResults = {
        totalRecords: supplementData.length,
        successfulMigrations: 0,
        failedMigrations: 0,
        errors: [] as string[]
      };
      
      // Process supplements in batches
      const batchSize = 100;
      for (let i = 0; i < supplementData.length; i += batchSize) {
        const batch = supplementData.slice(i, i + batchSize);
        
        try {
          await this.processBatch(batch, 'Supplement');
          migrationResults.successfulMigrations += batch.length;
        } catch (error) {
          migrationResults.failedMigrations += batch.length;
          migrationResults.errors.push(`Batch ${i}-${i + batch.length}: ${error}`);
          logError('Batch migration failed', error, { batchStart: i, batchSize: batch.length });
        }
      }
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Supplement data migration', 'Supplements', duration, { successfulMigrations: migrationResults.successfulMigrations });
      
      return migrationResults;
      
    } catch (error) {
      logError('Failed to migrate supplement data', error, { dataSource, format });
      throw new DatabaseError('Failed to migrate supplement data');
    }
  }
  
  /**
   * Migrate research studies data
   */
  async migrateStudyData(dataSource: string): Promise<any> {
    const startTime = Date.now();
    
    try {
      logger.info(`Starting study data migration from ${dataSource}`);
      
      const studyData: StudyData[] = await this.parseJsonFile(dataSource);
      
      const migrationResults = {
        totalRecords: studyData.length,
        successfulMigrations: 0,
        failedMigrations: 0,
        errors: [] as string[]
      };
      
      // Process studies in batches
      const batchSize = 50;
      for (let i = 0; i < studyData.length; i += batchSize) {
        const batch = studyData.slice(i, i + batchSize);
        
        try {
          await this.processBatch(batch, 'Study');
          migrationResults.successfulMigrations += batch.length;
        } catch (error) {
          migrationResults.failedMigrations += batch.length;
          migrationResults.errors.push(`Batch ${i}-${i + batch.length}: ${error}`);
        }
      }
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Study data migration', 'Studies', duration, { successfulMigrations: migrationResults.successfulMigrations });
      
      return migrationResults;
      
    } catch (error) {
      logError('Failed to migrate study data', error, { dataSource });
      throw new DatabaseError('Failed to migrate study data');
    }
  }
  
  /**
   * Create relationships between migrated entities
   */
  async createRelationships(relationshipData: any[]): Promise<any> {
    const startTime = Date.now();
    
    try {
      logger.info('Creating relationships between entities');
      
      const relationshipQueries = relationshipData.map(rel => ({
        query: `
          MATCH (a {name: $sourceName}), (b {name: $targetName})
          WHERE $sourceLabel IN labels(a) AND $targetLabel IN labels(b)
          CREATE (a)-[r:${rel.type} $properties]->(b)
          RETURN r
        `,
        parameters: {
          sourceName: rel.source,
          targetName: rel.target,
          sourceLabel: rel.sourceLabel,
          targetLabel: rel.targetLabel,
          properties: {
            strength: rel.strength || 'medium',
            evidenceLevel: rel.evidenceLevel || 'moderate',
            confidence: rel.confidence || 0.7,
            source: 'DATA_MIGRATION',
            createdAt: new Date().toISOString()
          }
        }
      }));
      
      // Execute relationship creation in batches
      const batchSize = 50;
      let successfulRelationships = 0;
      
      for (let i = 0; i < relationshipQueries.length; i += batchSize) {
        const batch = relationshipQueries.slice(i, i + batchSize);
        
        try {
          await executeNeo4jTransaction(batch);
          successfulRelationships += batch.length;
        } catch (error) {
          logError('Failed to create relationship batch', error, { batchStart: i });
        }
      }
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Relationship creation', 'Relationships', duration, { successfulRelationships: successfulRelationships });
      
      return {
        totalRelationships: relationshipData.length,
        successfulRelationships,
        failedRelationships: relationshipData.length - successfulRelationships
      };
      
    } catch (error) {
      logError('Failed to create relationships', error);
      throw new DatabaseError('Failed to create relationships');
    }
  }
  
  /**
   * Validate data integrity after migration
   */
  async validateDataIntegrity(): Promise<any> {
    const startTime = Date.now();
    
    try {
      logger.info('Validating data integrity');
      
      const validationQueries = [
        // Check for orphaned nodes
        'MATCH (n) WHERE NOT (n)--() RETURN labels(n) as nodeType, count(n) as count',
        
        // Check for duplicate supplements
        'MATCH (s:Supplement) WITH s.name as name, collect(s) as supplements WHERE size(supplements) > 1 RETURN name, size(supplements) as duplicateCount',
        
        // Check relationship distribution
        'MATCH ()-[r]->() RETURN type(r) as relationshipType, count(r) as count ORDER BY count DESC',
        
        // Check data completeness
        'MATCH (s:Supplement) RETURN count(s) as totalSupplements, count(s.description) as withDescription, count(s.dosageRange) as withDosage'
      ];
      
      const results = await Promise.all(
        validationQueries.map(query => executeNeo4jQuery(query))
      );
      
      const validation = {
        orphanedNodes: results[0].records.map((r: any) => ({
          nodeType: r.get('nodeType'),
          count: r.get('count').toNumber()
        })),
        duplicateSupplements: results[1].records.map((r: any) => ({
          name: r.get('name'),
          duplicateCount: r.get('duplicateCount').toNumber()
        })),
        relationshipDistribution: results[2].records.map((r: any) => ({
          type: r.get('relationshipType'),
          count: r.get('count').toNumber()
        })),
        dataCompleteness: results[3].records[0] ? {
          totalSupplements: results[3].records[0].get('totalSupplements').toNumber(),
          withDescription: results[3].records[0].get('withDescription').toNumber(),
          withDosage: results[3].records[0].get('withDosage').toNumber()
        } : null
      };
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Data integrity validation', 'Validation', duration);
      
      return validation;
      
    } catch (error) {
      logError('Failed to validate data integrity', error);
      throw new DatabaseError('Failed to validate data integrity');
    }
  }
  
  private async parseCsvFile(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data: any) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error: Error) => reject(error));
    });
  }
  
  private async parseJsonFile(filePath: string): Promise<any[]> {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(fileContent);
    } catch (error) {
      throw new Error(`Failed to parse JSON file: ${error}`);
    }
  }
  
  private async processBatch(batch: any[], nodeType: string): Promise<void> {
    const queries = batch.map(item => ({
      query: `
        CREATE (n:${nodeType} $properties)
        RETURN n
      `,
      parameters: {
        properties: {
          ...item,
          id: item.id || uuidv4(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          source: 'DATA_MIGRATION'
        }
      }
    }));
    
    await executeNeo4jTransaction(queries);
  }
}
