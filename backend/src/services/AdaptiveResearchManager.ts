import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';

export interface ResearchStrategy {
  id: string;
  name: string;
  description: string;
  type: 'comprehensive' | 'focused' | 'exploratory' | 'validation' | 'synthesis';
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  steps: ResearchStep[];
  estimatedDuration: number;
  requiredResources: string[];
  successCriteria: string[];
  adaptationRules: AdaptationRule[];
  performance: StrategyPerformance;
  metadata: Record<string, any>;
}

export interface ResearchStep {
  id: string;
  name: string;
  type: 'search' | 'analysis' | 'synthesis' | 'validation' | 'enhancement';
  description: string;
  inputs: string[];
  outputs: string[];
  dependencies: string[];
  estimatedTime: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  adaptable: boolean;
  fallbackOptions: string[];
}

export interface AdaptationRule {
  condition: string;
  action: 'skip_step' | 'add_step' | 'modify_step' | 'change_strategy' | 'increase_depth' | 'reduce_scope';
  parameters: Record<string, any>;
  priority: number;
}

export interface StrategyPerformance {
  usageCount: number;
  successRate: number;
  averageQuality: number;
  averageTime: number;
  userSatisfaction: number;
  adaptationFrequency: number;
  lastUsed: Date;
  lastOptimized: Date;
}

export interface ResearchContext {
  query: string;
  domain: string;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  timeConstraint?: number;
  qualityRequirement: 'basic' | 'standard' | 'high' | 'expert';
  resourceConstraints: string[];
  userExpertise: 'beginner' | 'intermediate' | 'expert';
  previousResults?: any[];
  sessionHistory?: any[];
}

export interface AdaptationDecision {
  originalStrategy: string;
  adaptedStrategy: string;
  changes: StrategyChange[];
  reasoning: string;
  confidence: number;
  expectedImprovement: number;
}

export interface StrategyChange {
  type: 'step_added' | 'step_removed' | 'step_modified' | 'order_changed' | 'parameters_adjusted';
  stepId?: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
}

/**
 * Adaptive Research Strategy Manager with intelligent strategy selection and optimization
 */
export class AdaptiveResearchManager extends EventEmitter {
  private strategies: Map<string, ResearchStrategy> = new Map();
  private adaptationHistory: Map<string, AdaptationDecision[]> = new Map();
  private performanceMetrics: Map<string, any> = new Map();
  private learningData: Map<string, any[]> = new Map();

  constructor() {
    super();
    this.initializeBaseStrategies();
    this.startAdaptationLearning();
    logger.info('🧭 Adaptive Research Strategy Manager initialized');
  }

  /**
   * Initialize base research strategies
   */
  private initializeBaseStrategies(): void {
    const baseStrategies: Omit<ResearchStrategy, 'performance'>[] = [
      {
        id: 'comprehensive_deep_dive',
        name: 'Comprehensive Deep Dive',
        description: 'Thorough multi-phase research with extensive validation',
        type: 'comprehensive',
        complexity: 'complex',
        estimatedDuration: 1800000, // 30 minutes
        requiredResources: ['search_agents', 'analysis_agents', 'validation_agents'],
        successCriteria: ['high_quality_sources', 'comprehensive_coverage', 'validated_findings'],
        steps: [
          {
            id: 'initial_search',
            name: 'Initial Broad Search',
            type: 'search',
            description: 'Conduct broad search to understand topic landscape',
            inputs: ['query', 'domain'],
            outputs: ['initial_sources', 'topic_map'],
            dependencies: [],
            estimatedTime: 300000, // 5 minutes
            priority: 'critical',
            adaptable: true,
            fallbackOptions: ['focused_search']
          },
          {
            id: 'source_analysis',
            name: 'Source Quality Analysis',
            type: 'analysis',
            description: 'Analyze and rank sources by quality and relevance',
            inputs: ['initial_sources'],
            outputs: ['ranked_sources', 'quality_metrics'],
            dependencies: ['initial_search'],
            estimatedTime: 420000, // 7 minutes
            priority: 'high',
            adaptable: true,
            fallbackOptions: ['basic_filtering']
          },
          {
            id: 'deep_content_analysis',
            name: 'Deep Content Analysis',
            type: 'analysis',
            description: 'Detailed analysis of top-quality sources',
            inputs: ['ranked_sources'],
            outputs: ['key_insights', 'evidence_map'],
            dependencies: ['source_analysis'],
            estimatedTime: 600000, // 10 minutes
            priority: 'high',
            adaptable: true,
            fallbackOptions: ['surface_analysis']
          },
          {
            id: 'cross_validation',
            name: 'Cross-Validation',
            type: 'validation',
            description: 'Validate findings across multiple sources',
            inputs: ['key_insights', 'evidence_map'],
            outputs: ['validated_insights', 'confidence_scores'],
            dependencies: ['deep_content_analysis'],
            estimatedTime: 360000, // 6 minutes
            priority: 'medium',
            adaptable: true,
            fallbackOptions: ['basic_validation']
          },
          {
            id: 'synthesis_report',
            name: 'Comprehensive Synthesis',
            type: 'synthesis',
            description: 'Create comprehensive research report',
            inputs: ['validated_insights', 'confidence_scores'],
            outputs: ['final_report', 'recommendations'],
            dependencies: ['cross_validation'],
            estimatedTime: 480000, // 8 minutes
            priority: 'critical',
            adaptable: false,
            fallbackOptions: []
          }
        ],
        adaptationRules: [
          {
            condition: 'time_constraint < 900000', // Less than 15 minutes
            action: 'reduce_scope',
            parameters: { skip_steps: ['cross_validation'], reduce_depth: true },
            priority: 1
          },
          {
            condition: 'quality_requirement == "basic"',
            action: 'skip_step',
            parameters: { step_id: 'cross_validation' },
            priority: 2
          }
        ],
        metadata: { difficulty: 'high', recommended_for: ['expert_users', 'critical_research'] }
      },
      {
        id: 'focused_rapid',
        name: 'Focused Rapid Research',
        description: 'Quick, targeted research for specific questions',
        type: 'focused',
        complexity: 'simple',
        estimatedDuration: 600000, // 10 minutes
        requiredResources: ['search_agents'],
        successCriteria: ['relevant_answers', 'quick_turnaround'],
        steps: [
          {
            id: 'targeted_search',
            name: 'Targeted Search',
            type: 'search',
            description: 'Search for specific information related to query',
            inputs: ['query'],
            outputs: ['relevant_sources'],
            dependencies: [],
            estimatedTime: 240000, // 4 minutes
            priority: 'critical',
            adaptable: true,
            fallbackOptions: ['broad_search']
          },
          {
            id: 'quick_analysis',
            name: 'Quick Analysis',
            type: 'analysis',
            description: 'Rapid analysis of most relevant sources',
            inputs: ['relevant_sources'],
            outputs: ['key_findings'],
            dependencies: ['targeted_search'],
            estimatedTime: 180000, // 3 minutes
            priority: 'high',
            adaptable: true,
            fallbackOptions: ['summary_extraction']
          },
          {
            id: 'rapid_synthesis',
            name: 'Rapid Synthesis',
            type: 'synthesis',
            description: 'Quick synthesis of findings',
            inputs: ['key_findings'],
            outputs: ['concise_answer'],
            dependencies: ['quick_analysis'],
            estimatedTime: 180000, // 3 minutes
            priority: 'critical',
            adaptable: false,
            fallbackOptions: []
          }
        ],
        adaptationRules: [
          {
            condition: 'insufficient_sources',
            action: 'add_step',
            parameters: { step_type: 'search', expand_query: true },
            priority: 1
          }
        ],
        metadata: { difficulty: 'low', recommended_for: ['quick_questions', 'time_constrained'] }
      },
      {
        id: 'exploratory_discovery',
        name: 'Exploratory Discovery',
        description: 'Open-ended exploration for new insights and connections',
        type: 'exploratory',
        complexity: 'moderate',
        estimatedDuration: 1200000, // 20 minutes
        requiredResources: ['search_agents', 'analysis_agents'],
        successCriteria: ['novel_insights', 'connection_discovery', 'comprehensive_mapping'],
        steps: [
          {
            id: 'broad_exploration',
            name: 'Broad Topic Exploration',
            type: 'search',
            description: 'Explore topic from multiple angles and perspectives',
            inputs: ['query', 'domain'],
            outputs: ['diverse_sources', 'topic_dimensions'],
            dependencies: [],
            estimatedTime: 360000, // 6 minutes
            priority: 'critical',
            adaptable: true,
            fallbackOptions: ['focused_search']
          },
          {
            id: 'pattern_analysis',
            name: 'Pattern and Connection Analysis',
            type: 'analysis',
            description: 'Identify patterns and connections across sources',
            inputs: ['diverse_sources'],
            outputs: ['patterns', 'connections', 'themes'],
            dependencies: ['broad_exploration'],
            estimatedTime: 480000, // 8 minutes
            priority: 'high',
            adaptable: true,
            fallbackOptions: ['basic_categorization']
          },
          {
            id: 'insight_generation',
            name: 'Novel Insight Generation',
            type: 'synthesis',
            description: 'Generate novel insights from discovered patterns',
            inputs: ['patterns', 'connections', 'themes'],
            outputs: ['novel_insights', 'hypotheses'],
            dependencies: ['pattern_analysis'],
            estimatedTime: 360000, // 6 minutes
            priority: 'medium',
            adaptable: true,
            fallbackOptions: ['summary_insights']
          }
        ],
        adaptationRules: [
          {
            condition: 'low_diversity_sources',
            action: 'modify_step',
            parameters: { step_id: 'broad_exploration', expand_search_terms: true },
            priority: 1
          }
        ],
        metadata: { difficulty: 'medium', recommended_for: ['research_discovery', 'innovation'] }
      }
    ];

    baseStrategies.forEach(strategy => {
      this.strategies.set(strategy.id, {
        ...strategy,
        performance: {
          usageCount: 0,
          successRate: 0,
          averageQuality: 0,
          averageTime: 0,
          userSatisfaction: 0,
          adaptationFrequency: 0,
          lastUsed: new Date(),
          lastOptimized: new Date()
        }
      });
    });
  }

  /**
   * Select optimal strategy based on research context
   */
  public async selectOptimalStrategy(context: ResearchContext): Promise<ResearchStrategy> {
    const metricId = performanceMonitoringService.trackAIOperation('select_strategy', 'strategy_selection');

    try {
      // Check cache for similar contexts
      const cacheKey = `strategy_selection:${this.hashContext(context)}`;
      const cached = await enhancedCacheService.get<string>(cacheKey);
      
      if (cached) {
        const strategy = this.strategies.get(cached);
        if (strategy) {
          performanceMonitoringService.endMetric(metricId, true);
          return strategy;
        }
      }

      // Score strategies based on context
      const strategies = Array.from(this.strategies.values());
      const scoredStrategies = strategies.map(strategy => {
        let score = 0;

        // Complexity matching
        if (strategy.complexity === context.complexity) {
          score += 30;
        } else if (this.isComplexityCompatible(strategy.complexity, context.complexity)) {
          score += 15;
        }

        // Quality requirement matching
        if (context.qualityRequirement === 'expert' && strategy.type === 'comprehensive') {
          score += 25;
        } else if (context.qualityRequirement === 'basic' && strategy.type === 'focused') {
          score += 25;
        }

        // Time constraint consideration
        if (context.timeConstraint) {
          if (strategy.estimatedDuration <= context.timeConstraint) {
            score += 20;
          } else {
            score -= Math.min((strategy.estimatedDuration - context.timeConstraint) / 60000, 20);
          }
        }

        // Performance history
        score += strategy.performance.successRate * 0.2;
        score += strategy.performance.averageQuality * 0.1;
        score += strategy.performance.userSatisfaction * 0.1;

        // User expertise matching
        if (context.userExpertise === 'expert' && strategy.complexity === 'expert') {
          score += 15;
        } else if (context.userExpertise === 'beginner' && strategy.complexity === 'simple') {
          score += 15;
        }

        return { strategy, score };
      });

      // Sort by score and select best
      scoredStrategies.sort((a, b) => b.score - a.score);
      const selectedStrategy = scoredStrategies[0].strategy;

      // Cache selection
      await enhancedCacheService.set(cacheKey, selectedStrategy.id, {
        ttl: 1800, // 30 minutes
        tags: ['strategy_selections', context.domain]
      });

      performanceMonitoringService.endMetric(metricId, true);

      this.emit('strategy_selected', {
        strategyId: selectedStrategy.id,
        context,
        score: scoredStrategies[0].score
      });

      return selectedStrategy;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Strategy selection failed');
      throw error;
    }
  }

  /**
   * Adapt strategy based on real-time conditions
   */
  public async adaptStrategy(
    strategy: ResearchStrategy,
    context: ResearchContext,
    currentConditions: Record<string, any>
  ): Promise<AdaptationDecision> {
    const metricId = performanceMonitoringService.trackAIOperation('adapt_strategy', 'strategy_adaptation');

    try {
      const adaptedStrategy = JSON.parse(JSON.stringify(strategy)); // Deep clone
      const changes: StrategyChange[] = [];
      let reasoning = 'Strategy adapted based on: ';
      const reasoningParts: string[] = [];

      // Apply adaptation rules
      for (const rule of strategy.adaptationRules) {
        if (this.evaluateCondition(rule.condition, context, currentConditions)) {
          const change = this.applyAdaptationRule(adaptedStrategy, rule);
          if (change) {
            changes.push(change);
            reasoningParts.push(`${rule.condition} -> ${rule.action}`);
          }
        }
      }

      // Dynamic adaptations based on current conditions
      if (currentConditions.timeRemaining && currentConditions.timeRemaining < strategy.estimatedDuration * 0.5) {
        const change = this.reduceStrategyScope(adaptedStrategy);
        if (change) {
          changes.push(change);
          reasoningParts.push('time constraint -> scope reduction');
        }
      }

      if (currentConditions.lowQualitySources) {
        const change = this.enhanceValidation(adaptedStrategy);
        if (change) {
          changes.push(change);
          reasoningParts.push('low quality sources -> enhanced validation');
        }
      }

      reasoning += reasoningParts.join(', ');

      const decision: AdaptationDecision = {
        originalStrategy: strategy.id,
        adaptedStrategy: adaptedStrategy.id,
        changes,
        reasoning,
        confidence: this.calculateAdaptationConfidence(changes),
        expectedImprovement: this.estimateImprovement(changes)
      };

      // Store adaptation decision
      if (!this.adaptationHistory.has(strategy.id)) {
        this.adaptationHistory.set(strategy.id, []);
      }
      this.adaptationHistory.get(strategy.id)!.push(decision);

      // Update strategy performance
      strategy.performance.adaptationFrequency++;

      performanceMonitoringService.endMetric(metricId, true);

      this.emit('strategy_adapted', {
        originalStrategy: strategy.id,
        decision
      });

      return decision;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Strategy adaptation failed');
      throw error;
    }
  }

  /**
   * Learn from strategy performance and optimize
   */
  public recordStrategyPerformance(
    strategyId: string,
    performance: {
      success: boolean;
      quality: number;
      actualTime: number;
      userSatisfaction: number;
      adaptationsUsed: number;
    }
  ): void {
    const strategy = this.strategies.get(strategyId);
    if (!strategy) return;

    // Update performance metrics
    strategy.performance.usageCount++;
    strategy.performance.lastUsed = new Date();

    if (performance.success) {
      strategy.performance.successRate = 
        (strategy.performance.successRate * (strategy.performance.usageCount - 1) + 100) / strategy.performance.usageCount;
    } else {
      strategy.performance.successRate = 
        (strategy.performance.successRate * (strategy.performance.usageCount - 1)) / strategy.performance.usageCount;
    }

    strategy.performance.averageQuality = 
      (strategy.performance.averageQuality * (strategy.performance.usageCount - 1) + performance.quality) / strategy.performance.usageCount;

    strategy.performance.averageTime = 
      (strategy.performance.averageTime * (strategy.performance.usageCount - 1) + performance.actualTime) / strategy.performance.usageCount;

    strategy.performance.userSatisfaction = 
      (strategy.performance.userSatisfaction * (strategy.performance.usageCount - 1) + performance.userSatisfaction) / strategy.performance.usageCount;

    // Store learning data
    if (!this.learningData.has(strategyId)) {
      this.learningData.set(strategyId, []);
    }
    this.learningData.get(strategyId)!.push({
      performance,
      timestamp: new Date()
    });

    this.emit('performance_recorded', {
      strategyId,
      performance: strategy.performance
    });
  }

  // Helper methods
  private hashContext(context: ResearchContext): string {
    const key = `${context.query}_${context.domain}_${context.complexity}_${context.qualityRequirement}_${context.userExpertise}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  }

  private isComplexityCompatible(strategyComplexity: string, contextComplexity: string): boolean {
    const complexityLevels = { simple: 1, moderate: 2, complex: 3, expert: 4 };
    const strategyLevel = complexityLevels[strategyComplexity as keyof typeof complexityLevels];
    const contextLevel = complexityLevels[contextComplexity as keyof typeof complexityLevels];
    
    return Math.abs(strategyLevel - contextLevel) <= 1;
  }

  private evaluateCondition(condition: string, context: ResearchContext, currentConditions: Record<string, any>): boolean {
    // Simple condition evaluation - in production, this would be more sophisticated
    if (condition.includes('time_constraint')) {
      const threshold = parseInt(condition.split('<')[1]?.trim() || '0');
      return (context.timeConstraint || Infinity) < threshold;
    }
    
    if (condition.includes('quality_requirement')) {
      const requirement = condition.split('==')[1]?.trim().replace(/"/g, '');
      return context.qualityRequirement === requirement;
    }

    if (condition in currentConditions) {
      return currentConditions[condition];
    }

    return false;
  }

  private applyAdaptationRule(strategy: ResearchStrategy, rule: AdaptationRule): StrategyChange | null {
    switch (rule.action) {
      case 'skip_step':
        if (rule.parameters.step_id) {
          const stepIndex = strategy.steps.findIndex(s => s.id === rule.parameters.step_id);
          if (stepIndex !== -1) {
            strategy.steps.splice(stepIndex, 1);
            return {
              type: 'step_removed',
              stepId: rule.parameters.step_id,
              description: `Removed step: ${rule.parameters.step_id}`,
              impact: 'medium'
            };
          }
        }
        break;

      case 'reduce_scope':
        if (rule.parameters.skip_steps) {
          rule.parameters.skip_steps.forEach((stepId: string) => {
            const stepIndex = strategy.steps.findIndex(s => s.id === stepId);
            if (stepIndex !== -1) {
              strategy.steps.splice(stepIndex, 1);
            }
          });
          return {
            type: 'step_removed',
            description: `Reduced scope by removing steps: ${rule.parameters.skip_steps.join(', ')}`,
            impact: 'high'
          };
        }
        break;
    }

    return null;
  }

  private reduceStrategyScope(strategy: ResearchStrategy): StrategyChange | null {
    // Remove lowest priority optional steps
    const optionalSteps = strategy.steps.filter(s => s.priority === 'low' || s.priority === 'medium');
    if (optionalSteps.length > 0) {
      const stepToRemove = optionalSteps[0];
      const stepIndex = strategy.steps.findIndex(s => s.id === stepToRemove.id);
      strategy.steps.splice(stepIndex, 1);
      
      return {
        type: 'step_removed',
        stepId: stepToRemove.id,
        description: `Removed step to reduce scope: ${stepToRemove.name}`,
        impact: 'medium'
      };
    }
    return null;
  }

  private enhanceValidation(strategy: ResearchStrategy): StrategyChange | null {
    // Add validation step if not present
    const hasValidation = strategy.steps.some(s => s.type === 'validation');
    if (!hasValidation) {
      const validationStep: ResearchStep = {
        id: 'enhanced_validation',
        name: 'Enhanced Source Validation',
        type: 'validation',
        description: 'Additional validation due to low-quality sources',
        inputs: ['sources'],
        outputs: ['validated_sources'],
        dependencies: [],
        estimatedTime: 300000,
        priority: 'high',
        adaptable: true,
        fallbackOptions: []
      };
      
      strategy.steps.push(validationStep);
      
      return {
        type: 'step_added',
        stepId: validationStep.id,
        description: 'Added enhanced validation step',
        impact: 'high'
      };
    }
    return null;
  }

  private calculateAdaptationConfidence(changes: StrategyChange[]): number {
    if (changes.length === 0) return 100;
    
    let confidence = 90;
    changes.forEach(change => {
      if (change.impact === 'high') confidence -= 15;
      else if (change.impact === 'medium') confidence -= 10;
      else confidence -= 5;
    });
    
    return Math.max(confidence, 50);
  }

  private estimateImprovement(changes: StrategyChange[]): number {
    // Estimate improvement percentage based on changes
    let improvement = 0;
    changes.forEach(change => {
      if (change.type === 'step_removed') improvement += 10; // Faster execution
      if (change.type === 'step_added') improvement += 15; // Better quality
      if (change.type === 'step_modified') improvement += 5; // Optimization
    });
    
    return Math.min(improvement, 50);
  }

  private startAdaptationLearning(): void {
    // Analyze adaptation patterns every hour
    setInterval(() => {
      this.analyzeAdaptationPatterns();
    }, 3600000);
  }

  private async analyzeAdaptationPatterns(): Promise<void> {
    try {
      // Analyze which adaptations lead to better outcomes
      for (const [strategyId, adaptations] of this.adaptationHistory) {
        if (adaptations.length > 5) {
          const successfulAdaptations = adaptations.filter(a => a.expectedImprovement > 10);
          if (successfulAdaptations.length > 0) {
            logger.info(`Strategy ${strategyId} shows successful adaptation patterns`);
            // Could trigger strategy optimization here
          }
        }
      }
    } catch (error) {
      logger.error('Adaptation pattern analysis failed:', error);
    }
  }

  // Public API methods
  public getStrategies(): ResearchStrategy[] {
    return Array.from(this.strategies.values());
  }

  public getStrategy(id: string): ResearchStrategy | null {
    return this.strategies.get(id) || null;
  }

  public getStrategyPerformance(): Record<string, any> {
    const strategies = Array.from(this.strategies.values());
    return {
      totalStrategies: strategies.length,
      averageSuccessRate: strategies.reduce((sum, s) => sum + s.performance.successRate, 0) / strategies.length,
      mostUsed: strategies.sort((a, b) => b.performance.usageCount - a.performance.usageCount).slice(0, 3),
      bestPerforming: strategies.sort((a, b) => b.performance.averageQuality - a.performance.averageQuality).slice(0, 3)
    };
  }

  public getAdaptationHistory(strategyId?: string): AdaptationDecision[] {
    if (strategyId) {
      return this.adaptationHistory.get(strategyId) || [];
    }
    
    const allAdaptations: AdaptationDecision[] = [];
    for (const adaptations of this.adaptationHistory.values()) {
      allAdaptations.push(...adaptations);
    }
    return allAdaptations.sort((a, b) => b.confidence - a.confidence);
  }
}

export default AdaptiveResearchManager;
