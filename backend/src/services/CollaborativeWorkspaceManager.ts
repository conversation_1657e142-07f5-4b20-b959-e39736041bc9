import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { enhancedCacheService } from './EnhancedCacheService';
import { performanceMonitoringService } from './PerformanceMonitoringService';

export interface WorkspaceDocument {
  id: string;
  workspaceId: string;
  title: string;
  content: string;
  type: 'research_note' | 'analysis' | 'report' | 'hypothesis' | 'conclusion';
  version: number;
  createdBy: string;
  createdAt: Date;
  lastModifiedBy: string;
  lastModifiedAt: Date;
  collaborators: string[];
  isLocked: boolean;
  lockedBy?: string;
  lockedAt?: Date;
  tags: string[];
  metadata: Record<string, any>;
}

export interface DocumentEdit {
  id: string;
  documentId: string;
  userId: string;
  username: string;
  type: 'insert' | 'delete' | 'replace' | 'format';
  position: number;
  length?: number;
  content?: string;
  timestamp: Date;
  applied: boolean;
}

export interface WorkspaceComment {
  id: string;
  documentId: string;
  userId: string;
  username: string;
  content: string;
  position?: number; // Character position in document
  selection?: { start: number; end: number };
  parentId?: string; // For replies
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  reactions: Map<string, string[]>; // emoji -> userIds
  createdAt: Date;
  updatedAt: Date;
}

export interface CollaborativeWorkspace {
  id: string;
  name: string;
  description?: string;
  type: 'research' | 'analysis' | 'project' | 'experiment';
  ownerId: string;
  members: Map<string, WorkspaceMember>;
  documents: Map<string, WorkspaceDocument>;
  comments: Map<string, WorkspaceComment>;
  settings: {
    isPublic: boolean;
    allowGuestAccess: boolean;
    requireApproval: boolean;
    enableVersioning: boolean;
    enableComments: boolean;
    enableRealTimeEditing: boolean;
    autoSave: boolean;
    autoSaveInterval: number; // in seconds
  };
  permissions: {
    read: string[];
    write: string[];
    admin: string[];
  };
  createdAt: Date;
  updatedAt: Date;
  lastActivity: Date;
}

export interface WorkspaceMember {
  userId: string;
  username: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer' | 'guest';
  joinedAt: Date;
  lastSeen: Date;
  isOnline: boolean;
  currentDocument?: string;
  cursor?: { documentId: string; position: number };
  permissions: {
    canRead: boolean;
    canWrite: boolean;
    canComment: boolean;
    canInvite: boolean;
    canManage: boolean;
  };
}

export interface WorkspaceActivity {
  id: string;
  workspaceId: string;
  userId: string;
  username: string;
  type: 'document_created' | 'document_edited' | 'document_deleted' | 'comment_added' | 'member_joined' | 'member_left' | 'workspace_created';
  description: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

/**
 * Collaborative workspace manager with real-time editing and commenting
 */
export class CollaborativeWorkspaceManager extends EventEmitter {
  private workspaces: Map<string, CollaborativeWorkspace> = new Map();
  private documentEdits: Map<string, DocumentEdit[]> = new Map(); // documentId -> edits
  private activeUsers: Map<string, Set<string>> = new Map(); // workspaceId -> userIds
  private userCursors: Map<string, Map<string, any>> = new Map(); // workspaceId -> userId -> cursor
  private documentLocks: Map<string, { userId: string; expiresAt: Date }> = new Map();
  private activities: Map<string, WorkspaceActivity[]> = new Map(); // workspaceId -> activities

  constructor() {
    super();
    this.startCleanupTasks();
    logger.info('🤝 Collaborative Workspace Manager initialized');
  }

  /**
   * Create a new workspace
   */
  public async createWorkspace(params: {
    name: string;
    description?: string;
    type: CollaborativeWorkspace['type'];
    ownerId: string;
    ownerUsername: string;
    settings?: Partial<CollaborativeWorkspace['settings']>;
  }): Promise<string> {
    const metricId = performanceMonitoringService.trackApiRequest('create_workspace', 'POST');

    try {
      const workspaceId = `workspace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const owner: WorkspaceMember = {
        userId: params.ownerId,
        username: params.ownerUsername,
        role: 'owner',
        joinedAt: new Date(),
        lastSeen: new Date(),
        isOnline: true,
        permissions: {
          canRead: true,
          canWrite: true,
          canComment: true,
          canInvite: true,
          canManage: true
        }
      };

      const workspace: CollaborativeWorkspace = {
        id: workspaceId,
        name: params.name,
        description: params.description,
        type: params.type,
        ownerId: params.ownerId,
        members: new Map([[params.ownerId, owner]]),
        documents: new Map(),
        comments: new Map(),
        settings: {
          isPublic: false,
          allowGuestAccess: false,
          requireApproval: true,
          enableVersioning: true,
          enableComments: true,
          enableRealTimeEditing: true,
          autoSave: true,
          autoSaveInterval: 30,
          ...params.settings
        },
        permissions: {
          read: [params.ownerId],
          write: [params.ownerId],
          admin: [params.ownerId]
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date()
      };

      this.workspaces.set(workspaceId, workspace);
      this.activities.set(workspaceId, []);

      // Cache workspace
      await enhancedCacheService.set(
        `workspace:${workspaceId}`,
        this.serializeWorkspace(workspace),
        { ttl: 3600, tags: ['workspaces', params.ownerId] }
      );

      // Log activity
      await this.logActivity(workspaceId, params.ownerId, params.ownerUsername, 'workspace_created', 'Workspace created', {
        workspaceName: params.name,
        workspaceType: params.type
      });

      this.emit('workspace_created', {
        workspaceId,
        workspace: this.serializeWorkspace(workspace)
      });

      performanceMonitoringService.endMetric(metricId, true);
      logger.info(`🏠 Workspace created: ${params.name} (${workspaceId}) by ${params.ownerUsername}`);

      return workspaceId;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Workspace creation failed');
      throw error;
    }
  }

  /**
   * Join a workspace
   */
  public async joinWorkspace(workspaceId: string, userId: string, username: string): Promise<void> {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      throw new Error('Workspace not found');
    }

    // Check permissions
    if (!workspace.settings.isPublic && !workspace.permissions.read.includes(userId)) {
      throw new Error('Access denied');
    }

    // Add member if not already present
    if (!workspace.members.has(userId)) {
      const member: WorkspaceMember = {
        userId,
        username,
        role: workspace.settings.allowGuestAccess ? 'guest' : 'viewer',
        joinedAt: new Date(),
        lastSeen: new Date(),
        isOnline: true,
        permissions: {
          canRead: true,
          canWrite: workspace.permissions.write.includes(userId),
          canComment: true,
          canInvite: false,
          canManage: workspace.permissions.admin.includes(userId)
        }
      };

      workspace.members.set(userId, member);
      workspace.lastActivity = new Date();
      workspace.updatedAt = new Date();

      // Track active user
      if (!this.activeUsers.has(workspaceId)) {
        this.activeUsers.set(workspaceId, new Set());
      }
      this.activeUsers.get(workspaceId)!.add(userId);

      // Log activity
      await this.logActivity(workspaceId, userId, username, 'member_joined', `${username} joined the workspace`);

      this.emit('member_joined', {
        workspaceId,
        member: this.serializeMember(member)
      });

      logger.info(`👤 User ${username} joined workspace ${workspaceId}`);
    } else {
      // Update existing member
      const member = workspace.members.get(userId)!;
      member.isOnline = true;
      member.lastSeen = new Date();
      
      if (!this.activeUsers.has(workspaceId)) {
        this.activeUsers.set(workspaceId, new Set());
      }
      this.activeUsers.get(workspaceId)!.add(userId);
    }
  }

  /**
   * Leave a workspace
   */
  public async leaveWorkspace(workspaceId: string, userId: string): Promise<void> {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) return;

    const member = workspace.members.get(userId);
    if (member) {
      member.isOnline = false;
      member.lastSeen = new Date();
      
      // Remove from active users
      const activeUsers = this.activeUsers.get(workspaceId);
      if (activeUsers) {
        activeUsers.delete(userId);
      }

      // Remove cursor
      const cursors = this.userCursors.get(workspaceId);
      if (cursors) {
        cursors.delete(userId);
      }

      this.emit('member_left', {
        workspaceId,
        userId,
        username: member.username
      });

      logger.info(`👋 User ${member.username} left workspace ${workspaceId}`);
    }
  }

  /**
   * Create a new document in workspace
   */
  public async createDocument(params: {
    workspaceId: string;
    title: string;
    content?: string;
    type: WorkspaceDocument['type'];
    userId: string;
    username: string;
    tags?: string[];
  }): Promise<string> {
    const workspace = this.workspaces.get(params.workspaceId);
    if (!workspace) {
      throw new Error('Workspace not found');
    }

    const member = workspace.members.get(params.userId);
    if (!member || !member.permissions.canWrite) {
      throw new Error('Permission denied');
    }

    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const document: WorkspaceDocument = {
      id: documentId,
      workspaceId: params.workspaceId,
      title: params.title,
      content: params.content || '',
      type: params.type,
      version: 1,
      createdBy: params.userId,
      createdAt: new Date(),
      lastModifiedBy: params.userId,
      lastModifiedAt: new Date(),
      collaborators: [params.userId],
      isLocked: false,
      tags: params.tags || [],
      metadata: {}
    };

    workspace.documents.set(documentId, document);
    workspace.lastActivity = new Date();
    workspace.updatedAt = new Date();

    // Initialize edit history
    this.documentEdits.set(documentId, []);

    // Log activity
    await this.logActivity(params.workspaceId, params.userId, params.username, 'document_created', 
      `Created document: ${params.title}`, { documentId, documentType: params.type });

    this.emit('document_created', {
      workspaceId: params.workspaceId,
      document: this.serializeDocument(document)
    });

    logger.info(`📄 Document created: ${params.title} (${documentId}) in workspace ${params.workspaceId}`);
    return documentId;
  }

  /**
   * Edit a document with real-time collaboration
   */
  public async editDocument(params: {
    documentId: string;
    userId: string;
    username: string;
    edit: Omit<DocumentEdit, 'id' | 'documentId' | 'userId' | 'username' | 'timestamp' | 'applied'>;
  }): Promise<void> {
    const document = this.findDocument(params.documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    const workspace = this.workspaces.get(document.workspaceId);
    if (!workspace) {
      throw new Error('Workspace not found');
    }

    const member = workspace.members.get(params.userId);
    if (!member || !member.permissions.canWrite) {
      throw new Error('Permission denied');
    }

    // Check if document is locked by another user
    const lock = this.documentLocks.get(params.documentId);
    if (lock && lock.userId !== params.userId && lock.expiresAt > new Date()) {
      throw new Error('Document is locked by another user');
    }

    // Create edit
    const editId = `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const edit: DocumentEdit = {
      id: editId,
      documentId: params.documentId,
      userId: params.userId,
      username: params.username,
      timestamp: new Date(),
      applied: false,
      ...params.edit
    };

    // Apply edit to document
    this.applyEdit(document, edit);

    // Store edit in history
    if (!this.documentEdits.has(params.documentId)) {
      this.documentEdits.set(params.documentId, []);
    }
    this.documentEdits.get(params.documentId)!.push(edit);

    // Update document metadata
    document.lastModifiedBy = params.userId;
    document.lastModifiedAt = new Date();
    document.version++;

    // Add user to collaborators if not already present
    if (!document.collaborators.includes(params.userId)) {
      document.collaborators.push(params.userId);
    }

    workspace.lastActivity = new Date();
    workspace.updatedAt = new Date();

    // Emit real-time edit
    this.emit('document_edited', {
      workspaceId: document.workspaceId,
      documentId: params.documentId,
      edit: this.serializeEdit(edit),
      document: this.serializeDocument(document)
    });

    // Auto-save if enabled
    if (workspace.settings.autoSave) {
      setTimeout(() => {
        this.saveDocument(params.documentId);
      }, workspace.settings.autoSaveInterval * 1000);
    }
  }

  /**
   * Add a comment to a document
   */
  public async addComment(params: {
    documentId: string;
    userId: string;
    username: string;
    content: string;
    position?: number;
    selection?: { start: number; end: number };
    parentId?: string;
  }): Promise<string> {
    const document = this.findDocument(params.documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    const workspace = this.workspaces.get(document.workspaceId);
    if (!workspace) {
      throw new Error('Workspace not found');
    }

    const member = workspace.members.get(params.userId);
    if (!member || !member.permissions.canComment) {
      throw new Error('Permission denied');
    }

    const commentId = `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const comment: WorkspaceComment = {
      id: commentId,
      documentId: params.documentId,
      userId: params.userId,
      username: params.username,
      content: params.content,
      position: params.position,
      selection: params.selection,
      parentId: params.parentId,
      resolved: false,
      reactions: new Map(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    workspace.comments.set(commentId, comment);
    workspace.lastActivity = new Date();

    // Log activity
    await this.logActivity(document.workspaceId, params.userId, params.username, 'comment_added', 
      `Added comment to ${document.title}`, { documentId: params.documentId, commentId });

    this.emit('comment_added', {
      workspaceId: document.workspaceId,
      documentId: params.documentId,
      comment: this.serializeComment(comment)
    });

    logger.info(`💬 Comment added to document ${params.documentId} by ${params.username}`);
    return commentId;
  }

  /**
   * Update user cursor position
   */
  public updateCursor(workspaceId: string, userId: string, documentId: string, position: number): void {
    if (!this.userCursors.has(workspaceId)) {
      this.userCursors.set(workspaceId, new Map());
    }

    const cursors = this.userCursors.get(workspaceId)!;
    cursors.set(userId, { documentId, position, timestamp: new Date() });

    // Update member cursor
    const workspace = this.workspaces.get(workspaceId);
    if (workspace) {
      const member = workspace.members.get(userId);
      if (member) {
        member.cursor = { documentId, position };
        member.currentDocument = documentId;
      }
    }

    this.emit('cursor_updated', {
      workspaceId,
      userId,
      documentId,
      position
    });
  }

  /**
   * Lock a document for editing
   */
  public async lockDocument(documentId: string, userId: string, durationMs: number = 300000): Promise<void> {
    const existingLock = this.documentLocks.get(documentId);
    if (existingLock && existingLock.userId !== userId && existingLock.expiresAt > new Date()) {
      throw new Error('Document is already locked by another user');
    }

    this.documentLocks.set(documentId, {
      userId,
      expiresAt: new Date(Date.now() + durationMs)
    });

    this.emit('document_locked', {
      documentId,
      userId,
      expiresAt: new Date(Date.now() + durationMs)
    });
  }

  /**
   * Unlock a document
   */
  public async unlockDocument(documentId: string, userId: string): Promise<void> {
    const lock = this.documentLocks.get(documentId);
    if (lock && lock.userId === userId) {
      this.documentLocks.delete(documentId);
      
      this.emit('document_unlocked', {
        documentId,
        userId
      });
    }
  }

  // Helper methods
  private applyEdit(document: WorkspaceDocument, edit: DocumentEdit): void {
    switch (edit.type) {
      case 'insert':
        document.content = 
          document.content.slice(0, edit.position) + 
          (edit.content || '') + 
          document.content.slice(edit.position);
        break;

      case 'delete':
        document.content = 
          document.content.slice(0, edit.position) + 
          document.content.slice(edit.position + (edit.length || 0));
        break;

      case 'replace':
        document.content = 
          document.content.slice(0, edit.position) + 
          (edit.content || '') + 
          document.content.slice(edit.position + (edit.length || 0));
        break;
    }

    edit.applied = true;
  }

  private findDocument(documentId: string): WorkspaceDocument | null {
    for (const workspace of this.workspaces.values()) {
      const document = workspace.documents.get(documentId);
      if (document) return document;
    }
    return null;
  }

  private async saveDocument(documentId: string): Promise<void> {
    const document = this.findDocument(documentId);
    if (document) {
      await enhancedCacheService.set(
        `document:${documentId}`,
        this.serializeDocument(document),
        { ttl: 3600, tags: ['documents', document.workspaceId] }
      );
    }
  }

  private async logActivity(
    workspaceId: string,
    userId: string,
    username: string,
    type: WorkspaceActivity['type'],
    description: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const activityId = `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const activity: WorkspaceActivity = {
      id: activityId,
      workspaceId,
      userId,
      username,
      type,
      description,
      metadata,
      timestamp: new Date()
    };

    if (!this.activities.has(workspaceId)) {
      this.activities.set(workspaceId, []);
    }

    const activities = this.activities.get(workspaceId)!;
    activities.push(activity);

    // Keep only last 1000 activities
    if (activities.length > 1000) {
      this.activities.set(workspaceId, activities.slice(-1000));
    }

    this.emit('activity_logged', {
      workspaceId,
      activity
    });
  }

  private startCleanupTasks(): void {
    // Clean up expired document locks every minute
    setInterval(() => {
      const now = new Date();
      for (const [documentId, lock] of this.documentLocks) {
        if (lock.expiresAt <= now) {
          this.documentLocks.delete(documentId);
          this.emit('document_unlocked', { documentId, userId: lock.userId });
        }
      }
    }, 60000);

    // Clean up old edit history every hour
    setInterval(() => {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      for (const [documentId, edits] of this.documentEdits) {
        const recentEdits = edits.filter(edit => edit.timestamp > oneWeekAgo);
        this.documentEdits.set(documentId, recentEdits);
      }
    }, 60 * 60 * 1000);
  }

  // Serialization methods
  private serializeWorkspace(workspace: CollaborativeWorkspace): any {
    return {
      ...workspace,
      members: Array.from(workspace.members.values()).map(m => this.serializeMember(m)),
      documents: Array.from(workspace.documents.values()).map(d => this.serializeDocument(d)),
      comments: Array.from(workspace.comments.values()).map(c => this.serializeComment(c))
    };
  }

  private serializeMember(member: WorkspaceMember): any {
    return { ...member };
  }

  private serializeDocument(document: WorkspaceDocument): any {
    return { ...document };
  }

  private serializeComment(comment: WorkspaceComment): any {
    return {
      ...comment,
      reactions: Object.fromEntries(comment.reactions)
    };
  }

  private serializeEdit(edit: DocumentEdit): any {
    return { ...edit };
  }

  // Public API methods
  public getWorkspace(workspaceId: string): CollaborativeWorkspace | null {
    return this.workspaces.get(workspaceId) || null;
  }

  public getUserWorkspaces(userId: string): CollaborativeWorkspace[] {
    return Array.from(this.workspaces.values()).filter(workspace =>
      workspace.members.has(userId)
    );
  }

  public getWorkspaceActivities(workspaceId: string, limit: number = 50): WorkspaceActivity[] {
    const activities = this.activities.get(workspaceId) || [];
    return activities.slice(-limit);
  }

  public getDocumentEdits(documentId: string, limit: number = 100): DocumentEdit[] {
    const edits = this.documentEdits.get(documentId) || [];
    return edits.slice(-limit);
  }

  public getActiveUsers(workspaceId: string): string[] {
    return Array.from(this.activeUsers.get(workspaceId) || []);
  }

  public getUserCursors(workspaceId: string): Map<string, any> {
    return this.userCursors.get(workspaceId) || new Map();
  }
}

export default CollaborativeWorkspaceManager;
