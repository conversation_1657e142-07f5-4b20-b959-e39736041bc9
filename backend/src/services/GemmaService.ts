import { Ollama } from 'ollama';
import { logger } from '../utils/logger';

export interface HealthDomain {
  name: string;
  type: 'neurological' | 'core_property';
  confidence: number;
  description: string;
  relatedProperties: string[];
}

export interface SupplementProperty {
  name: string;
  category: string;
  strength: number;
  evidence: string;
  mechanism?: string;
}

export interface SupplementAnalysis {
  supplementName: string;
  healthDomains: HealthDomain[];
  properties: SupplementProperty[];
  mechanisms: string[];
  interactions: string[];
  safetyProfile: {
    riskLevel: 'low' | 'moderate' | 'high';
    warnings: string[];
    contraindications: string[];
  };
  confidence: number;
  analysisDate: Date;
}

export interface InsightGeneration {
  summary: string;
  keyFindings: string[];
  warnings: string[];
  recommendations: string[];
  confidence: number;
}

export class GemmaService {
  private ollama: Ollama;
  private modelName: string;

  constructor() {
    this.ollama = new Ollama({
      host: process.env['OLLAMA_HOST'] || 'http://*************:1234'
    });
    this.modelName = process.env['GEMMA_MODEL'] || 'gemma3:4b';
  }

  async generateResearchPlan(planningPrompt: string): Promise<any> {
    try {
      logger.info(`Starting Gemma research plan generation`);

      const response = await this.ollama.generate({
        model: this.modelName,
        prompt: planningPrompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          num_ctx: 8192,
        }
      });

      return JSON.parse(response.response);
    } catch (error) {
      logger.error(`Error generating research plan:`, error);
      return {
        id: `fallback-plan-${Date.now()}`,
        goals: [],
        strategies: [],
        timeline: { startTime: new Date(), estimatedDuration: 60, milestones: [], adaptationPoints: [] },
        qualityMetrics: { completeness: 0, credibility: 0, freshness: 0, diversity: 0, relevance: 0 },
        adaptationRules: []
      };
    }
  }

  async analyzeSupplementData(
    supplementName: string,
    researchData: any
  ): Promise<SupplementAnalysis> {
    try {
      logger.info(`Starting Gemma analysis for supplement: ${supplementName}`);

      const analysisPrompt = this.createAnalysisPrompt(supplementName, researchData);
      
      const response = await this.ollama.generate({
        model: this.modelName,
        prompt: analysisPrompt,
        stream: false,
        options: {
          temperature: 0.3,
          top_p: 0.9,
          num_ctx: 8192,
        }
      });

      const analysis = this.parseAnalysisResponse(response.response, supplementName);
      
      logger.info(`Gemma analysis completed for ${supplementName}`);
      return analysis;

    } catch (error) {
      logger.error(`Error in Gemma analysis for ${supplementName}:`, error);
      // Return fallback analysis
      return this.createFallbackAnalysis(supplementName);
    }
  }

  async generateInsights(
    supplementName: string,
    analysis: SupplementAnalysis,
    graphUpdate: any
  ): Promise<InsightGeneration> {
    try {
      const insightPrompt = this.createInsightPrompt(supplementName, analysis, graphUpdate);
      
      const response = await this.ollama.generate({
        model: this.modelName,
        prompt: insightPrompt,
        stream: false,
        options: {
          temperature: 0.4,
          top_p: 0.9,
          num_ctx: 4096,
        }
      });

      return this.parseInsightResponse(response.response);

    } catch (error) {
      logger.error(`Error generating insights for ${supplementName}:`, error);
      return this.createFallbackInsights(supplementName);
    }
  }

  async processNaturalLanguageQuery(
    query: string,
    context: any
  ): Promise<string> {
    try {
      const queryPrompt = this.createQueryPrompt(query, context);
      
      const response = await this.ollama.generate({
        model: this.modelName,
        prompt: queryPrompt,
        stream: false,
        options: {
          temperature: 0.5,
          top_p: 0.9,
          num_ctx: 4096,
        }
      });

      return response.response;

    } catch (error) {
      logger.error('Error processing natural language query:', error);
      return 'I apologize, but I encountered an error processing your query. Please try rephrasing your question.';
    }
  }

  private createAnalysisPrompt(supplementName: string, researchData: any): string {
    return `
You are an expert supplement researcher and health analyst. Analyze the following research data for ${supplementName} and provide a comprehensive assessment.

Research Data:
${JSON.stringify(researchData, null, 2)}

Please analyze this supplement and categorize its effects into the following health domains:

NEUROLOGICAL DOMAINS:
- OUN (Central Nervous System)
- Neuroregulation
- Neurotransmitters

CORE PROPERTIES:
- Energia (Energy)
- Nastrój (Mood)
- Relaks (Relaxation)
- Sen (Sleep)
- Pamięć (Memory)
- Skupienie (Focus)
- Siła (Strength)
- Dieta (Diet)
- Detoks (Detox)
- Witalność (Vitality)
- Odporność (Immunity)
- Libido

For each relevant domain, provide:
1. Confidence level (0-1)
2. Mechanism of action
3. Evidence strength
4. Related properties

Also identify:
- Safety profile and risk level
- Potential interactions
- Contraindications
- Overall confidence in the analysis

Format your response as a structured JSON object with the following structure:
{
  "healthDomains": [
    {
      "name": "domain_name",
      "type": "neurological" or "core_property",
      "confidence": 0.0-1.0,
      "description": "detailed description",
      "relatedProperties": ["property1", "property2"]
    }
  ],
  "properties": [
    {
      "name": "property_name",
      "category": "category",
      "strength": 0.0-1.0,
      "evidence": "evidence description",
      "mechanism": "mechanism description"
    }
  ],
  "mechanisms": ["mechanism1", "mechanism2"],
  "interactions": ["interaction1", "interaction2"],
  "safetyProfile": {
    "riskLevel": "low/moderate/high",
    "warnings": ["warning1", "warning2"],
    "contraindications": ["contraindication1"]
  },
  "confidence": 0.0-1.0
}

Respond only with the JSON object, no additional text.
`;
  }

  private createInsightPrompt(
    supplementName: string,
    analysis: SupplementAnalysis,
    graphUpdate: any
  ): string {
    return `
Based on the comprehensive analysis of ${supplementName}, generate key insights and recommendations.

Analysis Data:
${JSON.stringify(analysis, null, 2)}

Graph Update:
${JSON.stringify(graphUpdate, null, 2)}

Please provide:
1. A concise summary of the supplement's primary benefits
2. Key findings from the research
3. Important warnings or considerations
4. Practical recommendations for users

Focus on actionable insights that help users understand the supplement's role in their health regimen.

Format as JSON:
{
  "summary": "concise summary",
  "keyFindings": ["finding1", "finding2", "finding3"],
  "warnings": ["warning1", "warning2"],
  "recommendations": ["rec1", "rec2"],
  "confidence": 0.0-1.0
}

Respond only with the JSON object.
`;
  }

  private createQueryPrompt(query: string, context: any): string {
    return `
You are a knowledgeable supplement expert. Answer the user's question based on the provided context about supplements and their health effects.

User Question: ${query}

Context:
${JSON.stringify(context, null, 2)}

Provide a helpful, accurate response based on the available data. If the information is not sufficient to answer the question, say so clearly. Always include appropriate disclaimers about consulting healthcare professionals for medical advice.

Keep your response conversational but informative, and cite specific evidence when available.
`;
  }

  private parseAnalysisResponse(response: string, supplementName: string): SupplementAnalysis {
    try {
      // Check if response is valid
      if (!response || response === 'undefined' || response.trim() === '') {
        logger.warn(`Empty or undefined response from Gemma for ${supplementName}, using fallback`);
        return this.createFallbackAnalysis(supplementName);
      }

      const parsed = JSON.parse(response);
      return {
        supplementName,
        healthDomains: parsed.healthDomains || [],
        properties: parsed.properties || [],
        mechanisms: parsed.mechanisms || [],
        interactions: parsed.interactions || [],
        safetyProfile: parsed.safetyProfile || {
          riskLevel: 'moderate',
          warnings: [],
          contraindications: []
        },
        confidence: parsed.confidence || 0.5,
        analysisDate: new Date()
      };
    } catch (error) {
      logger.error(`Error parsing Gemma analysis response: "${response}" is not valid JSON`, error);
      return this.createFallbackAnalysis(supplementName);
    }
  }

  private parseInsightResponse(response: string): InsightGeneration {
    try {
      const parsed = JSON.parse(response);
      return {
        summary: parsed.summary || 'Analysis completed successfully.',
        keyFindings: parsed.keyFindings || [],
        warnings: parsed.warnings || [],
        recommendations: parsed.recommendations || [],
        confidence: parsed.confidence || 0.5
      };
    } catch (error) {
      logger.error('Error parsing insight response:', error);
      return this.createFallbackInsights('supplement');
    }
  }

  private createFallbackAnalysis(supplementName: string): SupplementAnalysis {
    return {
      supplementName,
      healthDomains: [
        {
          name: 'General Health',
          type: 'core_property',
          confidence: 0.3,
          description: 'General health benefits based on limited analysis',
          relatedProperties: ['Witalność']
        }
      ],
      properties: [
        {
          name: 'Witalność',
          category: 'General',
          strength: 0.3,
          evidence: 'Limited evidence available'
        }
      ],
      mechanisms: ['Unknown mechanism'],
      interactions: [],
      safetyProfile: {
        riskLevel: 'moderate',
        warnings: ['Consult healthcare provider before use'],
        contraindications: []
      },
      confidence: 0.3,
      analysisDate: new Date()
    };
  }

  private createFallbackInsights(supplementName: string): InsightGeneration {
    return {
      summary: `Analysis of ${supplementName} completed with limited data availability.`,
      keyFindings: ['Limited research data available'],
      warnings: ['Consult healthcare provider before use'],
      recommendations: ['Seek professional medical advice'],
      confidence: 0.3
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.ollama.generate({
        model: this.modelName,
        prompt: 'Hello, are you working?',
        stream: false,
        options: { num_ctx: 512 }
      });
      return response.response.length > 0;
    } catch (error) {
      logger.error('Gemma health check failed:', error);
      return false;
    }
  }
}
