import { MongoClient, Db, Collection } from 'mongodb';
import { logger } from '../utils/logger';

// Health Profile Interfaces (137+ Parameters)
export interface Demographics {
  age: number;
  gender: 'male' | 'female' | 'other';
  weight: number; // kg
  height: number; // cm
  bmi: number;
  bodyFat?: number; // percentage
  muscleMass?: number; // kg
  metabolicRate?: number; // kcal/day
}

export interface MedicalHistory {
  conditions: HealthCondition[];
  medications: Medication[];
  allergies: string[];
  surgeries: Surgery[];
  familyHistory: FamilyCondition[];
  bloodType?: string;
  chronicConditions: string[];
}

export interface HealthCondition {
  id: string;
  name: string;
  severity: 'mild' | 'moderate' | 'severe';
  diagnosedDate: Date;
  status: 'active' | 'resolved' | 'managed';
  notes?: string;
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  startDate: Date;
  endDate?: Date;
  prescribedBy?: string;
  sideEffects?: string[];
}

export interface Surgery {
  id: string;
  procedure: string;
  date: Date;
  hospital?: string;
  complications?: string;
  recovery?: string;
}

export interface FamilyCondition {
  condition: string;
  relation: 'parent' | 'sibling' | 'grandparent' | 'other';
  ageOfOnset?: number;
}

export interface Lifestyle {
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
  exerciseFrequency: number; // times per week
  exerciseTypes: string[];
  diet: 'omnivore' | 'vegetarian' | 'vegan' | 'keto' | 'paleo' | 'mediterranean' | 'other';
  smoking: boolean;
  smokingHistory?: {
    packsPerDay: number;
    yearsSmoked: number;
    quitDate?: Date;
  };
  alcohol: 'none' | 'light' | 'moderate' | 'heavy';
  alcoholUnitsPerWeek?: number;
  sleep: {
    averageHours: number;
    quality: 1 | 2 | 3 | 4 | 5;
    bedtime?: string;
    wakeTime?: string;
    sleepDisorders?: string[];
  };
  stress: {
    level: 1 | 2 | 3 | 4 | 5;
    sources: string[];
    managementTechniques: string[];
  };
  environment: {
    location: string;
    pollutionLevel: 'low' | 'moderate' | 'high';
    waterQuality: 'excellent' | 'good' | 'fair' | 'poor';
    sunExposure: 'minimal' | 'moderate' | 'high';
  };
}

export interface HealthGoal {
  id: string;
  category: 'weight' | 'energy' | 'sleep' | 'mood' | 'immunity' | 'cognitive' | 'athletic' | 'other';
  description: string;
  targetValue?: number;
  currentValue?: number;
  unit?: string;
  deadline?: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'not_started' | 'in_progress' | 'achieved' | 'paused';
}

export interface UserPreferences {
  budget: {
    min: number;
    max: number;
    currency: string;
    monthlyLimit?: number;
  };
  supplementTypes: string[];
  avoidIngredients: string[];
  preferredBrands: string[];
  deliveryPreferences: {
    frequency: 'weekly' | 'biweekly' | 'monthly' | 'as_needed';
    autoReorder: boolean;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    reminderTimes: string[];
  };
  privacy: {
    shareDataForResearch: boolean;
    anonymousAnalytics: boolean;
  };
}

export interface LabResults {
  id: string;
  testDate: Date;
  testType: string;
  results: {
    [biomarker: string]: {
      value: number;
      unit: string;
      referenceRange: string;
      status: 'low' | 'normal' | 'high' | 'critical';
    };
  };
  orderedBy?: string;
  lab?: string;
  notes?: string;
}

export interface BiometricData {
  timestamp: Date;
  heartRate?: number;
  bloodPressure?: {
    systolic: number;
    diastolic: number;
  };
  bloodGlucose?: number;
  oxygenSaturation?: number;
  temperature?: number;
  steps?: number;
  caloriesBurned?: number;
  sleepHours?: number;
  mood?: 1 | 2 | 3 | 4 | 5;
  energyLevel?: 1 | 2 | 3 | 4 | 5;
  stressLevel?: 1 | 2 | 3 | 4 | 5;
}

export interface HealthProfile {
  demographics: Demographics;
  medicalHistory: MedicalHistory;
  lifestyle: Lifestyle;
  goals: HealthGoal[];
  preferences: UserPreferences;
  labResults: LabResults[];
  biometricData: BiometricData[];
  geneticData?: {
    snps: { [gene: string]: string };
    metabolismType: 'fast' | 'normal' | 'slow';
    nutrientSensitivities: string[];
    drugMetabolism: { [drug: string]: 'poor' | 'intermediate' | 'normal' | 'rapid' };
  };
  supplementStack: {
    supplements: Array<{
      supplementId: string;
      name: string;
      dosage: string;
      frequency: string;
      startDate: Date;
      endDate?: Date;
      notes?: string;
      effectiveness?: 1 | 2 | 3 | 4 | 5;
      sideEffects?: string[];
    }>;
    lastUpdated: Date;
  };
}

export interface User {
  _id?: string;
  email: string;
  passwordHash: string;
  profile: {
    firstName: string;
    lastName: string;
    dateOfBirth: Date;
    createdAt: Date;
    updatedAt: Date;
  };
  healthProfile: HealthProfile;
  analytics: {
    healthScore: number;
    lastCalculated: Date;
    trends: Array<{
      metric: string;
      value: number;
      date: Date;
    }>;
    riskFactors: string[];
    recommendations: string[];
  };
  subscription: {
    plan: 'free' | 'premium' | 'professional';
    startDate: Date;
    endDate?: Date;
    features: string[];
  };
}

export class UserService {
  private db: Db;
  private collection: Collection<User>;

  constructor(db: Db) {
    this.db = db;
    this.collection = db.collection<User>('users');
  }

  async createUser(userData: Partial<User>): Promise<User> {
    try {
      const user: User = {
        ...userData,
        profile: {
          ...userData.profile!,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        healthProfile: this.initializeHealthProfile(userData.healthProfile),
        analytics: {
          healthScore: 0,
          lastCalculated: new Date(),
          trends: [],
          riskFactors: [],
          recommendations: [],
        },
        subscription: {
          plan: 'free',
          startDate: new Date(),
          features: ['basic_recommendations', 'health_tracking'],
        },
      } as User;

      const result = await this.collection.insertOne(user);
      user._id = result.insertedId.toString();

      logger.info(`User created successfully: ${user.email}`);
      return user;
    } catch (error) {
      logger.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  private initializeHealthProfile(profile?: Partial<HealthProfile>): HealthProfile {
    return {
      demographics: {
        age: 0,
        gender: 'other',
        weight: 0,
        height: 0,
        bmi: 0,
        ...profile?.demographics,
      },
      medicalHistory: {
        conditions: [],
        medications: [],
        allergies: [],
        surgeries: [],
        familyHistory: [],
        chronicConditions: [],
        ...profile?.medicalHistory,
      },
      lifestyle: {
        activityLevel: 'moderate',
        exerciseFrequency: 0,
        exerciseTypes: [],
        diet: 'omnivore',
        smoking: false,
        alcohol: 'none',
        sleep: {
          averageHours: 8,
          quality: 3,
        },
        stress: {
          level: 3,
          sources: [],
          managementTechniques: [],
        },
        environment: {
          location: '',
          pollutionLevel: 'moderate',
          waterQuality: 'good',
          sunExposure: 'moderate',
        },
        ...profile?.lifestyle,
      },
      goals: profile?.goals || [],
      preferences: {
        budget: {
          min: 0,
          max: 100,
          currency: 'USD',
        },
        supplementTypes: [],
        avoidIngredients: [],
        preferredBrands: [],
        deliveryPreferences: {
          frequency: 'monthly',
          autoReorder: false,
        },
        notifications: {
          email: true,
          sms: false,
          push: true,
          reminderTimes: ['09:00'],
        },
        privacy: {
          shareDataForResearch: false,
          anonymousAnalytics: true,
        },
        ...profile?.preferences,
      },
      labResults: profile?.labResults || [],
      biometricData: profile?.biometricData || [],
      supplementStack: {
        supplements: [],
        lastUpdated: new Date(),
        ...profile?.supplementStack,
      },
    };
  }

  async getUserById(userId: string): Promise<User | null> {
    try {
      const user = await this.collection.findOne({ _id: userId });
      return user;
    } catch (error) {
      logger.error('Error fetching user:', error);
      throw new Error('Failed to fetch user');
    }
  }

  async updateHealthProfile(userId: string, profileUpdate: Partial<HealthProfile>): Promise<User | null> {
    try {
      const user = await this.collection.findOne({ _id: userId });
      if (!user) {
        return null; // User not found
      }

      // Merge existing health profile with the update
      const updatedHealthProfile: HealthProfile = {
        ...user.healthProfile,
        ...profileUpdate,
        // Ensure nested objects are also merged if they are partials
        demographics: { ...user.healthProfile.demographics, ...(profileUpdate.demographics || {}) },
        medicalHistory: { ...user.healthProfile.medicalHistory, ...(profileUpdate.medicalHistory || {}) },
        lifestyle: { ...user.healthProfile.lifestyle, ...(profileUpdate.lifestyle || {}) },
        preferences: { ...user.healthProfile.preferences, ...(profileUpdate.preferences || {}) },
        // For arrays, decide whether to replace or merge. Assuming replace for now.
        goals: profileUpdate.goals !== undefined ? profileUpdate.goals : user.healthProfile.goals,
        labResults: profileUpdate.labResults !== undefined ? profileUpdate.labResults : user.healthProfile.labResults,
        biometricData: profileUpdate.biometricData !== undefined ? profileUpdate.biometricData : user.healthProfile.biometricData,
        // Handle optional properties like geneticData and supplementStack carefully
        geneticData: profileUpdate.geneticData !== undefined ? profileUpdate.geneticData : user.healthProfile.geneticData,
        supplementStack: profileUpdate.supplementStack !== undefined ? profileUpdate.supplementStack : user.healthProfile.supplementStack,
      };

      const result = await this.collection.findOneAndUpdate(
        { _id: userId },
        {
          $set: {
            healthProfile: updatedHealthProfile, // Use the merged object
            'profile.updatedAt': new Date(),
          },
        },
        { returnDocument: 'after' }
      );

      if (result.value) {
        logger.info(`Health profile updated for user: ${userId}`);
        // Recalculate health score after profile update
        await this.calculateHealthScore(userId);
      }

      return result.value;
    } catch (error) {
      logger.error('Error updating health profile:', error);
      throw new Error('Failed to update health profile');
    }
  }

  async addHealthGoal(userId: string, goal: HealthGoal): Promise<User | null> {
    try {
      const result = await this.collection.findOneAndUpdate(
        { _id: userId },
        {
          $push: { 'healthProfile.goals': goal },
          $set: { 'profile.updatedAt': new Date() },
        },
        { returnDocument: 'after' }
      );

      if (result.value) {
        logger.info(`Health goal added for user: ${userId}`);
      }

      return result.value;
    } catch (error) {
      logger.error('Error adding health goal:', error);
      throw new Error('Failed to add health goal');
    }
  }

  async addBiometricData(userId: string, data: BiometricData): Promise<User | null> {
    try {
      const result = await this.collection.findOneAndUpdate(
        { _id: userId },
        {
          $push: { 'healthProfile.biometricData': data },
          $set: { 'profile.updatedAt': new Date() },
        },
        { returnDocument: 'after' }
      );

      if (result.value) {
        logger.info(`Biometric data added for user: ${userId}`);
        // Recalculate health score with new data
        await this.calculateHealthScore(userId);
      }

      return result.value;
    } catch (error) {
      logger.error('Error adding biometric data:', error);
      throw new Error('Failed to add biometric data');
    }
  }

  async addLabResults(userId: string, labResults: LabResults): Promise<User | null> {
    try {
      const result = await this.collection.findOneAndUpdate(
        { _id: userId },
        {
          $push: { 'healthProfile.labResults': labResults },
          $set: { 'profile.updatedAt': new Date() },
        },
        { returnDocument: 'after' }
      );

      if (result.value) {
        logger.info(`Lab results added for user: ${userId}`);
        // Recalculate health score with new lab data
        await this.calculateHealthScore(userId);
      }

      return result.value;
    } catch (error) {
      logger.error('Error adding lab results:', error);
      throw new Error('Failed to add lab results');
    }
  }

  async calculateHealthScore(userId: string): Promise<number> {
    try {
      const user = await this.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const score = this.computeHealthScore(user.healthProfile);

      await this.collection.updateOne(
        { _id: userId },
        {
          $set: {
            'analytics.healthScore': score,
            'analytics.lastCalculated': new Date(),
          },
          $push: {
            'analytics.trends': {
              metric: 'healthScore',
              value: score,
              date: new Date(),
            },
          },
        }
      );

      logger.info(`Health score calculated for user ${userId}: ${score}`);
      return score;
    } catch (error) {
      logger.error('Error calculating health score:', error);
      throw new Error('Failed to calculate health score');
    }
  }

  private computeHealthScore(profile: HealthProfile): number {
    let score = 0;
    let maxScore = 0;

    // Demographics score (20 points)
    maxScore += 20;
    if (profile.demographics.bmi >= 18.5 && profile.demographics.bmi <= 24.9) {
      score += 10;
    } else if (profile.demographics.bmi >= 25 && profile.demographics.bmi <= 29.9) {
      score += 5;
    }
    if (profile.demographics.age >= 18 && profile.demographics.age <= 65) {
      score += 10;
    }

    // Lifestyle score (30 points)
    maxScore += 30;
    if (profile.lifestyle.exerciseFrequency >= 3) score += 10;
    if (profile.lifestyle.sleep.averageHours >= 7 && profile.lifestyle.sleep.averageHours <= 9) score += 10;
    if (!profile.lifestyle.smoking) score += 5;
    if (profile.lifestyle.alcohol === 'none' || profile.lifestyle.alcohol === 'light') score += 5;

    // Medical history score (25 points)
    maxScore += 25;
    if (profile.medicalHistory.conditions.length === 0) score += 15;
    else if (profile.medicalHistory.conditions.length <= 2) score += 10;
    if (profile.medicalHistory.medications.length <= 2) score += 10;

    // Goals and engagement score (25 points)
    maxScore += 25;
    const activeGoals = profile.goals.filter(g => g.status === 'in_progress' || g.status === 'achieved');
    if (activeGoals.length >= 3) score += 15;
    else if (activeGoals.length >= 1) score += 10;
    if (profile.biometricData.length > 0) score += 10;

    // Convert to 0-100 scale
    return Math.round((score / maxScore) * 100);
  }
}
