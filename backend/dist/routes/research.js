"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const ResearchService_1 = require("../services/ResearchService");
const CrawlService_1 = require("../services/CrawlService");
const MedicalAIService_1 = require("../services/MedicalAIService");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
const researchService = new ResearchService_1.ResearchService();
const crawlService = new CrawlService_1.CrawlService();
const medicalAIService = new MedicalAIService_1.MedicalAIService();
router.post('/web-search', [
    (0, express_validator_1.body)('query').isString().isLength({ min: 1, max: 500 }),
    (0, express_validator_1.body)('type').isIn(['web_search', 'academic', 'news', 'supplement_specific']),
    (0, express_validator_1.body)('filters.maxResults').optional().isInt({ min: 1, max: 50 }),
    (0, express_validator_1.body)('filters.timeRange').optional().isIn(['day', 'week', 'month', 'year']),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const researchQuery = {
            query: req.body.query,
            type: req.body.type || 'web_search',
            filters: req.body.filters || {},
            context: req.body.context
        };
        let results;
        switch (researchQuery.type) {
            case 'supplement_specific':
                results = await researchService.searchSupplements(researchQuery);
                break;
            case 'academic':
                results = await researchService.searchTavily(researchQuery);
                break;
            default:
                results = await researchService.comprehensiveSearch(researchQuery);
        }
        return res.json({
            success: true,
            data: results,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Web search failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Search failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.post('/crawl-website', [
    (0, express_validator_1.body)('url').isURL(),
    (0, express_validator_1.body)('type').isIn(['single_page', 'recursive', 'sitemap', 'llms_txt']),
    (0, express_validator_1.body)('options.maxDepth').optional().isInt({ min: 1, max: 5 }),
    (0, express_validator_1.body)('options.maxPages').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.body)('options.chunkSize').optional().isInt({ min: 100, max: 5000 }),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const crawlRequest = {
            url: req.body.url,
            type: req.body.type,
            options: req.body.options || {}
        };
        let results;
        switch (crawlRequest.type) {
            case 'single_page':
                results = await crawlService.crawlSinglePage(crawlRequest);
                break;
            case 'recursive':
                results = await crawlService.crawlRecursive(crawlRequest);
                break;
            case 'sitemap':
                results = await crawlService.crawlSitemap(crawlRequest);
                break;
            case 'llms_txt':
                results = await crawlService.crawlMarkdown(crawlRequest);
                break;
            default:
                results = await crawlService.crawlSinglePage(crawlRequest);
        }
        return res.json({
            success: true,
            data: results,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Website crawl failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Crawl failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.post('/medical-analysis', [
    (0, express_validator_1.body)('text').isString().isLength({ min: 10, max: 10000 }),
    (0, express_validator_1.body)('analysisType').isIn(['supplement', 'interaction', 'side_effects', 'dosage', 'contraindications']),
    (0, express_validator_1.body)('context').optional().isString(),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const analysisRequest = {
            text: req.body.text,
            analysisType: req.body.analysisType,
            context: req.body.context
        };
        const analysis = await medicalAIService.analyzeSupplement(analysisRequest);
        return res.json({
            success: true,
            data: analysis,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Medical analysis failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Analysis failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.post('/analyze-interactions', [
    (0, express_validator_1.body)('substances').isArray({ min: 2, max: 10 }),
    (0, express_validator_1.body)('substances.*').isString().isLength({ min: 1, max: 100 }),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const substances = req.body.substances;
        const interactions = await medicalAIService.analyzeInteractions(substances);
        return res.json({
            success: true,
            data: {
                substances,
                interactions,
                analysisTime: new Date().toISOString()
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Interaction analysis failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Interaction analysis failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.post('/comprehensive-research', [
    (0, express_validator_1.body)('query').isString().isLength({ min: 1, max: 500 }),
    (0, express_validator_1.body)('includeAnalysis').optional().isBoolean(),
    (0, express_validator_1.body)('maxResults').optional().isInt({ min: 1, max: 20 }),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const query = req.body.query;
        const includeAnalysis = req.body.includeAnalysis || true;
        const maxResults = req.body.maxResults || 10;
        const searchResults = await researchService.searchSupplements({
            query,
            type: 'supplement_specific',
            filters: { maxResults }
        });
        const crawlPromises = searchResults.results.slice(0, 3).map(result => crawlService.crawlSinglePage({
            url: result.url,
            type: 'single_page',
            options: { chunkSize: 1000 }
        }).catch((error) => {
            logger_1.logger.warn(`Failed to crawl ${result.url}:`, error);
            return null;
        }));
        const crawlResults = (await Promise.all(crawlPromises)).filter(Boolean);
        let medicalAnalysis = null;
        if (includeAnalysis && crawlResults.length > 0) {
            const combinedContent = crawlResults
                .map(result => result?.content || '')
                .join('\n\n')
                .substring(0, 8000);
            if (combinedContent.length > 50) {
                medicalAnalysis = await medicalAIService.analyzeSupplement({
                    text: combinedContent,
                    analysisType: 'supplement',
                    context: `Research query: ${query}`
                }).catch((error) => {
                    logger_1.logger.warn('Medical analysis failed:', error);
                    return null;
                });
            }
        }
        return res.json({
            success: true,
            data: {
                query,
                searchResults,
                crawlResults,
                medicalAnalysis,
                summary: {
                    totalSearchResults: searchResults.totalResults,
                    crawledPages: crawlResults.length,
                    analysisIncluded: !!medicalAnalysis
                }
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Comprehensive research failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Research failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
router.get('/crawl-progress/:crawlId', async (req, res) => {
    try {
        const crawlId = req.params['crawlId'];
        const progress = await crawlService.getCrawlProgress(crawlId || '');
        return res.json({
            success: true,
            data: progress
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get crawl progress:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to get progress'
        });
    }
});
router.delete('/crawl/:crawlId', async (req, res) => {
    try {
        const crawlId = req.params['crawlId'];
        const cancelled = await crawlService.cancelCrawl(crawlId || '');
        return res.json({
            success: true,
            data: { cancelled }
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to cancel crawl:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to cancel crawl'
        });
    }
});
router.get('/health', async (_req, res) => {
    try {
        const [searchHealth, crawlHealth, medicalHealth] = await Promise.all([
            researchService.healthCheck(),
            crawlService.healthCheck(),
            medicalAIService.healthCheck()
        ]);
        const overallHealth = searchHealth.brave && searchHealth.tavily && crawlHealth && medicalHealth;
        return res.json({
            success: true,
            data: {
                overall: overallHealth,
                services: {
                    search: searchHealth,
                    crawl: crawlHealth,
                    medicalAI: medicalHealth
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Health check failed:', error);
        return res.status(500).json({
            success: false,
            error: 'Health check failed'
        });
    }
});
exports.default = router;
//# sourceMappingURL=research.js.map