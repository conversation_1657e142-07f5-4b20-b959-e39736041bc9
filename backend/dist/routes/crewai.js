"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const CrewAIIntegrationService_1 = require("../services/CrewAIIntegrationService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
router.post('/research/start', [
    (0, express_validator_1.body)('supplementName')
        .notEmpty()
        .withMessage('Supplement name is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Supplement name must be between 2 and 100 characters'),
    (0, express_validator_1.body)('researchDepth')
        .isIn(['basic', 'comprehensive', 'exhaustive'])
        .withMessage('Research depth must be basic, comprehensive, or exhaustive'),
    (0, express_validator_1.body)('researchGoals')
        .isArray()
        .withMessage('Research goals must be an array'),
    (0, express_validator_1.body)('priority')
        .optional()
        .isIn(['low', 'medium', 'high', 'critical'])
        .withMessage('Priority must be low, medium, high, or critical'),
    (0, express_validator_1.body)('userId')
        .optional()
        .isString()
        .withMessage('User ID must be a string'),
    (0, express_validator_1.body)('sessionId')
        .optional()
        .isString()
        .withMessage('Session ID must be a string')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }
        const request = {
            supplementName: req.body.supplementName,
            researchDepth: req.body.researchDepth || 'comprehensive',
            researchGoals: req.body.researchGoals || [],
            priority: req.body.priority || 'medium',
            userId: req.body.userId,
            sessionId: req.body.sessionId
        };
        const flowId = await CrewAIIntegrationService_1.crewAIService.startResearch(request);
        logger_1.logger.info(`Started CrewAI research flow: ${flowId} for supplement: ${request.supplementName}`);
        return res.status(201).json({
            success: true,
            message: 'Research flow started successfully',
            data: {
                flowId,
                supplementName: request.supplementName,
                researchDepth: request.researchDepth,
                estimatedCompletion: new Date(Date.now() + 10 * 60 * 1000)
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error starting CrewAI research:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to start research flow',
            error: error.message
        });
    }
});
router.get('/research/:flowId/progress', [
    (0, express_validator_1.param)('flowId')
        .notEmpty()
        .withMessage('Flow ID is required')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }
        const { flowId } = req.params;
        const progress = CrewAIIntegrationService_1.crewAIService.getFlowProgress(flowId);
        if (!progress) {
            return res.status(404).json({
                success: false,
                message: 'Research flow not found'
            });
        }
        return res.json({
            success: true,
            data: progress
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting research progress:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get research progress',
            error: error.message
        });
    }
});
router.get('/research/:flowId/result', [
    (0, express_validator_1.param)('flowId')
        .notEmpty()
        .withMessage('Flow ID is required')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }
        const { flowId } = req.params;
        const result = CrewAIIntegrationService_1.crewAIService.getFlowResult(flowId);
        if (!result) {
            return res.status(404).json({
                success: false,
                message: 'Research result not found'
            });
        }
        return res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting research result:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get research result',
            error: error.message
        });
    }
});
router.get('/research/active', async (req, res) => {
    try {
        const activeFlows = CrewAIIntegrationService_1.crewAIService.getActiveFlows();
        return res.json({
            success: true,
            data: {
                count: activeFlows.length,
                flows: activeFlows
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting active research flows:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get active research flows',
            error: error.message
        });
    }
});
router.delete('/research/:flowId', [
    (0, express_validator_1.param)('flowId')
        .notEmpty()
        .withMessage('Flow ID is required')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }
        const { flowId } = req.params;
        await CrewAIIntegrationService_1.crewAIService.stopResearch(flowId);
        logger_1.logger.info(`Stopped CrewAI research flow: ${flowId}`);
        return res.json({
            success: true,
            message: 'Research flow stopped successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error stopping research flow:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to stop research flow',
            error: error.message
        });
    }
});
router.get('/agents/status', async (req, res) => {
    try {
        const agentStatuses = CrewAIIntegrationService_1.crewAIService.getAgentStatuses();
        return res.json({
            success: true,
            data: {
                count: agentStatuses.length,
                agents: agentStatuses
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting agent statuses:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get agent statuses',
            error: error.message
        });
    }
});
router.get('/agents/:agentId/status', [
    (0, express_validator_1.param)('agentId')
        .notEmpty()
        .withMessage('Agent ID is required')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }
        const { agentId } = req.params;
        const agentStatus = CrewAIIntegrationService_1.crewAIService.getAgentStatus(agentId);
        if (!agentStatus) {
            return res.status(404).json({
                success: false,
                message: 'Agent not found'
            });
        }
        return res.json({
            success: true,
            data: agentStatus
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting agent status:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get agent status',
            error: error.message
        });
    }
});
router.get('/health', async (req, res) => {
    try {
        const isHealthy = await CrewAIIntegrationService_1.crewAIService.healthCheck();
        return res.json({
            success: true,
            data: {
                status: isHealthy ? 'healthy' : 'unhealthy',
                timestamp: new Date().toISOString(),
                service: 'CrewAI Integration Service'
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error checking CrewAI health:', error);
        return res.status(500).json({
            success: false,
            message: 'Health check failed',
            error: error.message
        });
    }
});
router.get('/capabilities', async (req, res) => {
    try {
        const capabilities = {
            researchDepths: ['basic', 'comprehensive', 'exhaustive'],
            availableAgents: [
                {
                    id: 'research_coordinator',
                    name: 'Research Coordinator',
                    role: 'Orchestrate research and synthesize findings',
                    capabilities: ['project_management', 'evidence_synthesis', 'quality_assessment']
                },
                {
                    id: 'literature_reviewer',
                    name: 'Literature Review Specialist',
                    role: 'Conduct comprehensive literature reviews',
                    capabilities: ['pubmed_search', 'study_quality_assessment', 'evidence_grading']
                },
                {
                    id: 'clinical_analyst',
                    name: 'Clinical Trial Analyst',
                    role: 'Analyze clinical trial data and methodology',
                    capabilities: ['clinical_trials_search', 'statistical_analysis', 'bias_assessment']
                },
                {
                    id: 'interaction_mapper',
                    name: 'Interaction Mapping Expert',
                    role: 'Identify drug-supplement interactions',
                    capabilities: ['interaction_databases', 'pharmacokinetic_modeling', 'pathway_analysis']
                },
                {
                    id: 'safety_assessor',
                    name: 'Safety Assessment Specialist',
                    role: 'Evaluate supplement safety profiles',
                    capabilities: ['adverse_event_analysis', 'risk_assessment', 'population_safety']
                },
                {
                    id: 'efficacy_evaluator',
                    name: 'Efficacy Evaluation Specialist',
                    role: 'Assess efficacy evidence using evidence-based medicine',
                    capabilities: ['evidence_grading', 'effect_size_calculation', 'clinical_significance']
                },
                {
                    id: 'market_analyst',
                    name: 'Market Intelligence Analyst',
                    role: 'Analyze market trends and consumer insights',
                    capabilities: ['market_research', 'consumer_analysis', 'competitive_intelligence']
                },
                {
                    id: 'regulatory_expert',
                    name: 'Regulatory Compliance Expert',
                    role: 'Evaluate regulatory status and compliance',
                    capabilities: ['regulatory_databases', 'compliance_checking', 'quality_standards']
                }
            ],
            researchGoals: [
                'safety_profile',
                'efficacy_evidence',
                'drug_interactions',
                'dosage_guidelines',
                'clinical_trials',
                'market_intelligence',
                'regulatory_compliance',
                'user_reviews'
            ],
            supportedFormats: ['json', 'structured_report', 'executive_summary'],
            maxConcurrentFlows: 5,
            estimatedExecutionTimes: {
                basic: '3-5 minutes',
                comprehensive: '8-12 minutes',
                exhaustive: '15-20 minutes'
            }
        };
        return res.json({
            success: true,
            data: capabilities
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting CrewAI capabilities:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to get capabilities',
            error: error.message
        });
    }
});
exports.default = router;
//# sourceMappingURL=crewai.js.map