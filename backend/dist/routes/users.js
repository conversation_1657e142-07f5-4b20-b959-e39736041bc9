"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const mongodb_1 = require("mongodb");
const UserService_1 = require("../services/UserService");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
let userService;
router.use(async (req, res, next) => {
    if (!userService) {
        try {
            const mongoClient = new mongodb_1.MongoClient(process.env.MONGODB_URI || 'mongodb://localhost:27017');
            await mongoClient.connect();
            const db = mongoClient.db('suplementor');
            userService = new UserService_1.UserService(db);
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize UserService:', error);
            return res.status(500).json({ error: 'Database connection failed' });
        }
    }
    return next();
});
router.post('/', async (req, res) => {
    try {
        const userData = req.body;
        if (!userData.email || !userData.passwordHash || !userData.profile) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        const user = await userService.createUser(userData);
        const { passwordHash, ...userResponse } = user;
        return res.status(201).json({
            success: true,
            data: userResponse,
            message: 'User created successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error creating user:', error);
        return res.status(500).json({ error: 'Failed to create user' });
    }
});
router.get('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await userService.getUserById(userId);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        const { passwordHash, ...userResponse } = user;
        return res.json({
            success: true,
            data: userResponse
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching user:', error);
        return res.status(500).json({ error: 'Failed to fetch user' });
    }
});
router.put('/:userId/health-profile', async (req, res) => {
    try {
        const { userId } = req.params;
        const profileUpdate = req.body;
        const updatedUser = await userService.updateHealthProfile(userId, profileUpdate);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.json({
            success: true,
            data: updatedUser.healthProfile,
            message: 'Health profile updated successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error updating health profile:', error);
        return res.status(500).json({ error: 'Failed to update health profile' });
    }
});
router.post('/:userId/health-goals', async (req, res) => {
    try {
        const { userId } = req.params;
        const goal = req.body;
        if (!goal.id || !goal.category || !goal.description) {
            return res.status(400).json({ error: 'Missing required goal fields' });
        }
        const updatedUser = await userService.addHealthGoal(userId, goal);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.status(201).json({
            success: true,
            data: goal,
            message: 'Health goal added successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error adding health goal:', error);
        return res.status(500).json({ error: 'Failed to add health goal' });
    }
});
router.post('/:userId/biometric-data', async (req, res) => {
    try {
        const { userId } = req.params;
        const biometricData = req.body;
        if (!biometricData.timestamp) {
            biometricData.timestamp = new Date();
        }
        const updatedUser = await userService.addBiometricData(userId, biometricData);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.status(201).json({
            success: true,
            data: biometricData,
            message: 'Biometric data added successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error adding biometric data:', error);
        return res.status(500).json({ error: 'Failed to add biometric data' });
    }
});
router.post('/:userId/lab-results', async (req, res) => {
    try {
        const { userId } = req.params;
        const labResults = req.body;
        if (!labResults.id || !labResults.testDate || !labResults.testType || !labResults.results) {
            return res.status(400).json({ error: 'Missing required lab result fields' });
        }
        const updatedUser = await userService.addLabResults(userId, labResults);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.status(201).json({
            success: true,
            data: labResults,
            message: 'Lab results added successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error adding lab results:', error);
        return res.status(500).json({ error: 'Failed to add lab results' });
    }
});
router.post('/:userId/calculate-health-score', async (req, res) => {
    try {
        const { userId } = req.params;
        const healthScore = await userService.calculateHealthScore(userId);
        return res.json({
            success: true,
            data: {
                healthScore,
                calculatedAt: new Date(),
            },
            message: 'Health score calculated successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error calculating health score:', error);
        return res.status(500).json({ error: 'Failed to calculate health score' });
    }
});
router.get('/:userId/analytics', async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await userService.getUserById(userId);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        const analytics = {
            ...user.analytics,
            profileCompleteness: calculateProfileCompleteness(user.healthProfile),
            recentActivity: getRecentActivity(user.healthProfile),
            goalProgress: calculateGoalProgress(user.healthProfile.goals),
        };
        return res.json({
            success: true,
            data: analytics
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching user analytics:', error);
        return res.status(500).json({ error: 'Failed to fetch user analytics' });
    }
});
function calculateProfileCompleteness(profile) {
    let completedFields = 0;
    let totalFields = 0;
    totalFields += 5;
    if (profile.demographics.age > 0)
        completedFields++;
    if (profile.demographics.gender !== 'other')
        completedFields++;
    if (profile.demographics.weight > 0)
        completedFields++;
    if (profile.demographics.height > 0)
        completedFields++;
    if (profile.demographics.bmi > 0)
        completedFields++;
    totalFields += 5;
    if (profile.lifestyle.exerciseFrequency > 0)
        completedFields++;
    if (profile.lifestyle.diet !== 'omnivore')
        completedFields++;
    if (profile.lifestyle.sleep.averageHours > 0)
        completedFields++;
    if (profile.lifestyle.stress.level > 0)
        completedFields++;
    if (profile.lifestyle.environment.location)
        completedFields++;
    totalFields += 1;
    if (profile.goals.length > 0)
        completedFields++;
    totalFields += 1;
    if (profile.labResults.length > 0)
        completedFields++;
    totalFields += 1;
    if (profile.biometricData.length > 0)
        completedFields++;
    return Math.round((completedFields / totalFields) * 100);
}
function getRecentActivity(profile) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentBiometrics = profile.biometricData.filter(data => new Date(data.timestamp) > thirtyDaysAgo);
    const recentLabs = profile.labResults.filter(lab => new Date(lab.testDate) > thirtyDaysAgo);
    return {
        biometricEntries: recentBiometrics.length,
        labResults: recentLabs.length,
        lastActivity: recentBiometrics.length > 0
            ? Math.max(...recentBiometrics.map(b => new Date(b.timestamp).getTime()))
            : null,
    };
}
function calculateGoalProgress(goals) {
    const totalGoals = goals.length;
    const achievedGoals = goals.filter(g => g.status === 'achieved').length;
    const inProgressGoals = goals.filter(g => g.status === 'in_progress').length;
    const notStartedGoals = goals.filter(g => g.status === 'not_started').length;
    return {
        total: totalGoals,
        achieved: achievedGoals,
        inProgress: inProgressGoals,
        notStarted: notStartedGoals,
        completionRate: totalGoals > 0 ? Math.round((achievedGoals / totalGoals) * 100) : 0,
    };
}
exports.default = router;
//# sourceMappingURL=users.js.map