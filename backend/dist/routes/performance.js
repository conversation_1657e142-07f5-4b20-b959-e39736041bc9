"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const PerformanceMonitoringService_1 = require("../services/PerformanceMonitoringService");
const EnhancedCacheService_1 = require("../services/EnhancedCacheService");
const DatabaseConnectionManager_1 = require("../services/DatabaseConnectionManager");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
router.get('/stats', async (req, res) => {
    try {
        const timeRange = parseInt(req.query.timeRange) || 3600000;
        const stats = PerformanceMonitoringService_1.performanceMonitoringService.getPerformanceStats(timeRange);
        res.json({
            success: true,
            data: stats,
            timeRange,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get performance stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve performance statistics'
        });
    }
});
router.get('/system', async (req, res) => {
    try {
        const systemMetrics = await PerformanceMonitoringService_1.performanceMonitoringService.getCurrentSystemMetrics();
        res.json({
            success: true,
            data: systemMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get system metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve system metrics'
        });
    }
});
router.get('/cache', async (req, res) => {
    try {
        const cacheStats = EnhancedCacheService_1.enhancedCacheService.getCacheStats();
        const cacheMetrics = EnhancedCacheService_1.enhancedCacheService.getCacheMetrics(100);
        res.json({
            success: true,
            data: {
                stats: cacheStats,
                metrics: cacheMetrics
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get cache metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve cache metrics'
        });
    }
});
router.get('/database', async (req, res) => {
    try {
        const connectionStats = await DatabaseConnectionManager_1.databaseConnectionManager.getConnectionPoolStats();
        const databaseHealth = DatabaseConnectionManager_1.databaseConnectionManager.getDatabaseHealth();
        res.json({
            success: true,
            data: {
                connections: connectionStats,
                health: databaseHealth
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get database metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve database metrics'
        });
    }
});
router.get('/alerts', async (req, res) => {
    try {
        const resolved = req.query.resolved === 'true';
        const alerts = PerformanceMonitoringService_1.performanceMonitoringService.getAlerts(resolved);
        res.json({
            success: true,
            data: alerts,
            count: alerts.length,
            resolved,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get performance alerts:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve performance alerts'
        });
    }
});
router.post('/alerts/:alertId/resolve', async (req, res) => {
    try {
        const { alertId } = req.params;
        PerformanceMonitoringService_1.performanceMonitoringService.resolveAlert(alertId);
        res.json({
            success: true,
            message: 'Alert resolved successfully',
            alertId,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to resolve alert:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to resolve alert'
        });
    }
});
router.get('/report', async (req, res) => {
    try {
        const timeRange = parseInt(req.query.timeRange) || 3600000;
        const report = PerformanceMonitoringService_1.performanceMonitoringService.getPerformanceReport(timeRange);
        res.json({
            success: true,
            data: report,
            timeRange,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to generate performance report:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate performance report'
        });
    }
});
router.post('/cache/clear', async (req, res) => {
    try {
        await EnhancedCacheService_1.enhancedCacheService.clear();
        res.json({
            success: true,
            message: 'All caches cleared successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to clear cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to clear cache'
        });
    }
});
router.post('/cache/invalidate', async (req, res) => {
    try {
        const { tags } = req.body;
        if (!tags || !Array.isArray(tags)) {
            return res.status(400).json({
                success: false,
                error: 'Tags array is required'
            });
        }
        await EnhancedCacheService_1.enhancedCacheService.invalidateByTags(tags);
        res.json({
            success: true,
            message: 'Cache invalidated successfully',
            tags,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to invalidate cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to invalidate cache'
        });
    }
});
router.post('/database/health-check', async (req, res) => {
    try {
        await DatabaseConnectionManager_1.databaseConnectionManager.performHealthChecks();
        const health = DatabaseConnectionManager_1.databaseConnectionManager.getDatabaseHealth();
        res.json({
            success: true,
            message: 'Health check completed',
            data: health,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to perform health check:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to perform health check'
        });
    }
});
router.get('/metrics/export', async (req, res) => {
    try {
        const format = req.query.format || 'json';
        const timeRange = parseInt(req.query.timeRange) || 3600000;
        const report = PerformanceMonitoringService_1.performanceMonitoringService.getPerformanceReport(timeRange);
        const cacheStats = EnhancedCacheService_1.enhancedCacheService.getCacheStats();
        const systemMetrics = await PerformanceMonitoringService_1.performanceMonitoringService.getCurrentSystemMetrics();
        const exportData = {
            performance: report,
            cache: cacheStats,
            system: systemMetrics,
            exportTime: new Date().toISOString(),
            timeRange
        };
        switch (format.toLowerCase()) {
            case 'csv':
                const csvData = convertToCSV(exportData);
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', 'attachment; filename=performance-metrics.csv');
                res.send(csvData);
                break;
            case 'json':
            default:
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', 'attachment; filename=performance-metrics.json');
                res.json(exportData);
                break;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to export metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to export metrics'
        });
    }
});
router.get('/dashboard', async (req, res) => {
    try {
        const timeRange = parseInt(req.query.timeRange) || 3600000;
        const [performanceStats, systemMetrics, cacheStats, databaseHealth, alerts] = await Promise.all([
            PerformanceMonitoringService_1.performanceMonitoringService.getPerformanceStats(timeRange),
            PerformanceMonitoringService_1.performanceMonitoringService.getCurrentSystemMetrics(),
            EnhancedCacheService_1.enhancedCacheService.getCacheStats(),
            DatabaseConnectionManager_1.databaseConnectionManager.getDatabaseHealth(),
            PerformanceMonitoringService_1.performanceMonitoringService.getAlerts(false)
        ]);
        const dashboardData = {
            overview: {
                totalRequests: performanceStats.total,
                averageResponseTime: performanceStats.averageResponseTime,
                successRate: performanceStats.successRate,
                errorRate: performanceStats.errorRate,
                activeAlerts: alerts.length
            },
            system: {
                cpu: systemMetrics.cpu,
                memory: systemMetrics.memory,
                uptime: process.uptime()
            },
            cache: {
                hitRate: cacheStats.hitRate,
                totalKeys: cacheStats.totalKeys,
                memoryUsage: cacheStats.memoryUsage
            },
            database: {
                health: databaseHealth,
                connections: await DatabaseConnectionManager_1.databaseConnectionManager.getConnectionPoolStats()
            },
            performance: {
                byType: performanceStats.byType,
                slowestOperations: performanceStats.slowestOperations.slice(0, 5),
                fastestOperations: performanceStats.fastestOperations.slice(0, 5)
            },
            alerts: alerts.slice(0, 10),
            timestamp: new Date().toISOString()
        };
        res.json({
            success: true,
            data: dashboardData
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get dashboard data:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve dashboard data'
        });
    }
});
function convertToCSV(data) {
    const headers = ['timestamp', 'metric', 'value', 'type'];
    const rows = [headers.join(',')];
    if (data.performance && data.performance.byType) {
        Object.entries(data.performance.byType).forEach(([type, metrics]) => {
            rows.push([data.exportTime, 'averageTime', metrics.averageTime, type].join(','));
            rows.push([data.exportTime, 'successRate', metrics.successRate, type].join(','));
            rows.push([data.exportTime, 'errorRate', metrics.errorRate, type].join(','));
        });
    }
    if (data.system) {
        rows.push([data.exportTime, 'memoryUsage', data.system.memory.percentage, 'system'].join(','));
        rows.push([data.exportTime, 'cpuUsage', data.system.cpu.usage, 'system'].join(','));
    }
    if (data.cache) {
        rows.push([data.exportTime, 'cacheHitRate', data.cache.hitRate, 'cache'].join(','));
        rows.push([data.exportTime, 'cacheKeys', data.cache.totalKeys, 'cache'].join(','));
    }
    return rows.join('\n');
}
exports.default = router;
//# sourceMappingURL=performance.js.map