"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const redis_1 = require("redis");
const SupplementService_1 = require("../services/SupplementService");
const InteractionService_1 = require("../services/InteractionService");
const BudgetOptimizationService_1 = require("../services/BudgetOptimizationService");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
let supplementService;
let interactionService;
let budgetOptimizationService;
router.use(async (req, res, next) => {
    if (!supplementService) {
        try {
            const neo4j = require('neo4j-driver');
            const neo4jDriver = neo4j.driver(process.env.NEO4J_URI || 'bolt://localhost:7687', neo4j.auth.basic(process.env.NEO4J_USER || 'neo4j', process.env.NEO4J_PASSWORD || 'password'));
            const redisClient = (0, redis_1.createClient)({
                url: process.env.REDIS_URL || 'redis://localhost:6379'
            });
            await redisClient.connect();
            supplementService = new SupplementService_1.SupplementService(neo4jDriver, redisClient);
            interactionService = new InteractionService_1.InteractionService(neo4jDriver, redisClient);
            budgetOptimizationService = new BudgetOptimizationService_1.BudgetOptimizationService();
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize services:', error);
            return res.status(500).json({ error: 'Service initialization failed' });
        }
    }
    return next();
});
router.get('/search', async (req, res) => {
    try {
        const query = {
            term: req.query.term,
            category: req.query.category,
            effects: req.query.effects ? req.query.effects.split(',') : undefined,
            priceRange: req.query.minPrice && req.query.maxPrice ? {
                min: parseFloat(req.query.minPrice),
                max: parseFloat(req.query.maxPrice)
            } : undefined,
            safetyRating: req.query.safetyRating ? parseFloat(req.query.safetyRating) : undefined,
            limit: req.query.limit ? parseInt(req.query.limit) : 20,
            offset: req.query.offset ? parseInt(req.query.offset) : 0,
        };
        const supplements = await supplementService.searchSupplements(query);
        return res.json({
            success: true,
            data: supplements,
            pagination: {
                limit: query.limit,
                offset: query.offset,
                total: supplements.length,
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error searching supplements:', error);
        return res.status(500).json({ error: 'Failed to search supplements' });
    }
});
router.get('/:supplementId', async (req, res) => {
    try {
        const { supplementId } = req.params;
        const supplement = await supplementService.getSupplementById(supplementId);
        if (!supplement) {
            return res.status(404).json({ error: 'Supplement not found' });
        }
        return res.json({
            success: true,
            data: supplement
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching supplement:', error);
        return res.status(500).json({ error: 'Failed to fetch supplement' });
    }
});
router.get('/effects/:effectName', async (req, res) => {
    try {
        const { effectName } = req.params;
        const limit = req.query.limit ? parseInt(req.query.limit) : 10;
        const supplements = await supplementService.getSupplementsByEffect(effectName, limit);
        return res.json({
            success: true,
            data: supplements,
            meta: {
                effect: effectName,
                count: supplements.length,
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching supplements by effect:', error);
        return res.status(500).json({ error: 'Failed to fetch supplements by effect' });
    }
});
router.post('/analyze-interactions', async (req, res) => {
    try {
        const { supplementIds } = req.body;
        if (!Array.isArray(supplementIds) || supplementIds.length === 0) {
            return res.status(400).json({ error: 'Invalid supplement IDs provided' });
        }
        const interactions = await supplementService.getSupplementInteractions(supplementIds);
        return res.json({
            success: true,
            data: interactions,
            meta: {
                supplementCount: supplementIds.length,
                interactionCount: interactions.length,
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error analyzing interactions:', error);
        return res.status(500).json({ error: 'Failed to analyze interactions' });
    }
});
router.post('/safety-analysis', async (req, res) => {
    try {
        const { supplements, healthProfile } = req.body;
        if (!Array.isArray(supplements) || !healthProfile) {
            return res.status(400).json({ error: 'Invalid request data' });
        }
        const safetyAnalysis = await interactionService.analyzeSupplementStack(supplements, healthProfile);
        return res.json({
            success: true,
            data: safetyAnalysis,
            meta: {
                supplementCount: supplements.length,
                analysisTimestamp: new Date(),
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error performing safety analysis:', error);
        return res.status(500).json({ error: 'Failed to perform safety analysis' });
    }
});
router.post('/optimize-stack', async (req, res) => {
    try {
        const { availableSupplements, criteria, healthProfile, safetyAnalysis } = req.body;
        if (!Array.isArray(availableSupplements) || !criteria || !healthProfile) {
            return res.status(400).json({ error: 'Invalid optimization request data' });
        }
        const optimizedStack = await budgetOptimizationService.optimizeSupplementStack(availableSupplements, criteria, healthProfile, safetyAnalysis);
        return res.json({
            success: true,
            data: optimizedStack,
            meta: {
                optimizationTimestamp: new Date(),
                inputSupplementCount: availableSupplements.length,
                selectedSupplementCount: optimizedStack.supplements.length,
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error optimizing supplement stack:', error);
        return res.status(500).json({ error: 'Failed to optimize supplement stack' });
    }
});
router.post('/recommendations/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const { preferences } = req.body;
        const recommendations = {
            primary: [],
            alternatives: [],
            reasoning: [
                'Based on your health goals and profile',
                'Optimized for safety and effectiveness',
                'Considers your budget constraints',
            ],
        };
        return res.json({
            success: true,
            data: recommendations,
            meta: {
                userId,
                generatedAt: new Date(),
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error generating recommendations:', error);
        return res.status(500).json({ error: 'Failed to generate recommendations' });
    }
});
router.get('/categories', async (req, res) => {
    try {
        const categories = [
            { id: 'vitamins', name: 'Vitamins', count: 150 },
            { id: 'minerals', name: 'Minerals', count: 85 },
            { id: 'herbs', name: 'Herbs & Botanicals', count: 200 },
            { id: 'amino-acids', name: 'Amino Acids', count: 45 },
            { id: 'probiotics', name: 'Probiotics', count: 30 },
            { id: 'omega-3', name: 'Omega-3 Fatty Acids', count: 25 },
            { id: 'antioxidants', name: 'Antioxidants', count: 60 },
            { id: 'adaptogens', name: 'Adaptogens', count: 40 },
        ];
        return res.json({
            success: true,
            data: categories
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching categories:', error);
        return res.status(500).json({ error: 'Failed to fetch categories' });
    }
});
router.get('/popular', async (req, res) => {
    try {
        const limit = req.query.limit ? parseInt(req.query.limit) : 10;
        const query = {
            term: '',
            limit,
            offset: 0,
        };
        const supplements = await supplementService.searchSupplements(query);
        return res.json({
            success: true,
            data: supplements.slice(0, limit),
            meta: {
                type: 'popular',
                count: supplements.length,
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching popular supplements:', error);
        return res.status(500).json({ error: 'Failed to fetch popular supplements' });
    }
});
router.get('/trends', async (req, res) => {
    try {
        const trends = {
            trending: [
                { name: 'Vitamin D3', change: '+15%', category: 'vitamins' },
                { name: 'Magnesium Glycinate', change: '+12%', category: 'minerals' },
                { name: 'Ashwagandha', change: '+8%', category: 'adaptogens' },
            ],
            seasonal: [
                { name: 'Immune Support', reason: 'Winter season' },
                { name: 'Mood Support', reason: 'Seasonal changes' },
            ],
            research: [
                { name: 'NMN', reason: 'New longevity research' },
                { name: 'Lion\'s Mane', reason: 'Cognitive health studies' },
            ],
        };
        return res.json({
            success: true,
            data: trends,
            meta: {
                generatedAt: new Date(),
                period: 'last_30_days',
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching trends:', error);
        return res.status(500).json({ error: 'Failed to fetch trends' });
    }
});
exports.default = router;
//# sourceMappingURL=supplements.js.map