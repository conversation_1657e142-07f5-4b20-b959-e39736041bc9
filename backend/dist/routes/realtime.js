"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const logger_1 = require("../utils/logger");
const PerformanceMonitoringService_1 = require("../services/PerformanceMonitoringService");
const router = (0, express_1.Router)();
router.get('/sessions', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('get_sessions', 'GET');
    try {
        const sessions = [
            {
                id: 'session_001',
                userId: 'user_123',
                title: 'Vitamin D Research Analysis',
                type: 'autonomous',
                status: 'running',
                progress: 65,
                currentTask: 'Analyzing research papers',
                estimatedCompletion: new Date(Date.now() + 300000),
                createdAt: new Date(Date.now() - 600000)
            },
            {
                id: 'session_002',
                userId: 'user_456',
                title: 'Magnesium Interaction Study',
                type: 'collaborative',
                status: 'completed',
                progress: 100,
                currentTask: 'Completed',
                estimatedCompletion: new Date(Date.now() - 60000),
                createdAt: new Date(Date.now() - 1800000)
            }
        ];
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            data: sessions,
            count: sessions.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get sessions');
        logger_1.logger.error('Failed to get research sessions:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve research sessions'
        });
    }
});
router.post('/sessions', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('create_session', 'POST');
    try {
        const { title, description, type, query, userId, roomId, settings } = req.body;
        if (!title || !type || !query || !userId) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: title, type, query, userId'
            });
        }
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const session = {
            id: sessionId,
            userId,
            roomId,
            title,
            description,
            type,
            status: 'initializing',
            progress: 0,
            currentTask: 'Initializing research session...',
            estimatedCompletion: new Date(Date.now() + 300000),
            createdAt: new Date(),
            settings: {
                maxConcurrentTasks: 3,
                autoRetry: true,
                retryAttempts: 2,
                timeoutMs: 120000,
                qualityThreshold: 0.7,
                enableRealTimeUpdates: true,
                ...settings
            }
        };
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.status(201).json({
            success: true,
            data: session,
            message: 'Research session created successfully'
        });
        logger_1.logger.info(`Research session created: ${sessionId} for user ${userId}`);
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to create session');
        logger_1.logger.error('Failed to create research session:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create research session'
        });
    }
});
router.get('/sessions/:sessionId', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('get_session_details', 'GET');
    try {
        const { sessionId } = req.params;
        const session = {
            id: sessionId,
            userId: 'user_123',
            title: 'Vitamin D Research Analysis',
            description: 'Comprehensive analysis of vitamin D supplementation research',
            type: 'autonomous',
            status: 'running',
            progress: 65,
            currentTask: 'Analyzing research papers',
            tasks: [
                {
                    id: 'task_001',
                    type: 'search',
                    query: 'vitamin D supplementation immune function',
                    status: 'completed',
                    progress: 100,
                    results: { papers: 45, relevantFindings: 23 }
                },
                {
                    id: 'task_002',
                    type: 'analysis',
                    query: 'analyze vitamin D research quality',
                    status: 'running',
                    progress: 65,
                    results: null
                },
                {
                    id: 'task_003',
                    type: 'synthesis',
                    query: 'synthesize vitamin D findings',
                    status: 'pending',
                    progress: 0,
                    results: null
                }
            ],
            metadata: {
                totalTasks: 3,
                completedTasks: 1,
                failedTasks: 0,
                estimatedCompletion: new Date(Date.now() + 300000),
                qualityScore: 85,
                confidence: 92
            },
            createdAt: new Date(Date.now() - 600000),
            updatedAt: new Date(Date.now() - 60000)
        };
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            data: session
        });
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get session details');
        logger_1.logger.error('Failed to get session details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve session details'
        });
    }
});
router.post('/sessions/:sessionId/pause', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('pause_session', 'POST');
    try {
        const { sessionId } = req.params;
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            message: 'Research session paused successfully',
            sessionId,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info(`Research session paused: ${sessionId}`);
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to pause session');
        logger_1.logger.error('Failed to pause session:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to pause research session'
        });
    }
});
router.post('/sessions/:sessionId/resume', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('resume_session', 'POST');
    try {
        const { sessionId } = req.params;
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            message: 'Research session resumed successfully',
            sessionId,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info(`Research session resumed: ${sessionId}`);
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to resume session');
        logger_1.logger.error('Failed to resume session:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to resume research session'
        });
    }
});
router.delete('/sessions/:sessionId', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('cancel_session', 'DELETE');
    try {
        const { sessionId } = req.params;
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            message: 'Research session cancelled successfully',
            sessionId,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info(`Research session cancelled: ${sessionId}`);
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to cancel session');
        logger_1.logger.error('Failed to cancel session:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to cancel research session'
        });
    }
});
router.get('/agents', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('get_agents', 'GET');
    try {
        const agents = [
            {
                id: 'agent_search_1',
                name: 'Search Agent Alpha',
                type: 'search',
                status: 'busy',
                capabilities: ['web_search', 'academic_search', 'news_search', 'social_search'],
                currentTask: 'task_002',
                performance: {
                    tasksCompleted: 156,
                    averageTime: 45000,
                    successRate: 94.2,
                    qualityScore: 85
                },
                lastActivity: new Date(Date.now() - 30000)
            },
            {
                id: 'agent_analysis_1',
                name: 'Analysis Agent Beta',
                type: 'analysis',
                status: 'idle',
                capabilities: ['content_analysis', 'sentiment_analysis', 'trend_analysis', 'statistical_analysis'],
                currentTask: null,
                performance: {
                    tasksCompleted: 89,
                    averageTime: 62000,
                    successRate: 96.8,
                    qualityScore: 90
                },
                lastActivity: new Date(Date.now() - 120000)
            },
            {
                id: 'agent_synthesis_1',
                name: 'Synthesis Agent Gamma',
                type: 'synthesis',
                status: 'idle',
                capabilities: ['data_synthesis', 'report_generation', 'insight_extraction', 'pattern_recognition'],
                currentTask: null,
                performance: {
                    tasksCompleted: 67,
                    averageTime: 78000,
                    successRate: 92.5,
                    qualityScore: 88
                },
                lastActivity: new Date(Date.now() - 180000)
            },
            {
                id: 'agent_validation_1',
                name: 'Validation Agent Delta',
                type: 'validation',
                status: 'idle',
                capabilities: ['fact_checking', 'source_verification', 'quality_assessment', 'bias_detection'],
                currentTask: null,
                performance: {
                    tasksCompleted: 134,
                    averageTime: 38000,
                    successRate: 98.1,
                    qualityScore: 92
                },
                lastActivity: new Date(Date.now() - 90000)
            }
        ];
        const summary = {
            totalAgents: agents.length,
            activeAgents: agents.filter(a => a.status !== 'offline').length,
            busyAgents: agents.filter(a => a.status === 'busy').length,
            averagePerformance: {
                tasksCompleted: Math.round(agents.reduce((sum, a) => sum + a.performance.tasksCompleted, 0) / agents.length),
                averageTime: Math.round(agents.reduce((sum, a) => sum + a.performance.averageTime, 0) / agents.length),
                successRate: Math.round(agents.reduce((sum, a) => sum + a.performance.successRate, 0) / agents.length * 10) / 10,
                qualityScore: Math.round(agents.reduce((sum, a) => sum + a.performance.qualityScore, 0) / agents.length)
            }
        };
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            data: {
                agents,
                summary
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get agents');
        logger_1.logger.error('Failed to get agents:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve agents information'
        });
    }
});
router.get('/workspaces', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('get_workspaces', 'GET');
    try {
        const userId = req.query.userId;
        if (!userId) {
            return res.status(400).json({
                success: false,
                error: 'userId parameter is required'
            });
        }
        const workspaces = [
            {
                id: 'workspace_001',
                name: 'Vitamin D Research Project',
                description: 'Collaborative research on vitamin D supplementation',
                type: 'research',
                ownerId: 'user_123',
                memberCount: 3,
                documentCount: 5,
                lastActivity: new Date(Date.now() - 300000),
                isOnline: true
            },
            {
                id: 'workspace_002',
                name: 'Magnesium Analysis',
                description: 'Analysis of magnesium forms and bioavailability',
                type: 'analysis',
                ownerId: 'user_456',
                memberCount: 2,
                documentCount: 3,
                lastActivity: new Date(Date.now() - 600000),
                isOnline: false
            }
        ];
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            data: workspaces,
            count: workspaces.length,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get workspaces');
        logger_1.logger.error('Failed to get workspaces:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve workspaces'
        });
    }
});
router.post('/workspaces', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('create_workspace', 'POST');
    try {
        const { name, description, type, ownerId, ownerUsername, settings } = req.body;
        if (!name || !type || !ownerId || !ownerUsername) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: name, type, ownerId, ownerUsername'
            });
        }
        const workspaceId = `workspace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const workspace = {
            id: workspaceId,
            name,
            description,
            type,
            ownerId,
            memberCount: 1,
            documentCount: 0,
            settings: {
                isPublic: false,
                allowGuestAccess: false,
                requireApproval: true,
                enableVersioning: true,
                enableComments: true,
                enableRealTimeEditing: true,
                autoSave: true,
                autoSaveInterval: 30,
                ...settings
            },
            createdAt: new Date(),
            lastActivity: new Date()
        };
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.status(201).json({
            success: true,
            data: workspace,
            message: 'Workspace created successfully'
        });
        logger_1.logger.info(`Workspace created: ${name} (${workspaceId}) by ${ownerUsername}`);
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to create workspace');
        logger_1.logger.error('Failed to create workspace:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create workspace'
        });
    }
});
router.get('/status', async (req, res) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('get_realtime_status', 'GET');
    try {
        const status = {
            websocket: {
                connected: true,
                activeConnections: 23,
                rooms: 5,
                messagesPerMinute: 45
            },
            orchestrator: {
                running: true,
                activeSessions: 3,
                queuedTasks: 7,
                busyAgents: 1
            },
            workspaces: {
                active: 2,
                totalUsers: 8,
                documentsBeingEdited: 3
            },
            performance: {
                averageResponseTime: 125,
                successRate: 98.5,
                errorRate: 1.5
            },
            timestamp: new Date().toISOString()
        };
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
        res.json({
            success: true,
            data: status
        });
    }
    catch (error) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Failed to get status');
        logger_1.logger.error('Failed to get real-time status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve real-time status'
        });
    }
});
exports.default = router;
//# sourceMappingURL=realtime.js.map