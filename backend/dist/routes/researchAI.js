"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const ResearchAIService_1 = __importDefault(require("@/services/ResearchAIService"));
const UserProfileService_1 = __importDefault(require("@/services/UserProfileService"));
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const router = express_1.default.Router();
const researchAIService = new ResearchAIService_1.default();
const userProfileService = new UserProfileService_1.default();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError('Validation failed');
    }
    next();
};
router.post('/search', (0, express_validator_1.body)('query').optional().isString(), (0, express_validator_1.body)('supplement').optional().isString(), (0, express_validator_1.body)('condition').optional().isString(), (0, express_validator_1.body)('studyType').optional().isIn(['RCT', 'Meta-Analysis', 'Observational', 'Review', 'Case Study']), (0, express_validator_1.body)('minYear').optional().isInt({ min: 1990, max: 2024 }), (0, express_validator_1.body)('minEvidenceLevel').optional().isInt({ min: 1, max: 5 }), (0, express_validator_1.body)('includeMetaAnalysis').optional().isBoolean(), (0, express_validator_1.body)('maxResults').optional().isInt({ min: 1, max: 100 }), (0, express_validator_1.body)('userId').optional().isString(), validateRequest, async (req, res, next) => {
    try {
        const { query, supplement, condition, studyType, minYear, minEvidenceLevel, includeMetaAnalysis, maxResults = 20, userId, } = req.body;
        const researchQuery = {
            supplement: supplement || query,
            condition,
            studyType,
            minYear,
            minEvidenceLevel,
            includeMetaAnalysis,
            maxResults,
        };
        const papers = await researchAIService.searchResearchPapers(researchQuery);
        let userProfile = null;
        if (userId) {
            userProfile = await userProfileService.getUserProfile(userId);
        }
        const aiInsights = await researchAIService.generateAIInsights(papers, userProfile);
        const stats = {
            totalPapers: papers.length,
            avgQualityScore: papers.reduce((sum, p) => sum + p.qualityScore, 0) / papers.length,
            avgEvidenceLevel: papers.reduce((sum, p) => sum + p.evidenceLevel, 0) / papers.length,
            avgConfidence: papers.reduce((sum, p) => sum + p.findings.confidence, 0) / papers.length,
            studyTypeDistribution: papers.reduce((acc, p) => {
                acc[p.studyType] = (acc[p.studyType] || 0) + 1;
                return acc;
            }, {}),
        };
        logger_1.logger.info(`Research AI search completed`, {
            query: researchQuery,
            resultsCount: papers.length,
            insightsCount: aiInsights.length,
            userId,
        });
        return res.json({
            success: true,
            data: {
                papers,
                insights: aiInsights,
                stats,
                query: researchQuery,
            },
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/insights/:userId', (0, express_validator_1.param)('userId').isString().notEmpty(), (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 50 }), validateRequest, async (req, res, next) => {
    try {
        const { userId } = req.params;
        const limit = parseInt(req.query.limit) || 10;
        const userProfile = await userProfileService.getUserProfile(userId);
        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found',
            });
        }
        const userSupplements = userProfile.currentSupplements || [];
        const researchPromises = userSupplements.slice(0, 5).map(supplement => researchAIService.searchResearchPapers({
            supplement,
            minEvidenceLevel: 3,
            maxResults: 5,
        }));
        const researchResults = await Promise.all(researchPromises);
        const allPapers = researchResults.flat();
        const insights = await researchAIService.generateAIInsights(allPapers, userProfile);
        return res.json({
            success: true,
            data: insights.slice(0, limit),
            count: insights.length,
            userSupplements,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
});
router.post('/analyze-interactions', (0, express_validator_1.body)('supplementIds').isArray().notEmpty(), (0, express_validator_1.body)('userId').optional().isString(), validateRequest, async (req, res, next) => {
    try {
        const { supplementIds, userId } = req.body;
        const interactions = await researchAIService.analyzeSupplementInteractions(supplementIds);
        let userProfile = null;
        if (userId) {
            userProfile = await userProfileService.getUserProfile(userId);
        }
        const interactionInsights = interactions.map(interaction => ({
            ...interaction,
            personalizedRisk: userProfile ? calculatePersonalizedRisk(interaction, userProfile) : null,
            recommendations: generateInteractionRecommendations(interaction),
        }));
        const overallScore = calculateOverallInteractionScore(interactions);
        return res.json({
            success: true,
            data: {
                interactions: interactionInsights,
                overallScore,
                riskLevel: overallScore >= 8 ? 'low' : overallScore >= 6 ? 'moderate' : 'high',
                summary: generateInteractionSummary(interactions),
            },
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/monitor/:userId', (0, express_validator_1.param)('userId').isString().notEmpty(), validateRequest, async (req, res, next) => {
    try {
        const { userId } = req.params;
        const userProfile = await userProfileService.getUserProfile(userId);
        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found',
            });
        }
        const userSupplements = userProfile.currentSupplements || [];
        const monitoringData = await researchAIService.monitorRealTimeResearch(userSupplements);
        return res.json({
            success: true,
            data: monitoringData,
            userSupplements,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
});
router.post('/generate-report', (0, express_validator_1.body)('userId').isString().notEmpty(), (0, express_validator_1.body)('supplements').optional().isArray(), (0, express_validator_1.body)('timeframe').optional().isIn(['week', 'month', 'quarter', 'year']), (0, express_validator_1.body)('includeInteractions').optional().isBoolean(), (0, express_validator_1.body)('includePersonalized').optional().isBoolean(), validateRequest, async (req, res, next) => {
    try {
        const { userId, supplements, timeframe = 'month', includeInteractions = true, includePersonalized = true, } = req.body;
        const userProfile = await userProfileService.getUserProfile(userId);
        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found',
            });
        }
        const targetSupplements = supplements || userProfile.currentSupplements || [];
        const report = await generateComprehensiveReport(targetSupplements, userProfile, timeframe, includeInteractions, includePersonalized);
        return res.json({
            success: true,
            data: report,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
});
function calculatePersonalizedRisk(interaction, userProfile) {
    let riskLevel = interaction.safetyRating;
    if (userProfile.medicalConditions && userProfile.medicalConditions.length > 0) {
        riskLevel -= 1;
    }
    if (userProfile.profileData.age && userProfile.profileData.age > 65) {
        riskLevel -= 0.5;
    }
    if (riskLevel >= 8)
        return 'low';
    if (riskLevel >= 6)
        return 'moderate';
    return 'high';
}
function generateInteractionRecommendations(interaction) {
    const recommendations = [];
    if (interaction.type === 'SYNERGIZES_WITH') {
        recommendations.push('Consider taking these supplements together for enhanced benefits');
        recommendations.push('Monitor for increased effects and adjust dosing if needed');
    }
    else if (interaction.type === 'ANTAGONIZES_WITH') {
        recommendations.push('Separate dosing by at least 2-4 hours');
        recommendations.push('Consider alternative supplements if interaction is significant');
    }
    if (interaction.evidenceLevel < 3) {
        recommendations.push('Limited evidence - monitor closely and consult healthcare provider');
    }
    return recommendations;
}
function calculateOverallInteractionScore(interactions) {
    if (interactions.length === 0)
        return 10;
    const avgSafety = interactions.reduce((sum, i) => sum + i.safetyRating, 0) / interactions.length;
    const avgEvidence = interactions.reduce((sum, i) => sum + i.evidenceLevel, 0) / interactions.length;
    return Math.round((avgSafety + avgEvidence) / 2 * 10) / 10;
}
function generateInteractionSummary(interactions) {
    const synergistic = interactions.filter(i => i.type === 'SYNERGIZES_WITH').length;
    const antagonistic = interactions.filter(i => i.type === 'ANTAGONIZES_WITH').length;
    const neutral = interactions.length - synergistic - antagonistic;
    return `Found ${interactions.length} interactions: ${synergistic} synergistic, ${antagonistic} antagonistic, ${neutral} neutral`;
}
async function generateComprehensiveReport(supplements, userProfile, timeframe, includeInteractions, includePersonalized) {
    const report = {
        summary: {
            supplementCount: supplements.length,
            timeframe,
            generatedAt: new Date().toISOString(),
        },
        supplements: [],
        interactions: [],
        insights: [],
        recommendations: [],
    };
    for (const supplement of supplements) {
        const papers = await researchAIService.searchResearchPapers({
            supplement,
            minEvidenceLevel: 3,
            maxResults: 10,
        });
        report.supplements.push({
            name: supplement,
            researchCount: papers.length,
            avgQuality: papers.reduce((sum, p) => sum + p.qualityScore, 0) / papers.length,
            keyFindings: papers.slice(0, 3).map(p => p.findings.primary),
        });
    }
    if (includeInteractions) {
        const interactions = await researchAIService.analyzeSupplementInteractions(supplements);
        report.interactions = interactions;
    }
    if (includePersonalized) {
        const allPapers = await Promise.all(supplements.map(supp => researchAIService.searchResearchPapers({ supplement: supp, maxResults: 5 })));
        const insights = await researchAIService.generateAIInsights(allPapers.flat(), userProfile);
        report.insights = insights;
    }
    return report;
}
exports.default = router;
//# sourceMappingURL=researchAI.js.map