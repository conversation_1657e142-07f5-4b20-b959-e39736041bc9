"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiProcessingBusinessRules = exports.graphBusinessRules = exports.crawlBusinessRules = exports.validateDateRange = exports.validatePagination = exports.validateTextInput = exports.validateConfidenceThreshold = exports.validateRelationshipTypes = exports.validateNodeTypes = exports.validateCrawlOptions = exports.validateUrl = exports.validateBusinessRules = exports.validateRequest = void 0;
const express_validator_1 = require("express-validator");
const logger_1 = require("../utils/logger");
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const validationErrors = errors.array().map((error) => ({
            field: String('field' in error ? error.field : 'unknown'),
            message: error.msg,
            value: 'value' in error ? error.value : undefined
        }));
        const errorResponse = {
            success: false,
            error: {
                message: 'Validation failed',
                statusCode: 400,
                timestamp: new Date().toISOString(),
                path: req.path,
                method: req.method,
                details: validationErrors
            }
        };
        logger_1.logger.warn('Validation failed', {
            path: req.path,
            method: req.method,
            errors: validationErrors,
            body: req.body,
            query: req.query,
            params: req.params
        });
        res.status(400).json(errorResponse);
        return;
    }
    next();
};
exports.validateRequest = validateRequest;
const validateBusinessRules = (rules) => {
    return (req, res, next) => {
        const errors = [];
        for (const rule of rules) {
            const error = rule(req);
            if (error) {
                errors.push(error);
            }
        }
        if (errors.length > 0) {
            const errorResponse = {
                success: false,
                error: {
                    message: 'Business rule validation failed',
                    statusCode: 422,
                    timestamp: new Date().toISOString(),
                    path: req.path,
                    method: req.method,
                    details: errors.map(error => ({
                        field: 'business_rule',
                        message: error
                    }))
                }
            };
            logger_1.logger.warn('Business rule validation failed', {
                path: req.path,
                method: req.method,
                errors,
                body: req.body
            });
            res.status(422).json(errorResponse);
            return;
        }
        next();
    };
};
exports.validateBusinessRules = validateBusinessRules;
const validateUrl = (url) => {
    try {
        const urlObj = new URL(url);
        return ['http:', 'https:'].includes(urlObj.protocol);
    }
    catch {
        return false;
    }
};
exports.validateUrl = validateUrl;
const validateCrawlOptions = (options) => {
    if (!options)
        return null;
    if (options.maxDepth && (options.maxDepth < 1 || options.maxDepth > 5)) {
        return 'maxDepth must be between 1 and 5';
    }
    if (options.maxPages && (options.maxPages < 1 || options.maxPages > 1000)) {
        return 'maxPages must be between 1 and 1000';
    }
    if (options.chunkSize && (options.chunkSize < 100 || options.chunkSize > 10000)) {
        return 'chunkSize must be between 100 and 10000';
    }
    return null;
};
exports.validateCrawlOptions = validateCrawlOptions;
const validateNodeTypes = (nodeTypes) => {
    if (!Array.isArray(nodeTypes)) {
        return 'nodeTypes must be an array';
    }
    const validTypes = [
        'SUPPLEMENT',
        'INGREDIENT',
        'CONDITION',
        'MECHANISM',
        'DOSAGE',
        'STUDY',
        'EFFECT',
        'POPULATION'
    ];
    const invalidTypes = nodeTypes.filter(type => !validTypes.includes(type));
    if (invalidTypes.length > 0) {
        return `Invalid node types: ${invalidTypes.join(', ')}. Valid types are: ${validTypes.join(', ')}`;
    }
    return null;
};
exports.validateNodeTypes = validateNodeTypes;
const validateRelationshipTypes = (relationshipTypes) => {
    if (!Array.isArray(relationshipTypes)) {
        return 'relationshipTypes must be an array';
    }
    const validTypes = [
        'TREATS',
        'PREVENTS',
        'ENHANCES',
        'INHIBITS',
        'SYNERGISTIC_WITH',
        'CONTRAINDICATED_WITH',
        'ACTS_VIA',
        'AFFECTS_ABSORPTION',
        'STUDIED_IN',
        'SUPPORTS',
        'CONTRADICTS'
    ];
    const invalidTypes = relationshipTypes.filter(type => !validTypes.includes(type));
    if (invalidTypes.length > 0) {
        return `Invalid relationship types: ${invalidTypes.join(', ')}. Valid types are: ${validTypes.join(', ')}`;
    }
    return null;
};
exports.validateRelationshipTypes = validateRelationshipTypes;
const validateConfidenceThreshold = (threshold) => {
    if (typeof threshold !== 'number') {
        return 'Confidence threshold must be a number';
    }
    if (threshold < 0 || threshold > 1) {
        return 'Confidence threshold must be between 0 and 1';
    }
    return null;
};
exports.validateConfidenceThreshold = validateConfidenceThreshold;
const validateTextInput = (text) => {
    if (typeof text !== 'string') {
        return 'Text input must be a string';
    }
    if (text.trim().length === 0) {
        return 'Text input cannot be empty';
    }
    if (text.length > 50000) {
        return 'Text input is too long (maximum 50,000 characters)';
    }
    return null;
};
exports.validateTextInput = validateTextInput;
const validatePagination = (page, limit) => {
    if (page !== undefined) {
        if (!Number.isInteger(page) || page < 1) {
            return 'Page must be a positive integer';
        }
    }
    if (limit !== undefined) {
        if (!Number.isInteger(limit) || limit < 1 || limit > 1000) {
            return 'Limit must be a positive integer between 1 and 1000';
        }
    }
    return null;
};
exports.validatePagination = validatePagination;
const validateDateRange = (startDate, endDate) => {
    if (startDate && !Date.parse(startDate)) {
        return 'Start date is not a valid date';
    }
    if (endDate && !Date.parse(endDate)) {
        return 'End date is not a valid date';
    }
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start > end) {
            return 'Start date must be before end date';
        }
        const maxRange = 365 * 24 * 60 * 60 * 1000;
        if (end.getTime() - start.getTime() > maxRange) {
            return 'Date range cannot exceed 1 year';
        }
    }
    return null;
};
exports.validateDateRange = validateDateRange;
exports.crawlBusinessRules = [
    (req) => {
        const { url } = req.body;
        if (!(0, exports.validateUrl)(url)) {
            return 'Invalid or inaccessible URL';
        }
        return null;
    },
    (req) => {
        const { options } = req.body;
        return (0, exports.validateCrawlOptions)(options);
    }
];
exports.graphBusinessRules = [
    (req) => {
        const { nodeTypes } = req.body;
        if (nodeTypes) {
            return (0, exports.validateNodeTypes)(nodeTypes);
        }
        return null;
    },
    (req) => {
        const { relationshipTypes } = req.body;
        if (relationshipTypes) {
            return (0, exports.validateRelationshipTypes)(relationshipTypes);
        }
        return null;
    },
    (req) => {
        const { confidenceThreshold } = req.body;
        if (confidenceThreshold !== undefined) {
            return (0, exports.validateConfidenceThreshold)(confidenceThreshold);
        }
        return null;
    }
];
exports.aiProcessingBusinessRules = [
    (req) => {
        const { text } = req.body;
        return (0, exports.validateTextInput)(text);
    }
];
//# sourceMappingURL=validation.js.map