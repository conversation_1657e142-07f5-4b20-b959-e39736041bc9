import { Request, Response, NextFunction } from 'express';
export interface PerformanceRequest extends Request {
    performanceMetricId?: string;
    startTime?: number;
}
export declare const performanceMiddleware: (req: PerformanceRequest, res: Response, next: NextFunction) => void;
export declare const databasePerformanceWrapper: <T extends any[], R>(operation: (...args: T) => Promise<R>, operationName: string, database?: string) => (...args: T) => Promise<R>;
export declare const cachePerformanceWrapper: <T extends any[], R>(operation: (...args: T) => Promise<R>, operationName: string) => (...args: T) => Promise<R>;
export declare const aiPerformanceWrapper: <T extends any[], R>(operation: (...args: T) => Promise<R>, operationName: string, model?: string) => (...args: T) => Promise<R>;
export declare const externalApiPerformanceWrapper: <T extends any[], R>(operation: (...args: T) => Promise<R>, serviceName: string, endpoint: string) => (...args: T) => Promise<R>;
export declare function trackPerformance(type: 'database' | 'cache' | 'ai' | 'external', name: string, additionalInfo?: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare const rateLimitWithPerformance: (windowMs: number, max: number) => (req: Request, res: Response, next: NextFunction) => void;
export declare const compressionWithPerformance: () => (req: Request, res: Response, next: NextFunction) => void;
export declare const memoryMonitoringMiddleware: (req: Request, res: Response, next: NextFunction) => void;
export declare const errorTrackingMiddleware: (error: Error, req: PerformanceRequest, res: Response, next: NextFunction) => void;
export declare const healthCheckMiddleware: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=performanceMiddleware.d.ts.map