"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheckMiddleware = exports.errorTrackingMiddleware = exports.memoryMonitoringMiddleware = exports.compressionWithPerformance = exports.rateLimitWithPerformance = exports.externalApiPerformanceWrapper = exports.aiPerformanceWrapper = exports.cachePerformanceWrapper = exports.databasePerformanceWrapper = exports.performanceMiddleware = void 0;
exports.trackPerformance = trackPerformance;
const PerformanceMonitoringService_1 = require("../services/PerformanceMonitoringService");
const logger_1 = require("../utils/logger");
const performanceMiddleware = (req, res, next) => {
    const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest(req.path, req.method);
    req.performanceMetricId = metricId;
    req.startTime = Date.now();
    const originalSend = res.send;
    res.send = function (data) {
        if (req.performanceMetricId) {
            const success = res.statusCode < 400;
            const error = success ? undefined : `HTTP ${res.statusCode}`;
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(req.performanceMetricId, success, error, {
                statusCode: res.statusCode,
                responseSize: data ? data.length : 0,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            });
        }
        return originalSend.call(this, data);
    };
    next();
};
exports.performanceMiddleware = performanceMiddleware;
const databasePerformanceWrapper = (operation, operationName, database = 'unknown') => {
    return async (...args) => {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery(operationName, database);
        try {
            const result = await operation(...args);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                operationName,
                database,
                argsCount: args.length
            });
            return result;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
                operationName,
                database,
                argsCount: args.length
            });
            throw error;
        }
    };
};
exports.databasePerformanceWrapper = databasePerformanceWrapper;
const cachePerformanceWrapper = (operation, operationName) => {
    return async (...args) => {
        const key = args.length > 0 ? String(args[0]) : 'unknown';
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackCacheOperation(operationName, key);
        try {
            const result = await operation(...args);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                operationName,
                key,
                resultSize: result ? JSON.stringify(result).length : 0
            });
            return result;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
                operationName,
                key
            });
            throw error;
        }
    };
};
exports.cachePerformanceWrapper = cachePerformanceWrapper;
const aiPerformanceWrapper = (operation, operationName, model) => {
    return async (...args) => {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation(operationName, model);
        try {
            const result = await operation(...args);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                operationName,
                model,
                argsCount: args.length,
                resultSize: result ? JSON.stringify(result).length : 0
            });
            return result;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
                operationName,
                model
            });
            throw error;
        }
    };
};
exports.aiPerformanceWrapper = aiPerformanceWrapper;
const externalApiPerformanceWrapper = (operation, serviceName, endpoint) => {
    return async (...args) => {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackExternalAPI(serviceName, endpoint);
        try {
            const result = await operation(...args);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                serviceName,
                endpoint,
                argsCount: args.length,
                resultSize: result ? JSON.stringify(result).length : 0
            });
            return result;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
                serviceName,
                endpoint
            });
            throw error;
        }
    };
};
exports.externalApiPerformanceWrapper = externalApiPerformanceWrapper;
function trackPerformance(type, name, additionalInfo) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            let metricId;
            switch (type) {
                case 'database':
                    metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery(name, additionalInfo || 'unknown');
                    break;
                case 'cache':
                    metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackCacheOperation(name, args[0] || 'unknown');
                    break;
                case 'ai':
                    metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation(name, additionalInfo);
                    break;
                case 'external':
                    metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackExternalAPI(name, additionalInfo || 'unknown');
                    break;
                default:
                    throw new Error(`Unknown performance tracking type: ${type}`);
            }
            try {
                const result = await originalMethod.apply(this, args);
                PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                    method: propertyKey,
                    type,
                    name,
                    additionalInfo,
                    argsCount: args.length
                });
                return result;
            }
            catch (error) {
                PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Unknown error', {
                    method: propertyKey,
                    type,
                    name,
                    additionalInfo,
                    argsCount: args.length
                });
                throw error;
            }
        };
        return descriptor;
    };
}
const rateLimitWithPerformance = (windowMs, max) => {
    const requests = new Map();
    return (req, res, next) => {
        const key = req.ip || 'unknown';
        const now = Date.now();
        const windowStart = now - windowMs;
        if (requests.has(key)) {
            const userRequests = requests.get(key).filter(time => time > windowStart);
            requests.set(key, userRequests);
        }
        else {
            requests.set(key, []);
        }
        const userRequests = requests.get(key);
        if (userRequests.length >= max) {
            const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest(req.path, req.method);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, 'Rate limit exceeded', {
                statusCode: 429,
                ip: req.ip,
                requestCount: userRequests.length
            });
            res.status(429).json({
                error: 'Too many requests',
                retryAfter: Math.ceil(windowMs / 1000)
            });
            return;
        }
        userRequests.push(now);
        requests.set(key, userRequests);
        next();
    };
};
exports.rateLimitWithPerformance = rateLimitWithPerformance;
const compressionWithPerformance = () => {
    return (req, res, next) => {
        const originalSend = res.send;
        res.send = function (data) {
            const originalSize = data ? data.length : 0;
            const compressionRatio = req.get('Accept-Encoding')?.includes('gzip') ? 0.7 : 1.0;
            const compressedSize = Math.floor(originalSize * compressionRatio);
            if (originalSize > 1024) {
                logger_1.logger.debug('Response compression', {
                    path: req.path,
                    originalSize,
                    compressedSize,
                    compressionRatio: (1 - compressionRatio) * 100
                });
            }
            return originalSend.call(this, data);
        };
        next();
    };
};
exports.compressionWithPerformance = compressionWithPerformance;
const memoryMonitoringMiddleware = (req, res, next) => {
    const memBefore = process.memoryUsage();
    res.on('finish', () => {
        const memAfter = process.memoryUsage();
        const memDiff = {
            rss: memAfter.rss - memBefore.rss,
            heapUsed: memAfter.heapUsed - memBefore.heapUsed,
            heapTotal: memAfter.heapTotal - memBefore.heapTotal,
            external: memAfter.external - memBefore.external
        };
        if (Math.abs(memDiff.heapUsed) > 10 * 1024 * 1024) {
            logger_1.logger.warn('Significant memory change detected', {
                path: req.path,
                method: req.method,
                memoryDiff: memDiff,
                statusCode: res.statusCode
            });
        }
    });
    next();
};
exports.memoryMonitoringMiddleware = memoryMonitoringMiddleware;
const errorTrackingMiddleware = (error, req, res, next) => {
    if (req.performanceMetricId) {
        PerformanceMonitoringService_1.performanceMonitoringService.endMetric(req.performanceMetricId, false, error.message, {
            errorName: error.name,
            errorStack: error.stack?.substring(0, 500),
            statusCode: res.statusCode || 500
        });
    }
    logger_1.logger.error('Request error', {
        path: req.path,
        method: req.method,
        error: error.message,
        stack: error.stack,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    if (!res.headersSent) {
        res.status(500).json({
            error: 'Internal server error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
        });
    }
    next(error);
};
exports.errorTrackingMiddleware = errorTrackingMiddleware;
const healthCheckMiddleware = async (req, res, next) => {
    if (req.path === '/health' || req.path === '/api/health') {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('health-check', 'GET');
        try {
            const systemMetrics = await PerformanceMonitoringService_1.performanceMonitoringService.getCurrentSystemMetrics();
            const performanceStats = PerformanceMonitoringService_1.performanceMonitoringService.getPerformanceStats(300000);
            const alerts = PerformanceMonitoringService_1.performanceMonitoringService.getAlerts(false);
            const healthStatus = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV,
                version: process.env.npm_package_version || '1.0.0',
                system: {
                    memory: {
                        usage: `${(systemMetrics.memory.percentage).toFixed(1)}%`,
                        used: `${(systemMetrics.memory.used / 1024 / 1024 / 1024).toFixed(2)}GB`,
                        total: `${(systemMetrics.memory.total / 1024 / 1024 / 1024).toFixed(2)}GB`
                    },
                    cpu: {
                        usage: `${systemMetrics.cpu.usage.toFixed(1)}%`,
                        loadAverage: systemMetrics.cpu.loadAverage.map(load => load.toFixed(2))
                    },
                    cache: {
                        hitRate: `${systemMetrics.cache.hitRate.toFixed(1)}%`,
                        keys: systemMetrics.cache.totalKeys
                    }
                },
                performance: {
                    requestsPerMinute: systemMetrics.api.requestsPerMinute,
                    averageResponseTime: `${performanceStats.averageResponseTime.toFixed(2)}ms`,
                    successRate: `${performanceStats.successRate.toFixed(1)}%`,
                    errorRate: `${performanceStats.errorRate.toFixed(1)}%`
                },
                alerts: alerts.length
            };
            if (alerts.some(a => a.type === 'critical')) {
                healthStatus.status = 'critical';
            }
            else if (alerts.length > 0 || systemMetrics.memory.percentage > 90 || systemMetrics.cpu.usage > 90) {
                healthStatus.status = 'warning';
            }
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true, undefined, {
                healthStatus: healthStatus.status,
                alertCount: alerts.length
            });
            res.status(healthStatus.status === 'critical' ? 503 : 200).json(healthStatus);
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Health check failed');
            res.status(503).json({
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: 'Health check failed'
            });
        }
    }
    else {
        next();
    }
};
exports.healthCheckMiddleware = healthCheckMiddleware;
//# sourceMappingURL=performanceMiddleware.js.map