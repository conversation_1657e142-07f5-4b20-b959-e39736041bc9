import { Request, Response, NextFunction } from 'express';
export interface ValidationErrorResponse {
    success: false;
    error: {
        message: string;
        statusCode: number;
        timestamp: string;
        path: string;
        method: string;
        details: {
            field: string;
            message: string;
            value?: any;
        }[];
    };
}
export declare const validateRequest: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateBusinessRules: (rules: ((req: Request) => string | null)[]) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateUrl: (url: string) => boolean;
export declare const validateCrawlOptions: (options: any) => string | null;
export declare const validateNodeTypes: (nodeTypes: string[]) => string | null;
export declare const validateRelationshipTypes: (relationshipTypes: string[]) => string | null;
export declare const validateConfidenceThreshold: (threshold: number) => string | null;
export declare const validateTextInput: (text: string) => string | null;
export declare const validatePagination: (page?: number, limit?: number) => string | null;
export declare const validateDateRange: (startDate?: string, endDate?: string) => string | null;
export declare const crawlBusinessRules: ((req: Request) => string)[];
export declare const graphBusinessRules: ((req: Request) => string)[];
export declare const aiProcessingBusinessRules: ((req: Request) => string)[];
//# sourceMappingURL=validation.d.ts.map