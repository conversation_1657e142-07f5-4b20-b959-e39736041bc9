interface Gemma3Entity {
    id: string;
    type: 'SUPPLEMENT' | 'INGREDIENT' | 'CONDITION' | 'MECHANISM' | 'DOSAGE' | 'STUDY' | 'EFFECT' | 'POPULATION';
    name: string;
    properties: {
        description: string;
        confidence: number;
        context: string;
        aliases?: string[];
        category?: string;
    };
}
interface Gemma3Relationship {
    id: string;
    type: string;
    source_entity: string;
    target_entity: string;
    properties: {
        strength: 'weak' | 'moderate' | 'strong';
        evidence_level: 'anecdotal' | 'observational' | 'clinical_trial' | 'meta_analysis';
        confidence: number;
        mechanism?: string;
        dosage_dependent?: boolean;
        population_specific?: string;
        temporal_aspect?: 'acute' | 'chronic' | 'long_term';
        context: string;
    };
}
interface SafetyProfile {
    supplement_id: string;
    overall_risk_level: 'low' | 'moderate' | 'high';
    contraindications: Array<{
        condition: string;
        severity: 'absolute' | 'relative';
        reason: string;
        evidence_level: string;
    }>;
    interactions: Array<{
        interacting_substance: string;
        interaction_type: string;
        severity: 'minor' | 'moderate' | 'major';
        mechanism: string;
        management: string;
    }>;
    adverse_effects: Array<{
        effect: string;
        frequency: 'rare' | 'uncommon' | 'common';
        severity: 'mild' | 'moderate' | 'severe';
        dosage_related: boolean;
        reversible: boolean;
    }>;
}
interface ProcessingResult {
    entities: Gemma3Entity[];
    relationships: Gemma3Relationship[];
    safety_profile: SafetyProfile;
    confidence_score: number;
    processing_time: number;
    source_text: string;
}
export declare class Gemma3Neo4jProcessor {
    private gemmaEndpoint;
    private maxRetries;
    private retryDelay;
    constructor(gemmaEndpoint?: string);
    processTextToGraph(text: string, sourceId?: string): Promise<any>;
    processWithGemma3(text: string): Promise<ProcessingResult>;
    private extractEntities;
    private extractRelationships;
    private analyzeSafety;
    private callGemma3;
    private createNeo4jGraph;
    private createEntities;
    private createRelationships;
    private createSafetyProfiles;
    private createSourceDocument;
    private parseEntitiesResponse;
    private parseRelationshipsResponse;
    private parseSafetyResponse;
    private fallbackEntityParsing;
    private getDefaultSafetyProfile;
    private calculateOverallConfidence;
}
export {};
//# sourceMappingURL=Gemma3Neo4jProcessor.d.ts.map