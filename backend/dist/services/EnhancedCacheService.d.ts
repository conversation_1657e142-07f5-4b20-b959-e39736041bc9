export interface CacheOptions {
    ttl?: number;
    compress?: boolean;
    tags?: string[];
    priority?: 'low' | 'medium' | 'high';
}
export interface CacheStats {
    hits: number;
    misses: number;
    hitRate: number;
    totalKeys: number;
    memoryUsage: number;
    lastUpdated: Date;
}
export interface CacheMetrics {
    operationType: 'get' | 'set' | 'delete' | 'invalidate';
    key: string;
    hit: boolean;
    executionTime: number;
    dataSize?: number;
    timestamp: Date;
}
export declare class EnhancedCacheService {
    private redisClient;
    private memoryCache;
    private cacheStats;
    private metrics;
    private maxMemorySize;
    private compressionThreshold;
    constructor();
    private initializeRedisClient;
    get<T>(key: string, options?: CacheOptions): Promise<T | null>;
    set<T>(key: string, data: T, options?: CacheOptions): Promise<void>;
    delete(key: string): Promise<void>;
    invalidateByTags(tags: string[]): Promise<void>;
    getCacheStats(): CacheStats;
    getCacheMetrics(limit?: number): CacheMetrics[];
    warmCache(warmupData: Array<{
        key: string;
        data: any;
        options?: CacheOptions;
    }>): Promise<void>;
    clear(): Promise<void>;
    private getFromMemory;
    private getFromRedis;
    private setInMemory;
    private setInRedis;
    private shouldCacheInMemory;
    private calculateDataSize;
    private getMemoryUsage;
    private evictLowPriorityItems;
    private addCacheTags;
    private getKeysByTag;
    private updateCacheStats;
    private recordMetric;
    private startCacheCleanup;
    shutdown(): Promise<void>;
}
export declare const enhancedCacheService: EnhancedCacheService;
//# sourceMappingURL=EnhancedCacheService.d.ts.map