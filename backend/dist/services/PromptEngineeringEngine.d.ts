import { EventEmitter } from 'events';
export interface PromptTemplate {
    id: string;
    name: string;
    category: 'research' | 'analysis' | 'synthesis' | 'validation' | 'creative' | 'technical';
    template: string;
    variables: string[];
    description: string;
    examples: PromptExample[];
    performance: PromptPerformance;
    metadata: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    version: number;
}
export interface PromptExample {
    input: Record<string, string>;
    expectedOutput: string;
    actualOutput?: string;
    quality?: number;
    timestamp?: Date;
}
export interface PromptPerformance {
    usageCount: number;
    averageQuality: number;
    averageConfidence: number;
    averageResponseTime: number;
    successRate: number;
    userRatings: number[];
    lastOptimized: Date;
}
export interface PromptOptimization {
    originalPrompt: string;
    optimizedPrompt: string;
    improvements: string[];
    expectedImprovement: number;
    testResults?: PromptTestResult[];
    confidence: number;
    reasoning: string;
}
export interface PromptTestResult {
    prompt: string;
    input: Record<string, string>;
    output: string;
    quality: number;
    confidence: number;
    responseTime: number;
    modelId: string;
}
export interface PromptContext {
    domain: string;
    taskType: string;
    userLevel: 'beginner' | 'intermediate' | 'expert';
    language: string;
    tone: 'formal' | 'casual' | 'technical' | 'friendly';
    outputFormat: 'text' | 'json' | 'markdown' | 'html';
    constraints: string[];
    requirements: string[];
}
export declare class PromptEngineeringEngine extends EventEmitter {
    private templates;
    private optimizationHistory;
    private abTests;
    private feedbackData;
    constructor();
    private initializeBaseTemplates;
    generateOptimizedPrompt(basePrompt: string, context: PromptContext, variables?: Record<string, string>): Promise<string>;
    private findBestTemplate;
    private optimizePrompt;
    private applyToneOptimization;
    private applyFormatOptimization;
    private applyConstraintOptimization;
    private applyRequirementOptimization;
    private applyUserLevelOptimization;
    private applyBestPractices;
    private interpolateVariables;
    testPromptPerformance(promptA: string, promptB: string, testInputs: Record<string, string>[], modelId: string): Promise<{
        winner: 'A' | 'B' | 'tie';
        results: any;
    }>;
    private executePromptTest;
    recordFeedback(promptId: string, rating: number, feedback: string, metadata?: Record<string, any>): void;
    getPromptAnalytics(promptId?: string): any;
    private hashPromptContext;
    private startOptimizationLoop;
    private analyzeAndOptimizeTemplates;
    getTemplates(): PromptTemplate[];
    getTemplate(id: string): PromptTemplate | null;
    createTemplate(template: Omit<PromptTemplate, 'performance' | 'createdAt' | 'updatedAt' | 'version'>): Promise<string>;
}
export default PromptEngineeringEngine;
//# sourceMappingURL=PromptEngineeringEngine.d.ts.map