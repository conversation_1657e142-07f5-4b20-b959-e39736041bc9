import { EventEmitter } from 'events';
export interface ResearchStrategy {
    id: string;
    name: string;
    description: string;
    type: 'comprehensive' | 'focused' | 'exploratory' | 'validation' | 'synthesis';
    complexity: 'simple' | 'moderate' | 'complex' | 'expert';
    steps: ResearchStep[];
    estimatedDuration: number;
    requiredResources: string[];
    successCriteria: string[];
    adaptationRules: AdaptationRule[];
    performance: StrategyPerformance;
    metadata: Record<string, any>;
}
export interface ResearchStep {
    id: string;
    name: string;
    type: 'search' | 'analysis' | 'synthesis' | 'validation' | 'enhancement';
    description: string;
    inputs: string[];
    outputs: string[];
    dependencies: string[];
    estimatedTime: number;
    priority: 'low' | 'medium' | 'high' | 'critical';
    adaptable: boolean;
    fallbackOptions: string[];
}
export interface AdaptationRule {
    condition: string;
    action: 'skip_step' | 'add_step' | 'modify_step' | 'change_strategy' | 'increase_depth' | 'reduce_scope';
    parameters: Record<string, any>;
    priority: number;
}
export interface StrategyPerformance {
    usageCount: number;
    successRate: number;
    averageQuality: number;
    averageTime: number;
    userSatisfaction: number;
    adaptationFrequency: number;
    lastUsed: Date;
    lastOptimized: Date;
}
export interface ResearchContext {
    query: string;
    domain: string;
    complexity: 'simple' | 'moderate' | 'complex' | 'expert';
    timeConstraint?: number;
    qualityRequirement: 'basic' | 'standard' | 'high' | 'expert';
    resourceConstraints: string[];
    userExpertise: 'beginner' | 'intermediate' | 'expert';
    previousResults?: any[];
    sessionHistory?: any[];
}
export interface AdaptationDecision {
    originalStrategy: string;
    adaptedStrategy: string;
    changes: StrategyChange[];
    reasoning: string;
    confidence: number;
    expectedImprovement: number;
}
export interface StrategyChange {
    type: 'step_added' | 'step_removed' | 'step_modified' | 'order_changed' | 'parameters_adjusted';
    stepId?: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
}
export declare class AdaptiveResearchManager extends EventEmitter {
    private strategies;
    private adaptationHistory;
    private performanceMetrics;
    private learningData;
    constructor();
    private initializeBaseStrategies;
    selectOptimalStrategy(context: ResearchContext): Promise<ResearchStrategy>;
    adaptStrategy(strategy: ResearchStrategy, context: ResearchContext, currentConditions: Record<string, any>): Promise<AdaptationDecision>;
    recordStrategyPerformance(strategyId: string, performance: {
        success: boolean;
        quality: number;
        actualTime: number;
        userSatisfaction: number;
        adaptationsUsed: number;
    }): void;
    private hashContext;
    private isComplexityCompatible;
    private evaluateCondition;
    private applyAdaptationRule;
    private reduceStrategyScope;
    private enhanceValidation;
    private calculateAdaptationConfidence;
    private estimateImprovement;
    private startAdaptationLearning;
    private analyzeAdaptationPatterns;
    getStrategies(): ResearchStrategy[];
    getStrategy(id: string): ResearchStrategy | null;
    getStrategyPerformance(): Record<string, any>;
    getAdaptationHistory(strategyId?: string): AdaptationDecision[];
}
export default AdaptiveResearchManager;
//# sourceMappingURL=AdaptiveResearchManager.d.ts.map