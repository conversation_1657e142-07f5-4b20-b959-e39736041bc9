{"version": 3, "file": "DataMigrationService.js", "sourceRoot": "", "sources": ["../../src/services/DataMigrationService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0CAA4E;AAC5E,2CAAwE;AACxE,4DAA0D;AAC1D,+BAAoC;AACpC,uCAAyB;AAEzB,4DAA6B;AAsC7B,MAAa,oBAAoB;IAK/B,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,MAAsB;QACpE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2CAA2C,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC;YAEjF,IAAI,cAAgC,CAAC;YAErC,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,gBAAgB,GAAG;gBACvB,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,MAAM,EAAE,EAAc;aACvB,CAAC;YAGF,MAAM,SAAS,GAAG,GAAG,CAAC;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAC1D,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAErD,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;oBAC7C,gBAAgB,CAAC,oBAAoB,IAAI,KAAK,CAAC,MAAM,CAAC;gBACxD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gBAAgB,CAAC,gBAAgB,IAAI,KAAK,CAAC,MAAM,CAAC;oBAClD,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC;oBACzE,IAAA,iBAAQ,EAAC,wBAAwB,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAA,6BAAoB,EAAC,2BAA2B,EAAE,aAAa,EAAE,QAAQ,EAAE,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAE5I,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,mCAAmC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7E,MAAM,IAAI,4BAAa,CAAC,mCAAmC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;YAEhE,MAAM,SAAS,GAAgB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,gBAAgB,GAAG;gBACvB,YAAY,EAAE,SAAS,CAAC,MAAM;gBAC9B,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,MAAM,EAAE,EAAc;aACvB,CAAC;YAGF,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAEhD,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACxC,gBAAgB,CAAC,oBAAoB,IAAI,KAAK,CAAC,MAAM,CAAC;gBACxD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gBAAgB,CAAC,gBAAgB,IAAI,KAAK,CAAC,MAAM,CAAC;oBAClD,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAA,6BAAoB,EAAC,sBAAsB,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAEnI,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,8BAA8B,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;YAChE,MAAM,IAAI,4BAAa,CAAC,8BAA8B,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,gBAAuB;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAEvD,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACvD,KAAK,EAAE;;;0BAGW,GAAG,CAAC,IAAI;;SAEzB;gBACD,UAAU,EAAE;oBACV,UAAU,EAAE,GAAG,CAAC,MAAM;oBACtB,UAAU,EAAE,GAAG,CAAC,MAAM;oBACtB,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,UAAU,EAAE;wBACV,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,QAAQ;wBAClC,aAAa,EAAE,GAAG,CAAC,aAAa,IAAI,UAAU;wBAC9C,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,GAAG;wBACjC,MAAM,EAAE,gBAAgB;wBACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;aACF,CAAC,CAAC,CAAC;YAGJ,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,uBAAuB,GAAG,CAAC,CAAC;YAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAC/D,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAE1D,IAAI,CAAC;oBACH,MAAM,IAAA,+BAAuB,EAAC,KAAK,CAAC,CAAC;oBACrC,uBAAuB,IAAI,KAAK,CAAC,MAAM,CAAC;gBAC1C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAA,iBAAQ,EAAC,qCAAqC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAA,6BAAoB,EAAC,uBAAuB,EAAE,eAAe,EAAE,QAAQ,EAAE,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAE/H,OAAO;gBACL,kBAAkB,EAAE,gBAAgB,CAAC,MAAM;gBAC3C,uBAAuB;gBACvB,mBAAmB,EAAE,gBAAgB,CAAC,MAAM,GAAG,uBAAuB;aACvE,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,4BAAa,CAAC,gCAAgC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAEzC,MAAM,iBAAiB,GAAG;gBAExB,6EAA6E;gBAG7E,kJAAkJ;gBAGlJ,4FAA4F;gBAG5F,uIAAuI;aACxI,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC,CACzD,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;oBACjD,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC3B,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE;iBACjC,CAAC,CAAC;gBACH,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;oBACxD,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnB,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE;iBACnD,CAAC,CAAC;gBACH,wBAAwB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;oBAC5D,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC;oBAC/B,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE;iBACjC,CAAC,CAAC;gBACH,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE;oBAC1E,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE;oBACxE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE;iBAC/D,CAAC,CAAC,CAAC,IAAI;aACT,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAA,6BAAoB,EAAC,2BAA2B,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE1E,OAAO,UAAU,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,4BAAa,CAAC,mCAAmC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAU,EAAE,CAAC;YAE1B,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC1B,IAAI,CAAC,IAAA,oBAAG,GAAE,CAAC;iBACX,EAAE,CAAC,MAAM,EAAE,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC7C,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBACjC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,QAAgB;QACvD,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE;oBACO,QAAQ;;OAErB;YACD,UAAU,EAAE;gBACV,UAAU,EAAE;oBACV,GAAG,IAAI;oBACP,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,IAAA,SAAM,GAAE;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,MAAM,EAAE,gBAAgB;iBACzB;aACF;SACF,CAAC,CAAC,CAAC;QAEJ,MAAM,IAAA,+BAAuB,EAAC,OAAO,CAAC,CAAC;IACzC,CAAC;CACF;AA9PD,oDA8PC"}