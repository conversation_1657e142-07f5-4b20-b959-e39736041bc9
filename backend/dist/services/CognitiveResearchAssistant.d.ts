import { EventEmitter } from 'events';
import EnhancedAIService from './EnhancedAIService';
import PromptEngineeringEngine from './PromptEngineeringEngine';
import AdaptiveResearchManager from './AdaptiveResearchManager';
export interface UserIntent {
    type: 'research' | 'analysis' | 'synthesis' | 'validation' | 'exploration' | 'comparison';
    confidence: number;
    entities: string[];
    keywords: string[];
    domain: string;
    complexity: 'beginner' | 'intermediate' | 'expert';
    timeframe?: string;
    scope: 'narrow' | 'broad' | 'comprehensive';
}
export interface ConversationContext {
    userId: string;
    sessionId: string;
    conversationHistory: ConversationTurn[];
    userProfile: UserProfile;
    currentTopic?: string;
    activeResearch?: string[];
    preferences: UserPreferences;
    learningData: LearningData;
}
export interface ConversationTurn {
    id: string;
    timestamp: Date;
    userMessage: string;
    assistantResponse: string;
    intent: UserIntent;
    actions: AssistantAction[];
    feedback?: UserFeedback;
    metadata: Record<string, any>;
}
export interface UserProfile {
    userId: string;
    expertise: Record<string, 'beginner' | 'intermediate' | 'expert'>;
    interests: string[];
    researchHistory: string[];
    preferredComplexity: 'beginner' | 'intermediate' | 'expert';
    communicationStyle: 'concise' | 'detailed' | 'technical' | 'friendly';
    learningGoals: string[];
    lastActive: Date;
}
export interface UserPreferences {
    responseLength: 'short' | 'medium' | 'long';
    includeSourceLinks: boolean;
    explanationLevel: 'basic' | 'intermediate' | 'advanced';
    visualizations: boolean;
    realTimeUpdates: boolean;
    collaborativeMode: boolean;
}
export interface LearningData {
    successfulQueries: string[];
    challengingTopics: string[];
    preferredSources: string[];
    feedbackPatterns: Record<string, number>;
    improvementAreas: string[];
    lastLearningUpdate: Date;
}
export interface AssistantAction {
    type: 'research' | 'analysis' | 'synthesis' | 'recommendation' | 'clarification' | 'learning';
    description: string;
    parameters: Record<string, any>;
    result?: any;
    confidence: number;
    timestamp: Date;
}
export interface UserFeedback {
    rating: number;
    helpful: boolean;
    accurate: boolean;
    complete: boolean;
    comments?: string;
    suggestions?: string[];
    timestamp: Date;
}
export interface ResearchRecommendation {
    type: 'topic_expansion' | 'methodology_improvement' | 'source_suggestion' | 'analysis_deepening';
    title: string;
    description: string;
    reasoning: string;
    confidence: number;
    priority: 'low' | 'medium' | 'high';
    estimatedValue: number;
    actionable: boolean;
}
export declare class CognitiveResearchAssistant extends EventEmitter {
    private aiService;
    private promptEngine;
    private strategyManager;
    private userContexts;
    private intentClassifier;
    private learningEngine;
    constructor(aiService: EnhancedAIService, promptEngine: PromptEngineeringEngine, strategyManager: AdaptiveResearchManager);
    private initializeIntentClassifier;
    processMessage(userId: string, sessionId: string, message: string, context?: Partial<ConversationContext>): Promise<{
        response: string;
        actions: AssistantAction[];
        recommendations: ResearchRecommendation[];
        intent: UserIntent;
    }>;
    private classifyIntent;
    private generateContextualResponse;
    private determineActions;
    private generateRecommendations;
    private updateLearningData;
    private getOrCreateContext;
    private cacheContext;
    private extractEntities;
    private extractKeywords;
    private identifyDomain;
    private assessComplexity;
    private determineScope;
    private getBasePromptForIntent;
    private startLearningLoop;
    private analyzeLearningPatterns;
    recordFeedback(userId: string, sessionId: string, turnId: string, feedback: UserFeedback): Promise<void>;
    getUserProfile(userId: string, sessionId: string): UserProfile | null;
    getConversationHistory(userId: string, sessionId: string, limit?: number): ConversationTurn[];
    getLearningInsights(userId: string, sessionId: string): any;
}
export default CognitiveResearchAssistant;
//# sourceMappingURL=CognitiveResearchAssistant.d.ts.map