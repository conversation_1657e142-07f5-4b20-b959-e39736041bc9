"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const logger_1 = require("../utils/logger");
class UserService {
    db;
    collection;
    constructor(db) {
        this.db = db;
        this.collection = db.collection('users');
    }
    async createUser(userData) {
        try {
            const user = {
                ...userData,
                profile: {
                    ...userData.profile,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                healthProfile: this.initializeHealthProfile(userData.healthProfile),
                analytics: {
                    healthScore: 0,
                    lastCalculated: new Date(),
                    trends: [],
                    riskFactors: [],
                    recommendations: [],
                },
                subscription: {
                    plan: 'free',
                    startDate: new Date(),
                    features: ['basic_recommendations', 'health_tracking'],
                },
            };
            const result = await this.collection.insertOne(user);
            user._id = result.insertedId.toString();
            logger_1.logger.info(`User created successfully: ${user.email}`);
            return user;
        }
        catch (error) {
            logger_1.logger.error('Error creating user:', error);
            throw new Error('Failed to create user');
        }
    }
    initializeHealthProfile(profile) {
        return {
            demographics: {
                age: 0,
                gender: 'other',
                weight: 0,
                height: 0,
                bmi: 0,
                ...profile?.demographics,
            },
            medicalHistory: {
                conditions: [],
                medications: [],
                allergies: [],
                surgeries: [],
                familyHistory: [],
                chronicConditions: [],
                ...profile?.medicalHistory,
            },
            lifestyle: {
                activityLevel: 'moderate',
                exerciseFrequency: 0,
                exerciseTypes: [],
                diet: 'omnivore',
                smoking: false,
                alcohol: 'none',
                sleep: {
                    averageHours: 8,
                    quality: 3,
                },
                stress: {
                    level: 3,
                    sources: [],
                    managementTechniques: [],
                },
                environment: {
                    location: '',
                    pollutionLevel: 'moderate',
                    waterQuality: 'good',
                    sunExposure: 'moderate',
                },
                ...profile?.lifestyle,
            },
            goals: profile?.goals || [],
            preferences: {
                budget: {
                    min: 0,
                    max: 100,
                    currency: 'USD',
                },
                supplementTypes: [],
                avoidIngredients: [],
                preferredBrands: [],
                deliveryPreferences: {
                    frequency: 'monthly',
                    autoReorder: false,
                },
                notifications: {
                    email: true,
                    sms: false,
                    push: true,
                    reminderTimes: ['09:00'],
                },
                privacy: {
                    shareDataForResearch: false,
                    anonymousAnalytics: true,
                },
                ...profile?.preferences,
            },
            labResults: profile?.labResults || [],
            biometricData: profile?.biometricData || [],
            supplementStack: {
                supplements: [],
                lastUpdated: new Date(),
                ...profile?.supplementStack,
            },
        };
    }
    async getUserById(userId) {
        try {
            const user = await this.collection.findOne({ _id: userId });
            return user;
        }
        catch (error) {
            logger_1.logger.error('Error fetching user:', error);
            throw new Error('Failed to fetch user');
        }
    }
    async updateHealthProfile(userId, profileUpdate) {
        try {
            const user = await this.collection.findOne({ _id: userId });
            if (!user) {
                return null;
            }
            const updatedHealthProfile = {
                ...user.healthProfile,
                ...profileUpdate,
                demographics: { ...user.healthProfile.demographics, ...(profileUpdate.demographics || {}) },
                medicalHistory: { ...user.healthProfile.medicalHistory, ...(profileUpdate.medicalHistory || {}) },
                lifestyle: { ...user.healthProfile.lifestyle, ...(profileUpdate.lifestyle || {}) },
                preferences: { ...user.healthProfile.preferences, ...(profileUpdate.preferences || {}) },
                goals: profileUpdate.goals !== undefined ? profileUpdate.goals : user.healthProfile.goals,
                labResults: profileUpdate.labResults !== undefined ? profileUpdate.labResults : user.healthProfile.labResults,
                biometricData: profileUpdate.biometricData !== undefined ? profileUpdate.biometricData : user.healthProfile.biometricData,
                geneticData: profileUpdate.geneticData !== undefined ? profileUpdate.geneticData : user.healthProfile.geneticData,
                supplementStack: profileUpdate.supplementStack !== undefined ? profileUpdate.supplementStack : user.healthProfile.supplementStack,
            };
            const result = await this.collection.findOneAndUpdate({ _id: userId }, {
                $set: {
                    healthProfile: updatedHealthProfile,
                    'profile.updatedAt': new Date(),
                },
            }, { returnDocument: 'after' });
            if (result.value) {
                logger_1.logger.info(`Health profile updated for user: ${userId}`);
                await this.calculateHealthScore(userId);
            }
            return result.value;
        }
        catch (error) {
            logger_1.logger.error('Error updating health profile:', error);
            throw new Error('Failed to update health profile');
        }
    }
    async addHealthGoal(userId, goal) {
        try {
            const result = await this.collection.findOneAndUpdate({ _id: userId }, {
                $push: { 'healthProfile.goals': goal },
                $set: { 'profile.updatedAt': new Date() },
            }, { returnDocument: 'after' });
            if (result.value) {
                logger_1.logger.info(`Health goal added for user: ${userId}`);
            }
            return result.value;
        }
        catch (error) {
            logger_1.logger.error('Error adding health goal:', error);
            throw new Error('Failed to add health goal');
        }
    }
    async addBiometricData(userId, data) {
        try {
            const result = await this.collection.findOneAndUpdate({ _id: userId }, {
                $push: { 'healthProfile.biometricData': data },
                $set: { 'profile.updatedAt': new Date() },
            }, { returnDocument: 'after' });
            if (result.value) {
                logger_1.logger.info(`Biometric data added for user: ${userId}`);
                await this.calculateHealthScore(userId);
            }
            return result.value;
        }
        catch (error) {
            logger_1.logger.error('Error adding biometric data:', error);
            throw new Error('Failed to add biometric data');
        }
    }
    async addLabResults(userId, labResults) {
        try {
            const result = await this.collection.findOneAndUpdate({ _id: userId }, {
                $push: { 'healthProfile.labResults': labResults },
                $set: { 'profile.updatedAt': new Date() },
            }, { returnDocument: 'after' });
            if (result.value) {
                logger_1.logger.info(`Lab results added for user: ${userId}`);
                await this.calculateHealthScore(userId);
            }
            return result.value;
        }
        catch (error) {
            logger_1.logger.error('Error adding lab results:', error);
            throw new Error('Failed to add lab results');
        }
    }
    async calculateHealthScore(userId) {
        try {
            const user = await this.getUserById(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const score = this.computeHealthScore(user.healthProfile);
            await this.collection.updateOne({ _id: userId }, {
                $set: {
                    'analytics.healthScore': score,
                    'analytics.lastCalculated': new Date(),
                },
                $push: {
                    'analytics.trends': {
                        metric: 'healthScore',
                        value: score,
                        date: new Date(),
                    },
                },
            });
            logger_1.logger.info(`Health score calculated for user ${userId}: ${score}`);
            return score;
        }
        catch (error) {
            logger_1.logger.error('Error calculating health score:', error);
            throw new Error('Failed to calculate health score');
        }
    }
    computeHealthScore(profile) {
        let score = 0;
        let maxScore = 0;
        maxScore += 20;
        if (profile.demographics.bmi >= 18.5 && profile.demographics.bmi <= 24.9) {
            score += 10;
        }
        else if (profile.demographics.bmi >= 25 && profile.demographics.bmi <= 29.9) {
            score += 5;
        }
        if (profile.demographics.age >= 18 && profile.demographics.age <= 65) {
            score += 10;
        }
        maxScore += 30;
        if (profile.lifestyle.exerciseFrequency >= 3)
            score += 10;
        if (profile.lifestyle.sleep.averageHours >= 7 && profile.lifestyle.sleep.averageHours <= 9)
            score += 10;
        if (!profile.lifestyle.smoking)
            score += 5;
        if (profile.lifestyle.alcohol === 'none' || profile.lifestyle.alcohol === 'light')
            score += 5;
        maxScore += 25;
        if (profile.medicalHistory.conditions.length === 0)
            score += 15;
        else if (profile.medicalHistory.conditions.length <= 2)
            score += 10;
        if (profile.medicalHistory.medications.length <= 2)
            score += 10;
        maxScore += 25;
        const activeGoals = profile.goals.filter(g => g.status === 'in_progress' || g.status === 'achieved');
        if (activeGoals.length >= 3)
            score += 15;
        else if (activeGoals.length >= 1)
            score += 10;
        if (profile.biometricData.length > 0)
            score += 10;
        return Math.round((score / maxScore) * 100);
    }
}
exports.UserService = UserService;
//# sourceMappingURL=UserService.js.map