"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataMigrationService = void 0;
const neo4j_1 = require("@/config/neo4j");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const uuid_1 = require("uuid");
const fs = __importStar(require("fs"));
const csv_parser_1 = __importDefault(require("csv-parser"));
class DataMigrationService {
    async migrateSupplementData(dataSource, format) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Starting supplement data migration from ${dataSource} (${format})`);
            let supplementData;
            if (format === 'csv') {
                supplementData = await this.parseCsvFile(dataSource);
            }
            else {
                supplementData = await this.parseJsonFile(dataSource);
            }
            const migrationResults = {
                totalRecords: supplementData.length,
                successfulMigrations: 0,
                failedMigrations: 0,
                errors: []
            };
            const batchSize = 100;
            for (let i = 0; i < supplementData.length; i += batchSize) {
                const batch = supplementData.slice(i, i + batchSize);
                try {
                    await this.processBatch(batch, 'Supplement');
                    migrationResults.successfulMigrations += batch.length;
                }
                catch (error) {
                    migrationResults.failedMigrations += batch.length;
                    migrationResults.errors.push(`Batch ${i}-${i + batch.length}: ${error}`);
                    (0, logger_1.logError)('Batch migration failed', error, { batchStart: i, batchSize: batch.length });
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Supplement data migration', 'Supplements', duration, { successfulMigrations: migrationResults.successfulMigrations });
            return migrationResults;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to migrate supplement data', error, { dataSource, format });
            throw new errorHandler_1.DatabaseError('Failed to migrate supplement data');
        }
    }
    async migrateStudyData(dataSource) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Starting study data migration from ${dataSource}`);
            const studyData = await this.parseJsonFile(dataSource);
            const migrationResults = {
                totalRecords: studyData.length,
                successfulMigrations: 0,
                failedMigrations: 0,
                errors: []
            };
            const batchSize = 50;
            for (let i = 0; i < studyData.length; i += batchSize) {
                const batch = studyData.slice(i, i + batchSize);
                try {
                    await this.processBatch(batch, 'Study');
                    migrationResults.successfulMigrations += batch.length;
                }
                catch (error) {
                    migrationResults.failedMigrations += batch.length;
                    migrationResults.errors.push(`Batch ${i}-${i + batch.length}: ${error}`);
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Study data migration', 'Studies', duration, { successfulMigrations: migrationResults.successfulMigrations });
            return migrationResults;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to migrate study data', error, { dataSource });
            throw new errorHandler_1.DatabaseError('Failed to migrate study data');
        }
    }
    async createRelationships(relationshipData) {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Creating relationships between entities');
            const relationshipQueries = relationshipData.map(rel => ({
                query: `
          MATCH (a {name: $sourceName}), (b {name: $targetName})
          WHERE $sourceLabel IN labels(a) AND $targetLabel IN labels(b)
          CREATE (a)-[r:${rel.type} $properties]->(b)
          RETURN r
        `,
                parameters: {
                    sourceName: rel.source,
                    targetName: rel.target,
                    sourceLabel: rel.sourceLabel,
                    targetLabel: rel.targetLabel,
                    properties: {
                        strength: rel.strength || 'medium',
                        evidenceLevel: rel.evidenceLevel || 'moderate',
                        confidence: rel.confidence || 0.7,
                        source: 'DATA_MIGRATION',
                        createdAt: new Date().toISOString()
                    }
                }
            }));
            const batchSize = 50;
            let successfulRelationships = 0;
            for (let i = 0; i < relationshipQueries.length; i += batchSize) {
                const batch = relationshipQueries.slice(i, i + batchSize);
                try {
                    await (0, neo4j_1.executeNeo4jTransaction)(batch);
                    successfulRelationships += batch.length;
                }
                catch (error) {
                    (0, logger_1.logError)('Failed to create relationship batch', error, { batchStart: i });
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Relationship creation', 'Relationships', duration, { successfulRelationships: successfulRelationships });
            return {
                totalRelationships: relationshipData.length,
                successfulRelationships,
                failedRelationships: relationshipData.length - successfulRelationships
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to create relationships', error);
            throw new errorHandler_1.DatabaseError('Failed to create relationships');
        }
    }
    async validateDataIntegrity() {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Validating data integrity');
            const validationQueries = [
                'MATCH (n) WHERE NOT (n)--() RETURN labels(n) as nodeType, count(n) as count',
                'MATCH (s:Supplement) WITH s.name as name, collect(s) as supplements WHERE size(supplements) > 1 RETURN name, size(supplements) as duplicateCount',
                'MATCH ()-[r]->() RETURN type(r) as relationshipType, count(r) as count ORDER BY count DESC',
                'MATCH (s:Supplement) RETURN count(s) as totalSupplements, count(s.description) as withDescription, count(s.dosageRange) as withDosage'
            ];
            const results = await Promise.all(validationQueries.map(query => (0, neo4j_1.executeNeo4jQuery)(query)));
            const validation = {
                orphanedNodes: results[0].records.map((r) => ({
                    nodeType: r.get('nodeType'),
                    count: r.get('count').toNumber()
                })),
                duplicateSupplements: results[1].records.map((r) => ({
                    name: r.get('name'),
                    duplicateCount: r.get('duplicateCount').toNumber()
                })),
                relationshipDistribution: results[2].records.map((r) => ({
                    type: r.get('relationshipType'),
                    count: r.get('count').toNumber()
                })),
                dataCompleteness: results[3].records[0] ? {
                    totalSupplements: results[3].records[0].get('totalSupplements').toNumber(),
                    withDescription: results[3].records[0].get('withDescription').toNumber(),
                    withDosage: results[3].records[0].get('withDosage').toNumber()
                } : null
            };
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Data integrity validation', 'Validation', duration);
            return validation;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to validate data integrity', error);
            throw new errorHandler_1.DatabaseError('Failed to validate data integrity');
        }
    }
    async parseCsvFile(filePath) {
        return new Promise((resolve, reject) => {
            const results = [];
            fs.createReadStream(filePath)
                .pipe((0, csv_parser_1.default)())
                .on('data', (data) => results.push(data))
                .on('end', () => resolve(results))
                .on('error', (error) => reject(error));
        });
    }
    async parseJsonFile(filePath) {
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(fileContent);
        }
        catch (error) {
            throw new Error(`Failed to parse JSON file: ${error}`);
        }
    }
    async processBatch(batch, nodeType) {
        const queries = batch.map(item => ({
            query: `
        CREATE (n:${nodeType} $properties)
        RETURN n
      `,
            parameters: {
                properties: {
                    ...item,
                    id: item.id || (0, uuid_1.v4)(),
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    source: 'DATA_MIGRATION'
                }
            }
        }));
        await (0, neo4j_1.executeNeo4jTransaction)(queries);
    }
}
exports.DataMigrationService = DataMigrationService;
//# sourceMappingURL=DataMigrationService.js.map