import { Server as HttpServer } from 'http';
export interface UserPresence {
    userId: string;
    username: string;
    avatar?: string;
    status: 'online' | 'away' | 'busy' | 'offline';
    lastSeen: Date;
    currentRoom?: string;
    activeResearch?: string;
}
export interface ResearchRoom {
    id: string;
    name: string;
    description?: string;
    type: 'research' | 'collaboration' | 'analysis';
    createdBy: string;
    createdAt: Date;
    participants: Set<string>;
    isPrivate: boolean;
    maxParticipants?: number;
    activeResearch?: string;
    settings: {
        allowComments: boolean;
        allowEditing: boolean;
        autoSave: boolean;
        notifications: boolean;
    };
}
export interface ResearchProgress {
    sessionId: string;
    roomId?: string;
    userId: string;
    type: 'search' | 'analysis' | 'synthesis' | 'validation';
    status: 'started' | 'in_progress' | 'completed' | 'error';
    progress: number;
    currentStep: string;
    totalSteps: number;
    results?: any;
    error?: string;
    timestamp: Date;
}
export interface LiveNotification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error' | 'research_update';
    title: string;
    message: string;
    userId?: string;
    roomId?: string;
    data?: any;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    persistent: boolean;
    timestamp: Date;
    expiresAt?: Date;
}
export interface RoomMessage {
    id: string;
    roomId: string;
    userId: string;
    username: string;
    type: 'text' | 'research_result' | 'system' | 'file' | 'reaction';
    content: string;
    data?: any;
    replyTo?: string;
    reactions: Map<string, string[]>;
    timestamp: Date;
    edited?: Date;
}
export declare class EnhancedWebSocketService {
    private io;
    private connectedUsers;
    private userSockets;
    private socketUsers;
    private researchRooms;
    private roomMessages;
    private activeResearchSessions;
    private notifications;
    constructor(server: HttpServer);
    private initializeEventHandlers;
    private handleUserAuthentication;
    private handleJoinRoom;
    private handleLeaveRoom;
    private handleCreateRoom;
    private handleStartResearch;
    private handleResearchProgress;
    private handleSendMessage;
    private handleAddReaction;
    private handleUpdateStatus;
    private handleTypingStart;
    private handleTypingStop;
    private handleMarkNotificationRead;
    private handleDisconnect;
    private serializeRoom;
    private getUserRooms;
    private getRoomParticipants;
    private getUserNotifications;
    private broadcastUserPresence;
    private startCleanupTasks;
    sendNotification(notification: Omit<LiveNotification, 'id' | 'timestamp'>): void;
    updateResearchProgress(sessionId: string, progress: Partial<ResearchProgress>): void;
    getConnectedUsers(): UserPresence[];
    getRooms(): ResearchRoom[];
    getActiveResearchSessions(): ResearchProgress[];
}
export default EnhancedWebSocketService;
//# sourceMappingURL=EnhancedWebSocketService.d.ts.map