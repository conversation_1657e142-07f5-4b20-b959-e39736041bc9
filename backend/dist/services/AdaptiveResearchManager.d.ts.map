{"version": 3, "file": "AdaptiveResearchManager.d.ts", "sourceRoot": "", "sources": ["../../src/services/AdaptiveResearchManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAKtC,MAAM,WAAW,gBAAgB;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,eAAe,GAAG,SAAS,GAAG,aAAa,GAAG,YAAY,GAAG,WAAW,CAAC;IAC/E,UAAU,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IACzD,KAAK,EAAE,YAAY,EAAE,CAAC;IACtB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,eAAe,EAAE,cAAc,EAAE,CAAC;IAClC,WAAW,EAAE,mBAAmB,CAAC;IACjC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,QAAQ,GAAG,UAAU,GAAG,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;IACzE,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,SAAS,EAAE,OAAO,CAAC;IACnB,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,WAAW,GAAG,UAAU,GAAG,aAAa,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,cAAc,CAAC;IACzG,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,gBAAgB,EAAE,MAAM,CAAC;IACzB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,QAAQ,EAAE,IAAI,CAAC;IACf,aAAa,EAAE,IAAI,CAAC;CACrB;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IACzD,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,kBAAkB,EAAE,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC7D,mBAAmB,EAAE,MAAM,EAAE,CAAC;IAC9B,aAAa,EAAE,UAAU,GAAG,cAAc,GAAG,QAAQ,CAAC;IACtD,eAAe,CAAC,EAAE,GAAG,EAAE,CAAC;IACxB,cAAc,CAAC,EAAE,GAAG,EAAE,CAAC;CACxB;AAED,MAAM,WAAW,kBAAkB;IACjC,gBAAgB,EAAE,MAAM,CAAC;IACzB,eAAe,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,cAAc,EAAE,CAAC;IAC1B,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,mBAAmB,EAAE,MAAM,CAAC;CAC7B;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,YAAY,GAAG,cAAc,GAAG,eAAe,GAAG,eAAe,GAAG,qBAAqB,CAAC;IAChG,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;CACnC;AAKD,qBAAa,uBAAwB,SAAQ,YAAY;IACvD,OAAO,CAAC,UAAU,CAA4C;IAC9D,OAAO,CAAC,iBAAiB,CAAgD;IACzE,OAAO,CAAC,kBAAkB,CAA+B;IACzD,OAAO,CAAC,YAAY,CAAiC;;IAYrD,OAAO,CAAC,wBAAwB;IA4OnB,qBAAqB,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAuF1E,aAAa,CACxB,QAAQ,EAAE,gBAAgB,EAC1B,OAAO,EAAE,eAAe,EACxB,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GACrC,OAAO,CAAC,kBAAkB,CAAC;IA0EvB,yBAAyB,CAC9B,UAAU,EAAE,MAAM,EAClB,WAAW,EAAE;QACX,OAAO,EAAE,OAAO,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,EAAE,MAAM,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC;QACzB,eAAe,EAAE,MAAM,CAAC;KACzB,GACA,IAAI;IAyCP,OAAO,CAAC,WAAW;IAKnB,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,iBAAiB;IAmBzB,OAAO,CAAC,mBAAmB;IAqC3B,OAAO,CAAC,mBAAmB;IAkB3B,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,6BAA6B;IAarC,OAAO,CAAC,mBAAmB;IAY3B,OAAO,CAAC,uBAAuB;YAOjB,yBAAyB;IAkBhC,aAAa,IAAI,gBAAgB,EAAE;IAInC,WAAW,CAAC,EAAE,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAIhD,sBAAsB,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAU7C,oBAAoB,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,kBAAkB,EAAE;CAWvE;AAED,eAAe,uBAAuB,CAAC"}