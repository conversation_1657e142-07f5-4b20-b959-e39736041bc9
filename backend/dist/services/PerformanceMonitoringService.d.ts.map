{"version": 3, "file": "PerformanceMonitoringService.d.ts", "sourceRoot": "", "sources": ["../../src/services/PerformanceMonitoringService.ts"], "names": [], "mappings": "AAIA,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,KAAK,GAAG,UAAU,GAAG,OAAO,GAAG,IAAI,GAAG,UAAU,CAAC;IACvD,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC/B,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,aAAa;IAC5B,GAAG,EAAE;QACH,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,EAAE,MAAM,EAAE,CAAC;KACvB,CAAC;IACF,MAAM,EAAE;QACN,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,KAAK,EAAE;QACL,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,GAAG,EAAE;QACH,iBAAiB,EAAE,MAAM,CAAC;QAC1B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,QAAQ,EAAE;QACR,iBAAiB,EAAE,MAAM,CAAC;QAC1B,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,gBAAgB;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,SAAS,GAAG,UAAU,CAAC;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAKD,qBAAa,4BAA4B;IACvC,OAAO,CAAC,OAAO,CAA6C;IAC5D,OAAO,CAAC,gBAAgB,CAA2B;IACnD,OAAO,CAAC,oBAAoB,CAAuB;IACnD,OAAO,CAAC,MAAM,CAA0B;IACxC,OAAO,CAAC,UAAU,CAOhB;;IAUF,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM;IAoBlG,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,GAAE,OAAc,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI;IAkC9G,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;IAOzD,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM;IAU3D,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM;IAO3D,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;IAO3D,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM;IAO3D,mBAAmB,CAAC,SAAS,GAAE,MAAgB,GAAG,GAAG;IAwE/C,uBAAuB,IAAI,OAAO,CAAC,aAAa,CAAC;IA6CvD,SAAS,CAAC,QAAQ,GAAE,OAAe,GAAG,gBAAgB,EAAE;IAOxD,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAWnC,oBAAoB,CAAC,SAAS,GAAE,MAAgB,GAAG,GAAG;YAwBxC,WAAW;IAkBzB,OAAO,CAAC,oBAAoB;YAQd,sBAAsB;IAMpC,OAAO,CAAC,0BAA0B;IAgBlC,OAAO,CAAC,WAAW;IA4BnB,OAAO,CAAC,uBAAuB;IAsB/B,OAAO,CAAC,qBAAqB;IAoB7B,OAAO,CAAC,oBAAoB;IA+B5B,OAAO,CAAC,qBAAqB;CAgB9B;AAED,eAAO,MAAM,4BAA4B,8BAAqC,CAAC"}