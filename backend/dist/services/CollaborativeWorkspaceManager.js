"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborativeWorkspaceManager = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const EnhancedCacheService_1 = require("./EnhancedCacheService");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
class CollaborativeWorkspaceManager extends events_1.EventEmitter {
    workspaces = new Map();
    documentEdits = new Map();
    activeUsers = new Map();
    userCursors = new Map();
    documentLocks = new Map();
    activities = new Map();
    constructor() {
        super();
        this.startCleanupTasks();
        logger_1.logger.info('🤝 Collaborative Workspace Manager initialized');
    }
    async createWorkspace(params) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('create_workspace', 'POST');
        try {
            const workspaceId = `workspace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const owner = {
                userId: params.ownerId,
                username: params.ownerUsername,
                role: 'owner',
                joinedAt: new Date(),
                lastSeen: new Date(),
                isOnline: true,
                permissions: {
                    canRead: true,
                    canWrite: true,
                    canComment: true,
                    canInvite: true,
                    canManage: true
                }
            };
            const workspace = {
                id: workspaceId,
                name: params.name,
                description: params.description,
                type: params.type,
                ownerId: params.ownerId,
                members: new Map([[params.ownerId, owner]]),
                documents: new Map(),
                comments: new Map(),
                settings: {
                    isPublic: false,
                    allowGuestAccess: false,
                    requireApproval: true,
                    enableVersioning: true,
                    enableComments: true,
                    enableRealTimeEditing: true,
                    autoSave: true,
                    autoSaveInterval: 30,
                    ...params.settings
                },
                permissions: {
                    read: [params.ownerId],
                    write: [params.ownerId],
                    admin: [params.ownerId]
                },
                createdAt: new Date(),
                updatedAt: new Date(),
                lastActivity: new Date()
            };
            this.workspaces.set(workspaceId, workspace);
            this.activities.set(workspaceId, []);
            await EnhancedCacheService_1.enhancedCacheService.set(`workspace:${workspaceId}`, this.serializeWorkspace(workspace), { ttl: 3600, tags: ['workspaces', params.ownerId] });
            await this.logActivity(workspaceId, params.ownerId, params.ownerUsername, 'workspace_created', 'Workspace created', {
                workspaceName: params.name,
                workspaceType: params.type
            });
            this.emit('workspace_created', {
                workspaceId,
                workspace: this.serializeWorkspace(workspace)
            });
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info(`🏠 Workspace created: ${params.name} (${workspaceId}) by ${params.ownerUsername}`);
            return workspaceId;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Workspace creation failed');
            throw error;
        }
    }
    async joinWorkspace(workspaceId, userId, username) {
        const workspace = this.workspaces.get(workspaceId);
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        if (!workspace.settings.isPublic && !workspace.permissions.read.includes(userId)) {
            throw new Error('Access denied');
        }
        if (!workspace.members.has(userId)) {
            const member = {
                userId,
                username,
                role: workspace.settings.allowGuestAccess ? 'guest' : 'viewer',
                joinedAt: new Date(),
                lastSeen: new Date(),
                isOnline: true,
                permissions: {
                    canRead: true,
                    canWrite: workspace.permissions.write.includes(userId),
                    canComment: true,
                    canInvite: false,
                    canManage: workspace.permissions.admin.includes(userId)
                }
            };
            workspace.members.set(userId, member);
            workspace.lastActivity = new Date();
            workspace.updatedAt = new Date();
            if (!this.activeUsers.has(workspaceId)) {
                this.activeUsers.set(workspaceId, new Set());
            }
            this.activeUsers.get(workspaceId).add(userId);
            await this.logActivity(workspaceId, userId, username, 'member_joined', `${username} joined the workspace`);
            this.emit('member_joined', {
                workspaceId,
                member: this.serializeMember(member)
            });
            logger_1.logger.info(`👤 User ${username} joined workspace ${workspaceId}`);
        }
        else {
            const member = workspace.members.get(userId);
            member.isOnline = true;
            member.lastSeen = new Date();
            if (!this.activeUsers.has(workspaceId)) {
                this.activeUsers.set(workspaceId, new Set());
            }
            this.activeUsers.get(workspaceId).add(userId);
        }
    }
    async leaveWorkspace(workspaceId, userId) {
        const workspace = this.workspaces.get(workspaceId);
        if (!workspace)
            return;
        const member = workspace.members.get(userId);
        if (member) {
            member.isOnline = false;
            member.lastSeen = new Date();
            const activeUsers = this.activeUsers.get(workspaceId);
            if (activeUsers) {
                activeUsers.delete(userId);
            }
            const cursors = this.userCursors.get(workspaceId);
            if (cursors) {
                cursors.delete(userId);
            }
            this.emit('member_left', {
                workspaceId,
                userId,
                username: member.username
            });
            logger_1.logger.info(`👋 User ${member.username} left workspace ${workspaceId}`);
        }
    }
    async createDocument(params) {
        const workspace = this.workspaces.get(params.workspaceId);
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        const member = workspace.members.get(params.userId);
        if (!member || !member.permissions.canWrite) {
            throw new Error('Permission denied');
        }
        const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const document = {
            id: documentId,
            workspaceId: params.workspaceId,
            title: params.title,
            content: params.content || '',
            type: params.type,
            version: 1,
            createdBy: params.userId,
            createdAt: new Date(),
            lastModifiedBy: params.userId,
            lastModifiedAt: new Date(),
            collaborators: [params.userId],
            isLocked: false,
            tags: params.tags || [],
            metadata: {}
        };
        workspace.documents.set(documentId, document);
        workspace.lastActivity = new Date();
        workspace.updatedAt = new Date();
        this.documentEdits.set(documentId, []);
        await this.logActivity(params.workspaceId, params.userId, params.username, 'document_created', `Created document: ${params.title}`, { documentId, documentType: params.type });
        this.emit('document_created', {
            workspaceId: params.workspaceId,
            document: this.serializeDocument(document)
        });
        logger_1.logger.info(`📄 Document created: ${params.title} (${documentId}) in workspace ${params.workspaceId}`);
        return documentId;
    }
    async editDocument(params) {
        const document = this.findDocument(params.documentId);
        if (!document) {
            throw new Error('Document not found');
        }
        const workspace = this.workspaces.get(document.workspaceId);
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        const member = workspace.members.get(params.userId);
        if (!member || !member.permissions.canWrite) {
            throw new Error('Permission denied');
        }
        const lock = this.documentLocks.get(params.documentId);
        if (lock && lock.userId !== params.userId && lock.expiresAt > new Date()) {
            throw new Error('Document is locked by another user');
        }
        const editId = `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const edit = {
            id: editId,
            documentId: params.documentId,
            userId: params.userId,
            username: params.username,
            timestamp: new Date(),
            applied: false,
            ...params.edit
        };
        this.applyEdit(document, edit);
        if (!this.documentEdits.has(params.documentId)) {
            this.documentEdits.set(params.documentId, []);
        }
        this.documentEdits.get(params.documentId).push(edit);
        document.lastModifiedBy = params.userId;
        document.lastModifiedAt = new Date();
        document.version++;
        if (!document.collaborators.includes(params.userId)) {
            document.collaborators.push(params.userId);
        }
        workspace.lastActivity = new Date();
        workspace.updatedAt = new Date();
        this.emit('document_edited', {
            workspaceId: document.workspaceId,
            documentId: params.documentId,
            edit: this.serializeEdit(edit),
            document: this.serializeDocument(document)
        });
        if (workspace.settings.autoSave) {
            setTimeout(() => {
                this.saveDocument(params.documentId);
            }, workspace.settings.autoSaveInterval * 1000);
        }
    }
    async addComment(params) {
        const document = this.findDocument(params.documentId);
        if (!document) {
            throw new Error('Document not found');
        }
        const workspace = this.workspaces.get(document.workspaceId);
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        const member = workspace.members.get(params.userId);
        if (!member || !member.permissions.canComment) {
            throw new Error('Permission denied');
        }
        const commentId = `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const comment = {
            id: commentId,
            documentId: params.documentId,
            userId: params.userId,
            username: params.username,
            content: params.content,
            position: params.position,
            selection: params.selection,
            parentId: params.parentId,
            resolved: false,
            reactions: new Map(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        workspace.comments.set(commentId, comment);
        workspace.lastActivity = new Date();
        await this.logActivity(document.workspaceId, params.userId, params.username, 'comment_added', `Added comment to ${document.title}`, { documentId: params.documentId, commentId });
        this.emit('comment_added', {
            workspaceId: document.workspaceId,
            documentId: params.documentId,
            comment: this.serializeComment(comment)
        });
        logger_1.logger.info(`💬 Comment added to document ${params.documentId} by ${params.username}`);
        return commentId;
    }
    updateCursor(workspaceId, userId, documentId, position) {
        if (!this.userCursors.has(workspaceId)) {
            this.userCursors.set(workspaceId, new Map());
        }
        const cursors = this.userCursors.get(workspaceId);
        cursors.set(userId, { documentId, position, timestamp: new Date() });
        const workspace = this.workspaces.get(workspaceId);
        if (workspace) {
            const member = workspace.members.get(userId);
            if (member) {
                member.cursor = { documentId, position };
                member.currentDocument = documentId;
            }
        }
        this.emit('cursor_updated', {
            workspaceId,
            userId,
            documentId,
            position
        });
    }
    async lockDocument(documentId, userId, durationMs = 300000) {
        const existingLock = this.documentLocks.get(documentId);
        if (existingLock && existingLock.userId !== userId && existingLock.expiresAt > new Date()) {
            throw new Error('Document is already locked by another user');
        }
        this.documentLocks.set(documentId, {
            userId,
            expiresAt: new Date(Date.now() + durationMs)
        });
        this.emit('document_locked', {
            documentId,
            userId,
            expiresAt: new Date(Date.now() + durationMs)
        });
    }
    async unlockDocument(documentId, userId) {
        const lock = this.documentLocks.get(documentId);
        if (lock && lock.userId === userId) {
            this.documentLocks.delete(documentId);
            this.emit('document_unlocked', {
                documentId,
                userId
            });
        }
    }
    applyEdit(document, edit) {
        switch (edit.type) {
            case 'insert':
                document.content =
                    document.content.slice(0, edit.position) +
                        (edit.content || '') +
                        document.content.slice(edit.position);
                break;
            case 'delete':
                document.content =
                    document.content.slice(0, edit.position) +
                        document.content.slice(edit.position + (edit.length || 0));
                break;
            case 'replace':
                document.content =
                    document.content.slice(0, edit.position) +
                        (edit.content || '') +
                        document.content.slice(edit.position + (edit.length || 0));
                break;
        }
        edit.applied = true;
    }
    findDocument(documentId) {
        for (const workspace of this.workspaces.values()) {
            const document = workspace.documents.get(documentId);
            if (document)
                return document;
        }
        return null;
    }
    async saveDocument(documentId) {
        const document = this.findDocument(documentId);
        if (document) {
            await EnhancedCacheService_1.enhancedCacheService.set(`document:${documentId}`, this.serializeDocument(document), { ttl: 3600, tags: ['documents', document.workspaceId] });
        }
    }
    async logActivity(workspaceId, userId, username, type, description, metadata = {}) {
        const activityId = `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const activity = {
            id: activityId,
            workspaceId,
            userId,
            username,
            type,
            description,
            metadata,
            timestamp: new Date()
        };
        if (!this.activities.has(workspaceId)) {
            this.activities.set(workspaceId, []);
        }
        const activities = this.activities.get(workspaceId);
        activities.push(activity);
        if (activities.length > 1000) {
            this.activities.set(workspaceId, activities.slice(-1000));
        }
        this.emit('activity_logged', {
            workspaceId,
            activity
        });
    }
    startCleanupTasks() {
        setInterval(() => {
            const now = new Date();
            for (const [documentId, lock] of this.documentLocks) {
                if (lock.expiresAt <= now) {
                    this.documentLocks.delete(documentId);
                    this.emit('document_unlocked', { documentId, userId: lock.userId });
                }
            }
        }, 60000);
        setInterval(() => {
            const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            for (const [documentId, edits] of this.documentEdits) {
                const recentEdits = edits.filter(edit => edit.timestamp > oneWeekAgo);
                this.documentEdits.set(documentId, recentEdits);
            }
        }, 60 * 60 * 1000);
    }
    serializeWorkspace(workspace) {
        return {
            ...workspace,
            members: Array.from(workspace.members.values()).map(m => this.serializeMember(m)),
            documents: Array.from(workspace.documents.values()).map(d => this.serializeDocument(d)),
            comments: Array.from(workspace.comments.values()).map(c => this.serializeComment(c))
        };
    }
    serializeMember(member) {
        return { ...member };
    }
    serializeDocument(document) {
        return { ...document };
    }
    serializeComment(comment) {
        return {
            ...comment,
            reactions: Object.fromEntries(comment.reactions)
        };
    }
    serializeEdit(edit) {
        return { ...edit };
    }
    getWorkspace(workspaceId) {
        return this.workspaces.get(workspaceId) || null;
    }
    getUserWorkspaces(userId) {
        return Array.from(this.workspaces.values()).filter(workspace => workspace.members.has(userId));
    }
    getWorkspaceActivities(workspaceId, limit = 50) {
        const activities = this.activities.get(workspaceId) || [];
        return activities.slice(-limit);
    }
    getDocumentEdits(documentId, limit = 100) {
        const edits = this.documentEdits.get(documentId) || [];
        return edits.slice(-limit);
    }
    getActiveUsers(workspaceId) {
        return Array.from(this.activeUsers.get(workspaceId) || []);
    }
    getUserCursors(workspaceId) {
        return this.userCursors.get(workspaceId) || new Map();
    }
}
exports.CollaborativeWorkspaceManager = CollaborativeWorkspaceManager;
exports.default = CollaborativeWorkspaceManager;
//# sourceMappingURL=CollaborativeWorkspaceManager.js.map