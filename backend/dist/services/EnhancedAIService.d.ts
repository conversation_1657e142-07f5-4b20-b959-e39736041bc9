import { EventEmitter } from 'events';
export interface AIModel {
    id: string;
    name: string;
    provider: 'openai' | 'anthropic' | 'google' | 'local' | 'huggingface';
    type: 'chat' | 'completion' | 'embedding' | 'multimodal';
    capabilities: string[];
    maxTokens: number;
    costPerToken: number;
    responseTime: number;
    accuracy: number;
    reliability: number;
    specialties: string[];
    isAvailable: boolean;
    endpoint?: string;
    apiKey?: string;
    lastUsed: Date;
    usageCount: number;
    errorCount: number;
}
export interface AIRequest {
    id: string;
    prompt: string;
    context?: string;
    taskType: 'research' | 'analysis' | 'synthesis' | 'validation' | 'creative' | 'technical';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    requiresFactualAccuracy?: boolean;
    requiresCreativity?: boolean;
    requiresSpeed?: boolean;
    domain?: string;
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
}
export interface AIResponse {
    id: string;
    requestId: string;
    modelId: string;
    content: string;
    confidence: number;
    quality: number;
    factualAccuracy?: number;
    creativity?: number;
    relevance?: number;
    completeness?: number;
    tokensUsed: number;
    responseTime: number;
    cost: number;
    metadata: Record<string, any>;
    timestamp: Date;
}
export interface EnsembleResult {
    finalResponse: string;
    confidence: number;
    quality: number;
    modelResponses: AIResponse[];
    consensusScore: number;
    conflictResolution?: string;
    reasoning: string;
    metadata: Record<string, any>;
}
export declare class EnhancedAIService extends EventEmitter {
    private models;
    private activeRequests;
    private responseHistory;
    private ensembleStrategies;
    private modelPerformanceCache;
    constructor();
    private initializeModels;
    private initializeEnsembleStrategies;
    processRequest(request: AIRequest): Promise<EnsembleResult>;
    private selectOptimalModels;
    private executeModelRequest;
    private callModel;
    private callOpenAI;
    private callAnthropic;
    private callGoogle;
    private callLocalModel;
    private applyEnsembleStrategy;
    private hashRequest;
    private estimateTokens;
    private assessResponseQuality;
    private assessConfidence;
    private calculateConsensusScore;
    private generateEnsembleReasoning;
    private cacheResult;
    private updateModelPerformance;
    private startPerformanceMonitoring;
    getAvailableModels(): AIModel[];
    getModelPerformance(): Record<string, any>;
    generateResponse(prompt: string, options?: Partial<AIRequest>): Promise<EnsembleResult>;
    getResponseHistory(limit?: number): AIResponse[];
}
export default EnhancedAIService;
//# sourceMappingURL=EnhancedAIService.d.ts.map