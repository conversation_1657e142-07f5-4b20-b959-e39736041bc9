export interface Interaction {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
interface UserProfile {
    id: string;
    userId: string;
    healthGoals: string[];
    currentSupplements: string[];
    allergies: string[];
    medicalConditions: string[];
    preferences: {
        budget: number;
        naturalOnly: boolean;
        veganOnly: boolean;
        glutenFree: boolean;
        categories: string[];
    };
    restrictions: {
        maxDailyPills: number;
        avoidIngredients: string[];
        preferredBrands: string[];
    };
    profileData: {
        age?: number;
        gender?: string;
        weight?: number;
        activityLevel?: string;
        dietType?: string;
    };
    createdAt: string;
    updatedAt: string;
}
interface SupplementRecommendation {
    supplementId: string;
    name: string;
    category: string;
    score: number;
    reasoning: string;
    benefits: string[];
    dosage: string;
    timing: string[];
    price: number;
    safetyRating: number;
    evidenceLevel: number;
    interactions: Interaction[];
    alternatives: string[];
}
declare class UserProfileService {
    createUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile>;
    getUserProfile(userId: string): Promise<UserProfile | null>;
    updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile>;
    addSupplementToProfile(userId: string, supplementId: string, dosage?: string, timing?: string[]): Promise<void>;
    removeSupplementFromProfile(userId: string, supplementId: string): Promise<void>;
    getPersonalizedRecommendations(userId: string, limit?: number): Promise<SupplementRecommendation[]>;
    private linkSupplementsToProfile;
    private updateSupplementRelationships;
    private calculateRecommendationScore;
    private generateReasoningText;
}
export default UserProfileService;
//# sourceMappingURL=UserProfileService.d.ts.map