"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptEngineeringEngine = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
const EnhancedCacheService_1 = require("./EnhancedCacheService");
class PromptEngineeringEngine extends events_1.EventEmitter {
    templates = new Map();
    optimizationHistory = new Map();
    abTests = new Map();
    feedbackData = new Map();
    constructor() {
        super();
        this.initializeBaseTemplates();
        this.startOptimizationLoop();
        logger_1.logger.info('🎯 Prompt Engineering Engine initialized with self-optimization');
    }
    initializeBaseTemplates() {
        const baseTemplates = [
            {
                id: 'research_comprehensive',
                name: 'Comprehensive Research',
                category: 'research',
                template: `You are an expert researcher conducting a comprehensive analysis on {{topic}}.

Context: {{context}}
Research Focus: {{focus}}
Target Audience: {{audience}}

Please provide a thorough research analysis that includes:
1. Key findings and insights
2. Supporting evidence and sources
3. Potential implications
4. Areas for further investigation

Requirements:
- Use credible sources and evidence
- Maintain objectivity and balance
- Provide clear, actionable insights
- Format findings in a structured manner

Research Query: {{query}}`,
                variables: ['topic', 'context', 'focus', 'audience', 'query'],
                description: 'Comprehensive research template for in-depth analysis',
                examples: [
                    {
                        input: {
                            topic: 'Vitamin D supplementation',
                            context: 'Health and nutrition research',
                            focus: 'Immune system benefits',
                            audience: 'Healthcare professionals',
                            query: 'What are the evidence-based benefits of vitamin D for immune function?'
                        },
                        expectedOutput: 'Structured research analysis with evidence-based findings'
                    }
                ],
                metadata: { difficulty: 'intermediate', estimatedTime: '5-10 minutes' }
            },
            {
                id: 'analysis_critical',
                name: 'Critical Analysis',
                category: 'analysis',
                template: `Perform a critical analysis of the following content with focus on {{analysisType}}.

Content to Analyze: {{content}}
Analysis Framework: {{framework}}
Key Questions: {{questions}}

Please provide:
1. Strengths and weaknesses
2. Logical consistency
3. Evidence quality
4. Potential biases or limitations
5. Alternative perspectives
6. Conclusions and recommendations

Maintain analytical rigor and provide specific examples to support your assessment.`,
                variables: ['analysisType', 'content', 'framework', 'questions'],
                description: 'Critical analysis template for evaluating content quality',
                examples: [
                    {
                        input: {
                            analysisType: 'scientific validity',
                            content: 'Research paper on supplement efficacy',
                            framework: 'Evidence-based medicine',
                            questions: 'Is the methodology sound? Are conclusions supported?'
                        },
                        expectedOutput: 'Detailed critical analysis with specific assessments'
                    }
                ],
                metadata: { difficulty: 'advanced', estimatedTime: '10-15 minutes' }
            },
            {
                id: 'synthesis_comprehensive',
                name: 'Knowledge Synthesis',
                category: 'synthesis',
                template: `Synthesize the following information into a coherent {{outputType}}.

Source Materials: {{sources}}
Synthesis Goal: {{goal}}
Target Format: {{format}}
Key Themes: {{themes}}

Create a synthesis that:
1. Integrates key insights from all sources
2. Identifies patterns and connections
3. Resolves conflicts or contradictions
4. Provides new understanding or perspectives
5. Maintains accuracy and attribution

Focus on creating value through integration rather than simple summarization.`,
                variables: ['outputType', 'sources', 'goal', 'format', 'themes'],
                description: 'Synthesis template for combining multiple information sources',
                examples: [
                    {
                        input: {
                            outputType: 'comprehensive report',
                            sources: 'Multiple research papers on nutrition',
                            goal: 'Create unified understanding',
                            format: 'structured report',
                            themes: 'efficacy, safety, interactions'
                        },
                        expectedOutput: 'Integrated synthesis with new insights'
                    }
                ],
                metadata: { difficulty: 'advanced', estimatedTime: '15-20 minutes' }
            },
            {
                id: 'validation_factual',
                name: 'Fact Validation',
                category: 'validation',
                template: `Validate the factual accuracy of the following claims using rigorous fact-checking methods.

Claims to Validate: {{claims}}
Validation Criteria: {{criteria}}
Required Evidence Level: {{evidenceLevel}}

For each claim, provide:
1. Verification status (Verified/Partially Verified/Unverified/False)
2. Supporting evidence and sources
3. Confidence level (0-100%)
4. Limitations or caveats
5. Alternative interpretations if applicable

Maintain strict standards for evidence quality and source credibility.`,
                variables: ['claims', 'criteria', 'evidenceLevel'],
                description: 'Fact validation template for verifying information accuracy',
                examples: [
                    {
                        input: {
                            claims: 'Vitamin C prevents common cold',
                            criteria: 'Peer-reviewed research',
                            evidenceLevel: 'High-quality RCTs'
                        },
                        expectedOutput: 'Detailed fact-check with evidence assessment'
                    }
                ],
                metadata: { difficulty: 'expert', estimatedTime: '10-20 minutes' }
            }
        ];
        baseTemplates.forEach(template => {
            this.templates.set(template.id, {
                ...template,
                performance: {
                    usageCount: 0,
                    averageQuality: 0,
                    averageConfidence: 0,
                    averageResponseTime: 0,
                    successRate: 0,
                    userRatings: [],
                    lastOptimized: new Date()
                },
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            });
        });
    }
    async generateOptimizedPrompt(basePrompt, context, variables = {}) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation('generate_prompt', 'optimization');
        try {
            const cacheKey = `optimized_prompt:${this.hashPromptContext(basePrompt, context)}`;
            const cached = await EnhancedCacheService_1.enhancedCacheService.get(cacheKey);
            if (cached) {
                PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
                return this.interpolateVariables(cached, variables);
            }
            const template = await this.findBestTemplate(context);
            const optimizedPrompt = await this.optimizePrompt(template ? template.template : basePrompt, context);
            await EnhancedCacheService_1.enhancedCacheService.set(cacheKey, optimizedPrompt, {
                ttl: 3600,
                tags: ['optimized_prompts', context.taskType, context.domain]
            });
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            return this.interpolateVariables(optimizedPrompt, variables);
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Prompt optimization failed');
            throw error;
        }
    }
    async findBestTemplate(context) {
        const templates = Array.from(this.templates.values());
        const scoredTemplates = templates.map(template => {
            let score = 0;
            if (template.category === context.taskType) {
                score += 50;
            }
            score += template.performance.averageQuality * 0.3;
            score += template.performance.successRate * 0.2;
            if (template.performance.usageCount > 10) {
                score += 10;
            }
            return { template, score };
        });
        scoredTemplates.sort((a, b) => b.score - a.score);
        return scoredTemplates.length > 0 ? scoredTemplates[0].template : null;
    }
    async optimizePrompt(basePrompt, context) {
        let optimizedPrompt = basePrompt;
        optimizedPrompt = this.applyToneOptimization(optimizedPrompt, context.tone);
        optimizedPrompt = this.applyFormatOptimization(optimizedPrompt, context.outputFormat);
        optimizedPrompt = this.applyConstraintOptimization(optimizedPrompt, context.constraints);
        optimizedPrompt = this.applyRequirementOptimization(optimizedPrompt, context.requirements);
        optimizedPrompt = this.applyUserLevelOptimization(optimizedPrompt, context.userLevel);
        optimizedPrompt = this.applyBestPractices(optimizedPrompt);
        return optimizedPrompt;
    }
    applyToneOptimization(prompt, tone) {
        const toneInstructions = {
            formal: 'Maintain a professional and formal tone throughout your response.',
            casual: 'Use a conversational and approachable tone.',
            technical: 'Use precise technical language appropriate for experts.',
            friendly: 'Adopt a warm and helpful tone while remaining informative.'
        };
        const instruction = toneInstructions[tone];
        if (instruction && !prompt.includes(instruction)) {
            return `${prompt}\n\nTone: ${instruction}`;
        }
        return prompt;
    }
    applyFormatOptimization(prompt, format) {
        const formatInstructions = {
            json: 'Provide your response in valid JSON format.',
            markdown: 'Format your response using Markdown syntax for better readability.',
            html: 'Structure your response using appropriate HTML tags.',
            text: 'Provide a clear, well-structured text response.'
        };
        const instruction = formatInstructions[format];
        if (instruction && !prompt.toLowerCase().includes(format)) {
            return `${prompt}\n\nOutput Format: ${instruction}`;
        }
        return prompt;
    }
    applyConstraintOptimization(prompt, constraints) {
        if (constraints.length === 0)
            return prompt;
        const constraintText = constraints.map(c => `- ${c}`).join('\n');
        return `${prompt}\n\nConstraints:\n${constraintText}`;
    }
    applyRequirementOptimization(prompt, requirements) {
        if (requirements.length === 0)
            return prompt;
        const requirementText = requirements.map(r => `- ${r}`).join('\n');
        return `${prompt}\n\nRequirements:\n${requirementText}`;
    }
    applyUserLevelOptimization(prompt, userLevel) {
        const levelInstructions = {
            beginner: 'Explain concepts clearly and avoid excessive jargon. Provide context for technical terms.',
            intermediate: 'Assume moderate familiarity with the topic. Balance detail with accessibility.',
            expert: 'Use appropriate technical language and focus on advanced insights and nuances.'
        };
        const instruction = levelInstructions[userLevel];
        if (instruction) {
            return `${prompt}\n\nAudience Level: ${instruction}`;
        }
        return prompt;
    }
    applyBestPractices(prompt) {
        let optimized = prompt;
        if (!optimized.includes('Please provide') && !optimized.includes('Your task is')) {
            optimized = `Your task is to ${optimized}`;
        }
        if (optimized.length > 500 && !optimized.includes('think step by step')) {
            optimized = `${optimized}\n\nPlease think step by step and provide a thorough response.`;
        }
        if (!optimized.includes('provide') && !optimized.includes('generate') && !optimized.includes('create')) {
            optimized = `${optimized}\n\nPlease provide a comprehensive response.`;
        }
        return optimized;
    }
    interpolateVariables(prompt, variables) {
        let result = prompt;
        Object.entries(variables).forEach(([key, value]) => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            result = result.replace(regex, value);
        });
        result = result.replace(/{{[^}]+}}/g, '[VARIABLE_NOT_PROVIDED]');
        return result;
    }
    async testPromptPerformance(promptA, promptB, testInputs, modelId) {
        const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            const resultsA = [];
            const resultsB = [];
            for (const input of testInputs) {
                const [resultA, resultB] = await Promise.all([
                    this.executePromptTest(promptA, input, modelId),
                    this.executePromptTest(promptB, input, modelId)
                ]);
                resultsA.push(resultA);
                resultsB.push(resultB);
            }
            const avgQualityA = resultsA.reduce((sum, r) => sum + r.quality, 0) / resultsA.length;
            const avgQualityB = resultsB.reduce((sum, r) => sum + r.quality, 0) / resultsB.length;
            const avgConfidenceA = resultsA.reduce((sum, r) => sum + r.confidence, 0) / resultsA.length;
            const avgConfidenceB = resultsB.reduce((sum, r) => sum + r.confidence, 0) / resultsB.length;
            const scoreA = (avgQualityA * 0.7) + (avgConfidenceA * 0.3);
            const scoreB = (avgQualityB * 0.7) + (avgConfidenceB * 0.3);
            let winner;
            if (Math.abs(scoreA - scoreB) < 5) {
                winner = 'tie';
            }
            else {
                winner = scoreA > scoreB ? 'A' : 'B';
            }
            const results = {
                testId,
                promptA: { avgQuality: avgQualityA, avgConfidence: avgConfidenceA, score: scoreA },
                promptB: { avgQuality: avgQualityB, avgConfidence: avgConfidenceB, score: scoreB },
                detailedResults: { resultsA, resultsB },
                winner,
                improvement: Math.abs(scoreA - scoreB)
            };
            this.abTests.set(testId, results);
            this.emit('ab_test_completed', { testId, results });
            return { winner, results };
        }
        catch (error) {
            logger_1.logger.error('A/B test failed:', error);
            throw error;
        }
    }
    async executePromptTest(prompt, input, modelId) {
        const startTime = Date.now();
        const simulatedOutput = `Test response for prompt: ${prompt.substring(0, 50)}...`;
        const responseTime = Date.now() - startTime;
        return {
            prompt,
            input,
            output: simulatedOutput,
            quality: Math.random() * 40 + 60,
            confidence: Math.random() * 30 + 70,
            responseTime,
            modelId
        };
    }
    recordFeedback(promptId, rating, feedback, metadata = {}) {
        if (!this.feedbackData.has(promptId)) {
            this.feedbackData.set(promptId, []);
        }
        this.feedbackData.get(promptId).push({
            rating,
            feedback,
            metadata,
            timestamp: new Date()
        });
        const template = this.templates.get(promptId);
        if (template) {
            template.performance.userRatings.push(rating);
            template.updatedAt = new Date();
        }
        this.emit('feedback_recorded', { promptId, rating, feedback });
    }
    getPromptAnalytics(promptId) {
        if (promptId) {
            const template = this.templates.get(promptId);
            const feedback = this.feedbackData.get(promptId) || [];
            return {
                template,
                feedback,
                analytics: {
                    totalUsage: template?.performance.usageCount || 0,
                    averageRating: feedback.length > 0 ?
                        feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : 0,
                    lastUsed: template?.performance.lastOptimized
                }
            };
        }
        const templates = Array.from(this.templates.values());
        const totalUsage = templates.reduce((sum, t) => sum + t.performance.usageCount, 0);
        const avgQuality = templates.reduce((sum, t) => sum + t.performance.averageQuality, 0) / templates.length;
        return {
            totalTemplates: templates.length,
            totalUsage,
            averageQuality: avgQuality,
            topPerformers: templates
                .sort((a, b) => b.performance.averageQuality - a.performance.averageQuality)
                .slice(0, 5)
                .map(t => ({ id: t.id, name: t.name, quality: t.performance.averageQuality }))
        };
    }
    hashPromptContext(prompt, context) {
        const key = `${prompt}_${context.taskType}_${context.domain}_${context.tone}_${context.outputFormat}`;
        return Buffer.from(key).toString('base64').substring(0, 32);
    }
    startOptimizationLoop() {
        setInterval(() => {
            this.analyzeAndOptimizeTemplates();
        }, 3600000);
    }
    async analyzeAndOptimizeTemplates() {
        try {
            for (const [templateId, template] of this.templates) {
                if (template.performance.usageCount > 10) {
                    const feedback = this.feedbackData.get(templateId) || [];
                    const avgRating = feedback.length > 0 ?
                        feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : 0;
                    if (avgRating < 7 || template.performance.averageQuality < 80) {
                        logger_1.logger.info(`Scheduling optimization for template: ${template.name}`);
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Template optimization analysis failed:', error);
        }
    }
    getTemplates() {
        return Array.from(this.templates.values());
    }
    getTemplate(id) {
        return this.templates.get(id) || null;
    }
    async createTemplate(template) {
        const newTemplate = {
            ...template,
            performance: {
                usageCount: 0,
                averageQuality: 0,
                averageConfidence: 0,
                averageResponseTime: 0,
                successRate: 0,
                userRatings: [],
                lastOptimized: new Date()
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        this.templates.set(template.id, newTemplate);
        this.emit('template_created', { templateId: template.id });
        return template.id;
    }
}
exports.PromptEngineeringEngine = PromptEngineeringEngine;
exports.default = PromptEngineeringEngine;
//# sourceMappingURL=PromptEngineeringEngine.js.map