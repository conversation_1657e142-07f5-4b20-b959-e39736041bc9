interface ResearchPaper {
    id: string;
    title: string;
    authors: string[];
    journal: string;
    year: number;
    doi?: string;
    pmid?: string;
    abstract: string;
    keywords: string[];
    studyType: 'RCT' | 'Meta-Analysis' | 'Observational' | 'Review' | 'Case Study';
    evidenceLevel: number;
    sampleSize?: number;
    duration?: string;
    findings: {
        primary: string;
        secondary: string[];
        significance: 'high' | 'moderate' | 'low';
        confidence: number;
    };
    supplements: string[];
    conditions: string[];
    interactions: string[];
    safetyProfile: {
        adverse: string[];
        contraindications: string[];
        warnings: string[];
        riskLevel: 'low' | 'moderate' | 'high';
    };
    qualityScore: number;
    relevanceScore: number;
    citationCount: number;
    lastUpdated: string;
}
interface AIInsight {
    id: string;
    type: 'efficacy' | 'safety' | 'dosage' | 'interaction' | 'mechanism' | 'breakthrough';
    title: string;
    content: string;
    confidence: number;
    impact: 'low' | 'moderate' | 'high';
    sources: string[];
    relevantSupplements: string[];
    lastUpdated: string;
}
interface ResearchQuery {
    supplement?: string;
    condition?: string;
    studyType?: string;
    minYear?: number;
    minEvidenceLevel?: number;
    includeMetaAnalysis?: boolean;
    language?: string;
    maxResults?: number;
}
declare class ResearchAIService {
    private aiService;
    constructor();
    enhancedSupplementSearch(params: {
        originalQuery: string;
        enhancedQuery: string;
        includeTavily: boolean;
        includeBraveSearch: boolean;
        includeScientificDatabases: boolean;
    }): Promise<any>;
    searchResearchPapers(query: ResearchQuery): Promise<ResearchPaper[]>;
    generateAIInsights(papers: ResearchPaper[], userProfile?: any): Promise<AIInsight[]>;
    analyzeSupplementInteractions(supplementIds: string[]): Promise<any[]>;
    monitorRealTimeResearch(supplements: string[]): Promise<any>;
    private simulateResearchSearch;
    private analyzeBreakthroughFindings;
    private analyzeSafetyPatterns;
    private analyzeEfficacyTrends;
    private generatePersonalizedInsights;
    private generateResearchAlerts;
    private calculateClinicalRelevance;
    generateTaskPlan(query: string, type: string): Promise<any>;
    analyzeContent(content: string, options: any): Promise<any>;
    synthesizeResults(data: any, options: any): Promise<any>;
    validateResults(results: any, options: any): Promise<any>;
}
export default ResearchAIService;
//# sourceMappingURL=ResearchAIService.d.ts.map