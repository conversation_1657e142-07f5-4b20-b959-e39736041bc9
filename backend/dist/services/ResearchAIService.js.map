{"version": 3, "file": "ResearchAIService.js", "sourceRoot": "", "sources": ["../../src/services/ResearchAIService.ts"], "names": [], "mappings": ";;AAAA,0CAAmD;AACnD,0CAAoD;AAEpD,2CAAkD;AAClD,oDAAiD;AAoEjD,MAAM,iBAAiB;IACb,SAAS,CAAY;IAE7B;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,EAAE,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,MAM9B;QAEC,OAAO;YACL;gBACE,MAAM,EAAE,QAAQ;gBAChB,KAAK,EAAE,wCAAwC;gBAC/C,GAAG,EAAE,0CAA0C;gBAC/C,QAAQ,EAAE,+DAA+D;gBACzE,SAAS,EAAE,IAAI;aAChB;YACD;gBACE,MAAM,EAAE,gBAAgB;gBACxB,KAAK,EAAE,8CAA8C;gBACrD,GAAG,EAAE,qCAAqC;gBAC1C,QAAQ,EAAE,4DAA4D;gBACtE,SAAS,EAAE,IAAI;aAChB;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAAoB;QAC7C,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAE5D,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAQ,EAAC,QAAQ,CAAC,CAAC;YACxC,IAAI,MAAM;gBAAE,OAAO,MAAM,CAAC;YAG1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAGxD,MAAM,IAAA,gBAAQ,EAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAEvC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,KAAK;gBACL,YAAY,EAAE,MAAM,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,kCAAkC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAuB,EAAE,WAAiB;QACjE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAgB,EAAE,CAAC;YAGjC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAC5E,QAAQ,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;YAGvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;YAGjC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAClE,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;YAGnC,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAC1F,QAAQ,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,aAAuB;QACzD,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG;;;;;OAKb,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAA,yBAAiB,EAAC,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;YAEjE,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;gBACtD,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACxC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAEpC,OAAO;oBACL,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI;oBAC/B,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI;oBAC/B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,IAAI,GAAG;oBACxC,aAAa,EAAE,QAAQ,IAAI,CAAC;oBAC5B,YAAY,EAAE,MAAM,IAAI,CAAC;oBACzB,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,WAAW,IAAI,EAAE;oBAC7C,SAAS,EAAE,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,EAAE;oBACzC,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC;iBACnE,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,WAAqB;QACjD,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG;gBACZ,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;gBACnD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBACpD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC3C,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAClD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;YAE9D,OAAO;gBACL,KAAK;gBACL,MAAM;gBACN,UAAU,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACnC,UAAU,EAAE,IAAI;oBAChB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBACzC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACrC,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,iBAAQ,EAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAoB;QAEvD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,MAAM,YAAY,GAAoB;YACpC;gBACE,EAAE,EAAE,WAAW;gBACf,KAAK,EAAE,+EAA+E;gBACtF,OAAO,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,gBAAgB,CAAC;gBAC3D,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,qBAAqB;gBAC1B,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,gLAAgL;gBAC1L,QAAQ,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,KAAK,CAAC;gBACtF,SAAS,EAAE,eAAe;gBAC1B,aAAa,EAAE,CAAC;gBAChB,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,aAAa;gBACvB,QAAQ,EAAE;oBACR,OAAO,EAAE,4EAA4E;oBACrF,SAAS,EAAE;wBACT,gCAAgC;wBAChC,0BAA0B;wBAC1B,4BAA4B;wBAC5B,8BAA8B;qBAC/B;oBACD,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,EAAE;iBACf;gBACD,WAAW,EAAE,CAAC,YAAY,CAAC;gBAC3B,UAAU,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,sBAAsB,CAAC;gBACnF,YAAY,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC;gBACpD,aAAa,EAAE;oBACb,OAAO,EAAE,CAAC,sCAAsC,EAAE,8BAA8B,CAAC;oBACjF,iBAAiB,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC;oBACpE,QAAQ,EAAE,CAAC,wBAAwB,EAAE,uBAAuB,CAAC;oBAC7D,SAAS,EAAE,KAAK;iBACjB;gBACD,YAAY,EAAE,GAAG;gBACjB,cAAc,EAAE,IAAI;gBACpB,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,YAAY;aAC1B;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,KAAK,EAAE,mFAAmF;gBAC1F,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,cAAc,CAAC;gBACtD,OAAO,EAAE,gCAAgC;gBACzC,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,sBAAsB;gBAC3B,QAAQ,EAAE,4JAA4J;gBACtK,QAAQ,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;gBAC9E,SAAS,EAAE,KAAK;gBAChB,aAAa,EAAE,CAAC;gBAChB,UAAU,EAAE,GAAG;gBACf,QAAQ,EAAE,UAAU;gBACpB,QAAQ,EAAE;oBACR,OAAO,EAAE,wEAAwE;oBACjF,SAAS,EAAE;wBACT,6BAA6B;wBAC7B,uBAAuB;wBACvB,uBAAuB;wBACvB,qCAAqC;qBACtC;oBACD,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,EAAE;iBACf;gBACD,WAAW,EAAE,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;gBACvD,UAAU,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,CAAC;gBACvE,YAAY,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC;gBAC9C,aAAa,EAAE;oBACb,OAAO,EAAE,CAAC,+CAA+C,EAAE,wBAAwB,CAAC;oBACpF,iBAAiB,EAAE,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;oBACjE,QAAQ,EAAE,CAAC,wBAAwB,EAAE,2BAA2B,CAAC;oBACjE,SAAS,EAAE,KAAK;iBACjB;gBACD,YAAY,EAAE,GAAG;gBACjB,cAAc,EAAE,IAAI;gBACpB,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,YAAY;aAC1B;SACF,CAAC;QAGF,IAAI,cAAc,GAAG,YAAY,CAAC;QAElC,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC5B,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAW,CAAC,WAAW,EAAE,CAAC,CAC7D,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3B,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAU,CAAC,WAAW,EAAE,CAAC,CAC5D,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CACpC,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,OAAQ,CAC7B,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC3B,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,gBAAiB,CAC/C,CAAC;QACJ,CAAC;QAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,MAAuB;QAC/D,MAAM,QAAQ,GAAgB,EAAE,CAAC;QAGjC,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAC5D,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,2CAA2C;gBAClD,OAAO,EAAE,GAAG,gBAAgB,CAAC,MAAM,oJAAoJ;gBACvL,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxC,mBAAmB,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC/E,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAuB;QACzD,MAAM,QAAQ,GAAgB,EAAE,CAAC;QAGjC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACzC,KAAK,CAAC,aAAa,CAAC,SAAS,KAAK,UAAU,IAAI,KAAK,CAAC,aAAa,CAAC,SAAS,KAAK,MAAM,CACzF,CAAC;QAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC1B,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,kCAAkC;gBACzC,OAAO,EAAE,eAAe,MAAM,CAAC,MAAM,wDAAwD,YAAY,CAAC,MAAM,4EAA4E;gBAC5L,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,mBAAmB,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC3E,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAuB;QACzD,MAAM,QAAQ,GAAgB,EAAE,CAAC;QAGjC,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,QAAQ,CAAC,YAAY,KAAK,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,GAAG,CACpE,CAAC;QAEF,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,gCAAgC;gBACvC,OAAO,EAAE,GAAG,kBAAkB,CAAC,MAAM,6IAA6I;gBAClL,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,mBAAmB,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;gBACjF,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,MAAuB,EAAE,WAAgB;QAClF,MAAM,QAAQ,GAAgB,EAAE,CAAC;QAGjC,MAAM,eAAe,GAAG,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;QAC7D,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC3C,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC5B,eAAe,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE,CACxC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACpD,CACF,CACF,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,uCAAuC;gBAC9C,OAAO,EAAE,SAAS,cAAc,CAAC,MAAM,yHAAyH;gBAChK,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtC,mBAAmB,EAAE,eAAe;gBACpC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,WAAqB;QACxD,MAAM,MAAM,GAAG,EAAE,CAAC;QAGlB,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,UAAU,EAAE;oBACvC,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,wBAAwB;oBAC/B,OAAO,EAAE,0BAA0B,UAAU,4BAA4B;oBACzE,UAAU;oBACV,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,0BAA0B,CAAC,UAAe;QAChD,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,IAAI,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,GAAG,CAAC;QAC5C,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,CAAC,CAAC;QAElD,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;QAE9E,IAAI,KAAK,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9B,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,UAAU,CAAC;QACpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,IAAY;QAChD,eAAM,CAAC,IAAI,CAAC,mCAAmC,KAAK,WAAW,IAAI,EAAE,CAAC,CAAC;QAEvE,OAAO;YACL,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,KAAK;oBACZ,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE;oBACrD,QAAQ,EAAE,MAAM;oBAChB,iBAAiB,EAAE,KAAK;oBACxB,YAAY,EAAE,EAAE;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,wBAAwB,KAAK,EAAE;oBACtC,UAAU,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,YAAY,EAAE,eAAe,EAAE;oBAClF,QAAQ,EAAE,QAAQ;oBAClB,iBAAiB,EAAE,KAAK;oBACxB,YAAY,EAAE,CAAC,QAAQ,CAAC;iBACzB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,2BAA2B,KAAK,EAAE;oBACzC,UAAU,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE;oBAC5D,QAAQ,EAAE,QAAQ;oBAClB,iBAAiB,EAAE,MAAM;oBACzB,YAAY,EAAE,CAAC,QAAQ,CAAC;iBACzB;aACF;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,OAAY;QAChD,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE1E,OAAO;YACL,cAAc,EAAE,2BAA2B,OAAO,CAAC,YAAY,aAAa;YAC5E,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACpC,UAAU,EAAE,IAAI;SACjB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,IAAS,EAAE,OAAY;QAC7C,eAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE7E,OAAO;YACL,iBAAiB,EAAE,qCAAqC,OAAO,CAAC,MAAM,UAAU;YAChF,eAAe,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;YACzD,OAAO,EAAE,GAAG;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAY,EAAE,OAAY;QAC9C,eAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE3E,OAAO;YACL,gBAAgB,EAAE,QAAQ;YAC1B,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,CAAC;SACf,CAAC;IACJ,CAAC;CACF;AAED,kBAAe,iBAAiB,CAAC"}