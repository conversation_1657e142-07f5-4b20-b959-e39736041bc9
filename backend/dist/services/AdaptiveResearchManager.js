"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdaptiveResearchManager = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
const EnhancedCacheService_1 = require("./EnhancedCacheService");
class AdaptiveResearchManager extends events_1.EventEmitter {
    strategies = new Map();
    adaptationHistory = new Map();
    performanceMetrics = new Map();
    learningData = new Map();
    constructor() {
        super();
        this.initializeBaseStrategies();
        this.startAdaptationLearning();
        logger_1.logger.info('🧭 Adaptive Research Strategy Manager initialized');
    }
    initializeBaseStrategies() {
        const baseStrategies = [
            {
                id: 'comprehensive_deep_dive',
                name: 'Comprehensive Deep Dive',
                description: 'Thorough multi-phase research with extensive validation',
                type: 'comprehensive',
                complexity: 'complex',
                estimatedDuration: 1800000,
                requiredResources: ['search_agents', 'analysis_agents', 'validation_agents'],
                successCriteria: ['high_quality_sources', 'comprehensive_coverage', 'validated_findings'],
                steps: [
                    {
                        id: 'initial_search',
                        name: 'Initial Broad Search',
                        type: 'search',
                        description: 'Conduct broad search to understand topic landscape',
                        inputs: ['query', 'domain'],
                        outputs: ['initial_sources', 'topic_map'],
                        dependencies: [],
                        estimatedTime: 300000,
                        priority: 'critical',
                        adaptable: true,
                        fallbackOptions: ['focused_search']
                    },
                    {
                        id: 'source_analysis',
                        name: 'Source Quality Analysis',
                        type: 'analysis',
                        description: 'Analyze and rank sources by quality and relevance',
                        inputs: ['initial_sources'],
                        outputs: ['ranked_sources', 'quality_metrics'],
                        dependencies: ['initial_search'],
                        estimatedTime: 420000,
                        priority: 'high',
                        adaptable: true,
                        fallbackOptions: ['basic_filtering']
                    },
                    {
                        id: 'deep_content_analysis',
                        name: 'Deep Content Analysis',
                        type: 'analysis',
                        description: 'Detailed analysis of top-quality sources',
                        inputs: ['ranked_sources'],
                        outputs: ['key_insights', 'evidence_map'],
                        dependencies: ['source_analysis'],
                        estimatedTime: 600000,
                        priority: 'high',
                        adaptable: true,
                        fallbackOptions: ['surface_analysis']
                    },
                    {
                        id: 'cross_validation',
                        name: 'Cross-Validation',
                        type: 'validation',
                        description: 'Validate findings across multiple sources',
                        inputs: ['key_insights', 'evidence_map'],
                        outputs: ['validated_insights', 'confidence_scores'],
                        dependencies: ['deep_content_analysis'],
                        estimatedTime: 360000,
                        priority: 'medium',
                        adaptable: true,
                        fallbackOptions: ['basic_validation']
                    },
                    {
                        id: 'synthesis_report',
                        name: 'Comprehensive Synthesis',
                        type: 'synthesis',
                        description: 'Create comprehensive research report',
                        inputs: ['validated_insights', 'confidence_scores'],
                        outputs: ['final_report', 'recommendations'],
                        dependencies: ['cross_validation'],
                        estimatedTime: 480000,
                        priority: 'critical',
                        adaptable: false,
                        fallbackOptions: []
                    }
                ],
                adaptationRules: [
                    {
                        condition: 'time_constraint < 900000',
                        action: 'reduce_scope',
                        parameters: { skip_steps: ['cross_validation'], reduce_depth: true },
                        priority: 1
                    },
                    {
                        condition: 'quality_requirement == "basic"',
                        action: 'skip_step',
                        parameters: { step_id: 'cross_validation' },
                        priority: 2
                    }
                ],
                metadata: { difficulty: 'high', recommended_for: ['expert_users', 'critical_research'] }
            },
            {
                id: 'focused_rapid',
                name: 'Focused Rapid Research',
                description: 'Quick, targeted research for specific questions',
                type: 'focused',
                complexity: 'simple',
                estimatedDuration: 600000,
                requiredResources: ['search_agents'],
                successCriteria: ['relevant_answers', 'quick_turnaround'],
                steps: [
                    {
                        id: 'targeted_search',
                        name: 'Targeted Search',
                        type: 'search',
                        description: 'Search for specific information related to query',
                        inputs: ['query'],
                        outputs: ['relevant_sources'],
                        dependencies: [],
                        estimatedTime: 240000,
                        priority: 'critical',
                        adaptable: true,
                        fallbackOptions: ['broad_search']
                    },
                    {
                        id: 'quick_analysis',
                        name: 'Quick Analysis',
                        type: 'analysis',
                        description: 'Rapid analysis of most relevant sources',
                        inputs: ['relevant_sources'],
                        outputs: ['key_findings'],
                        dependencies: ['targeted_search'],
                        estimatedTime: 180000,
                        priority: 'high',
                        adaptable: true,
                        fallbackOptions: ['summary_extraction']
                    },
                    {
                        id: 'rapid_synthesis',
                        name: 'Rapid Synthesis',
                        type: 'synthesis',
                        description: 'Quick synthesis of findings',
                        inputs: ['key_findings'],
                        outputs: ['concise_answer'],
                        dependencies: ['quick_analysis'],
                        estimatedTime: 180000,
                        priority: 'critical',
                        adaptable: false,
                        fallbackOptions: []
                    }
                ],
                adaptationRules: [
                    {
                        condition: 'insufficient_sources',
                        action: 'add_step',
                        parameters: { step_type: 'search', expand_query: true },
                        priority: 1
                    }
                ],
                metadata: { difficulty: 'low', recommended_for: ['quick_questions', 'time_constrained'] }
            },
            {
                id: 'exploratory_discovery',
                name: 'Exploratory Discovery',
                description: 'Open-ended exploration for new insights and connections',
                type: 'exploratory',
                complexity: 'moderate',
                estimatedDuration: 1200000,
                requiredResources: ['search_agents', 'analysis_agents'],
                successCriteria: ['novel_insights', 'connection_discovery', 'comprehensive_mapping'],
                steps: [
                    {
                        id: 'broad_exploration',
                        name: 'Broad Topic Exploration',
                        type: 'search',
                        description: 'Explore topic from multiple angles and perspectives',
                        inputs: ['query', 'domain'],
                        outputs: ['diverse_sources', 'topic_dimensions'],
                        dependencies: [],
                        estimatedTime: 360000,
                        priority: 'critical',
                        adaptable: true,
                        fallbackOptions: ['focused_search']
                    },
                    {
                        id: 'pattern_analysis',
                        name: 'Pattern and Connection Analysis',
                        type: 'analysis',
                        description: 'Identify patterns and connections across sources',
                        inputs: ['diverse_sources'],
                        outputs: ['patterns', 'connections', 'themes'],
                        dependencies: ['broad_exploration'],
                        estimatedTime: 480000,
                        priority: 'high',
                        adaptable: true,
                        fallbackOptions: ['basic_categorization']
                    },
                    {
                        id: 'insight_generation',
                        name: 'Novel Insight Generation',
                        type: 'synthesis',
                        description: 'Generate novel insights from discovered patterns',
                        inputs: ['patterns', 'connections', 'themes'],
                        outputs: ['novel_insights', 'hypotheses'],
                        dependencies: ['pattern_analysis'],
                        estimatedTime: 360000,
                        priority: 'medium',
                        adaptable: true,
                        fallbackOptions: ['summary_insights']
                    }
                ],
                adaptationRules: [
                    {
                        condition: 'low_diversity_sources',
                        action: 'modify_step',
                        parameters: { step_id: 'broad_exploration', expand_search_terms: true },
                        priority: 1
                    }
                ],
                metadata: { difficulty: 'medium', recommended_for: ['research_discovery', 'innovation'] }
            }
        ];
        baseStrategies.forEach(strategy => {
            this.strategies.set(strategy.id, {
                ...strategy,
                performance: {
                    usageCount: 0,
                    successRate: 0,
                    averageQuality: 0,
                    averageTime: 0,
                    userSatisfaction: 0,
                    adaptationFrequency: 0,
                    lastUsed: new Date(),
                    lastOptimized: new Date()
                }
            });
        });
    }
    async selectOptimalStrategy(context) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation('select_strategy', 'strategy_selection');
        try {
            const cacheKey = `strategy_selection:${this.hashContext(context)}`;
            const cached = await EnhancedCacheService_1.enhancedCacheService.get(cacheKey);
            if (cached) {
                const strategy = this.strategies.get(cached);
                if (strategy) {
                    PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
                    return strategy;
                }
            }
            const strategies = Array.from(this.strategies.values());
            const scoredStrategies = strategies.map(strategy => {
                let score = 0;
                if (strategy.complexity === context.complexity) {
                    score += 30;
                }
                else if (this.isComplexityCompatible(strategy.complexity, context.complexity)) {
                    score += 15;
                }
                if (context.qualityRequirement === 'expert' && strategy.type === 'comprehensive') {
                    score += 25;
                }
                else if (context.qualityRequirement === 'basic' && strategy.type === 'focused') {
                    score += 25;
                }
                if (context.timeConstraint) {
                    if (strategy.estimatedDuration <= context.timeConstraint) {
                        score += 20;
                    }
                    else {
                        score -= Math.min((strategy.estimatedDuration - context.timeConstraint) / 60000, 20);
                    }
                }
                score += strategy.performance.successRate * 0.2;
                score += strategy.performance.averageQuality * 0.1;
                score += strategy.performance.userSatisfaction * 0.1;
                if (context.userExpertise === 'expert' && strategy.complexity === 'expert') {
                    score += 15;
                }
                else if (context.userExpertise === 'beginner' && strategy.complexity === 'simple') {
                    score += 15;
                }
                return { strategy, score };
            });
            scoredStrategies.sort((a, b) => b.score - a.score);
            const selectedStrategy = scoredStrategies[0].strategy;
            await EnhancedCacheService_1.enhancedCacheService.set(cacheKey, selectedStrategy.id, {
                ttl: 1800,
                tags: ['strategy_selections', context.domain]
            });
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            this.emit('strategy_selected', {
                strategyId: selectedStrategy.id,
                context,
                score: scoredStrategies[0].score
            });
            return selectedStrategy;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Strategy selection failed');
            throw error;
        }
    }
    async adaptStrategy(strategy, context, currentConditions) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation('adapt_strategy', 'strategy_adaptation');
        try {
            const adaptedStrategy = JSON.parse(JSON.stringify(strategy));
            const changes = [];
            let reasoning = 'Strategy adapted based on: ';
            const reasoningParts = [];
            for (const rule of strategy.adaptationRules) {
                if (this.evaluateCondition(rule.condition, context, currentConditions)) {
                    const change = this.applyAdaptationRule(adaptedStrategy, rule);
                    if (change) {
                        changes.push(change);
                        reasoningParts.push(`${rule.condition} -> ${rule.action}`);
                    }
                }
            }
            if (currentConditions.timeRemaining && currentConditions.timeRemaining < strategy.estimatedDuration * 0.5) {
                const change = this.reduceStrategyScope(adaptedStrategy);
                if (change) {
                    changes.push(change);
                    reasoningParts.push('time constraint -> scope reduction');
                }
            }
            if (currentConditions.lowQualitySources) {
                const change = this.enhanceValidation(adaptedStrategy);
                if (change) {
                    changes.push(change);
                    reasoningParts.push('low quality sources -> enhanced validation');
                }
            }
            reasoning += reasoningParts.join(', ');
            const decision = {
                originalStrategy: strategy.id,
                adaptedStrategy: adaptedStrategy.id,
                changes,
                reasoning,
                confidence: this.calculateAdaptationConfidence(changes),
                expectedImprovement: this.estimateImprovement(changes)
            };
            if (!this.adaptationHistory.has(strategy.id)) {
                this.adaptationHistory.set(strategy.id, []);
            }
            this.adaptationHistory.get(strategy.id).push(decision);
            strategy.performance.adaptationFrequency++;
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            this.emit('strategy_adapted', {
                originalStrategy: strategy.id,
                decision
            });
            return decision;
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Strategy adaptation failed');
            throw error;
        }
    }
    recordStrategyPerformance(strategyId, performance) {
        const strategy = this.strategies.get(strategyId);
        if (!strategy)
            return;
        strategy.performance.usageCount++;
        strategy.performance.lastUsed = new Date();
        if (performance.success) {
            strategy.performance.successRate =
                (strategy.performance.successRate * (strategy.performance.usageCount - 1) + 100) / strategy.performance.usageCount;
        }
        else {
            strategy.performance.successRate =
                (strategy.performance.successRate * (strategy.performance.usageCount - 1)) / strategy.performance.usageCount;
        }
        strategy.performance.averageQuality =
            (strategy.performance.averageQuality * (strategy.performance.usageCount - 1) + performance.quality) / strategy.performance.usageCount;
        strategy.performance.averageTime =
            (strategy.performance.averageTime * (strategy.performance.usageCount - 1) + performance.actualTime) / strategy.performance.usageCount;
        strategy.performance.userSatisfaction =
            (strategy.performance.userSatisfaction * (strategy.performance.usageCount - 1) + performance.userSatisfaction) / strategy.performance.usageCount;
        if (!this.learningData.has(strategyId)) {
            this.learningData.set(strategyId, []);
        }
        this.learningData.get(strategyId).push({
            performance,
            timestamp: new Date()
        });
        this.emit('performance_recorded', {
            strategyId,
            performance: strategy.performance
        });
    }
    hashContext(context) {
        const key = `${context.query}_${context.domain}_${context.complexity}_${context.qualityRequirement}_${context.userExpertise}`;
        return Buffer.from(key).toString('base64').substring(0, 32);
    }
    isComplexityCompatible(strategyComplexity, contextComplexity) {
        const complexityLevels = { simple: 1, moderate: 2, complex: 3, expert: 4 };
        const strategyLevel = complexityLevels[strategyComplexity];
        const contextLevel = complexityLevels[contextComplexity];
        return Math.abs(strategyLevel - contextLevel) <= 1;
    }
    evaluateCondition(condition, context, currentConditions) {
        if (condition.includes('time_constraint')) {
            const threshold = parseInt(condition.split('<')[1]?.trim() || '0');
            return (context.timeConstraint || Infinity) < threshold;
        }
        if (condition.includes('quality_requirement')) {
            const requirement = condition.split('==')[1]?.trim().replace(/"/g, '');
            return context.qualityRequirement === requirement;
        }
        if (condition in currentConditions) {
            return currentConditions[condition];
        }
        return false;
    }
    applyAdaptationRule(strategy, rule) {
        switch (rule.action) {
            case 'skip_step':
                if (rule.parameters.step_id) {
                    const stepIndex = strategy.steps.findIndex(s => s.id === rule.parameters.step_id);
                    if (stepIndex !== -1) {
                        strategy.steps.splice(stepIndex, 1);
                        return {
                            type: 'step_removed',
                            stepId: rule.parameters.step_id,
                            description: `Removed step: ${rule.parameters.step_id}`,
                            impact: 'medium'
                        };
                    }
                }
                break;
            case 'reduce_scope':
                if (rule.parameters.skip_steps) {
                    rule.parameters.skip_steps.forEach((stepId) => {
                        const stepIndex = strategy.steps.findIndex(s => s.id === stepId);
                        if (stepIndex !== -1) {
                            strategy.steps.splice(stepIndex, 1);
                        }
                    });
                    return {
                        type: 'step_removed',
                        description: `Reduced scope by removing steps: ${rule.parameters.skip_steps.join(', ')}`,
                        impact: 'high'
                    };
                }
                break;
        }
        return null;
    }
    reduceStrategyScope(strategy) {
        const optionalSteps = strategy.steps.filter(s => s.priority === 'low' || s.priority === 'medium');
        if (optionalSteps.length > 0) {
            const stepToRemove = optionalSteps[0];
            const stepIndex = strategy.steps.findIndex(s => s.id === stepToRemove.id);
            strategy.steps.splice(stepIndex, 1);
            return {
                type: 'step_removed',
                stepId: stepToRemove.id,
                description: `Removed step to reduce scope: ${stepToRemove.name}`,
                impact: 'medium'
            };
        }
        return null;
    }
    enhanceValidation(strategy) {
        const hasValidation = strategy.steps.some(s => s.type === 'validation');
        if (!hasValidation) {
            const validationStep = {
                id: 'enhanced_validation',
                name: 'Enhanced Source Validation',
                type: 'validation',
                description: 'Additional validation due to low-quality sources',
                inputs: ['sources'],
                outputs: ['validated_sources'],
                dependencies: [],
                estimatedTime: 300000,
                priority: 'high',
                adaptable: true,
                fallbackOptions: []
            };
            strategy.steps.push(validationStep);
            return {
                type: 'step_added',
                stepId: validationStep.id,
                description: 'Added enhanced validation step',
                impact: 'high'
            };
        }
        return null;
    }
    calculateAdaptationConfidence(changes) {
        if (changes.length === 0)
            return 100;
        let confidence = 90;
        changes.forEach(change => {
            if (change.impact === 'high')
                confidence -= 15;
            else if (change.impact === 'medium')
                confidence -= 10;
            else
                confidence -= 5;
        });
        return Math.max(confidence, 50);
    }
    estimateImprovement(changes) {
        let improvement = 0;
        changes.forEach(change => {
            if (change.type === 'step_removed')
                improvement += 10;
            if (change.type === 'step_added')
                improvement += 15;
            if (change.type === 'step_modified')
                improvement += 5;
        });
        return Math.min(improvement, 50);
    }
    startAdaptationLearning() {
        setInterval(() => {
            this.analyzeAdaptationPatterns();
        }, 3600000);
    }
    async analyzeAdaptationPatterns() {
        try {
            for (const [strategyId, adaptations] of this.adaptationHistory) {
                if (adaptations.length > 5) {
                    const successfulAdaptations = adaptations.filter(a => a.expectedImprovement > 10);
                    if (successfulAdaptations.length > 0) {
                        logger_1.logger.info(`Strategy ${strategyId} shows successful adaptation patterns`);
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Adaptation pattern analysis failed:', error);
        }
    }
    getStrategies() {
        return Array.from(this.strategies.values());
    }
    getStrategy(id) {
        return this.strategies.get(id) || null;
    }
    getStrategyPerformance() {
        const strategies = Array.from(this.strategies.values());
        return {
            totalStrategies: strategies.length,
            averageSuccessRate: strategies.reduce((sum, s) => sum + s.performance.successRate, 0) / strategies.length,
            mostUsed: strategies.sort((a, b) => b.performance.usageCount - a.performance.usageCount).slice(0, 3),
            bestPerforming: strategies.sort((a, b) => b.performance.averageQuality - a.performance.averageQuality).slice(0, 3)
        };
    }
    getAdaptationHistory(strategyId) {
        if (strategyId) {
            return this.adaptationHistory.get(strategyId) || [];
        }
        const allAdaptations = [];
        for (const adaptations of this.adaptationHistory.values()) {
            allAdaptations.push(...adaptations);
        }
        return allAdaptations.sort((a, b) => b.confidence - a.confidence);
    }
}
exports.AdaptiveResearchManager = AdaptiveResearchManager;
exports.default = AdaptiveResearchManager;
//# sourceMappingURL=AdaptiveResearchManager.js.map