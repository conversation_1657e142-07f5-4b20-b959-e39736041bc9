import { Session as Neo4jSession } from 'neo4j-driver';
import { Db as MongoDb } from 'mongodb';
import { RedisClientType } from 'redis';
import { WeaviateClient } from 'weaviate-ts-client';
export interface ConnectionPoolStats {
    neo4j: {
        active: number;
        idle: number;
        total: number;
        maxSize: number;
    };
    mongodb: {
        active: number;
        idle: number;
        total: number;
        maxSize: number;
    };
    redis: {
        connected: boolean;
        ready: boolean;
        reconnecting: boolean;
    };
    weaviate: {
        connected: boolean;
        lastPing: Date | null;
    };
}
export interface DatabaseHealth {
    neo4j: {
        status: 'healthy' | 'warning' | 'critical';
        responseTime: number;
        errorRate: number;
        lastCheck: Date;
    };
    mongodb: {
        status: 'healthy' | 'warning' | 'critical';
        responseTime: number;
        errorRate: number;
        lastCheck: Date;
    };
    redis: {
        status: 'healthy' | 'warning' | 'critical';
        responseTime: number;
        errorRate: number;
        lastCheck: Date;
    };
    weaviate: {
        status: 'healthy' | 'warning' | 'critical';
        responseTime: number;
        errorRate: number;
        lastCheck: Date;
    };
}
export declare class DatabaseConnectionManager {
    private neo4jDriver;
    private mongoClient;
    private redisClient;
    private weaviateClient;
    private neo4jSessions;
    private connectionHealth;
    private healthCheckInterval;
    constructor();
    initializeConnections(): Promise<void>;
    private initializeNeo4j;
    private initializeMongoDB;
    private initializeRedis;
    private initializeWeaviate;
    getNeo4jSession(): Promise<Neo4jSession>;
    closeNeo4jSession(session: Neo4jSession): Promise<void>;
    getMongoDatabase(dbName?: string): MongoDb;
    getRedisClient(): RedisClientType;
    getWeaviateClient(): WeaviateClient;
    getConnectionPoolStats(): Promise<ConnectionPoolStats>;
    getDatabaseHealth(): DatabaseHealth;
    performHealthChecks(): Promise<void>;
    shutdown(): Promise<void>;
    private initializeHealthStatus;
    private startHealthMonitoring;
    private checkNeo4jHealth;
    private checkMongoDBHealth;
    private checkRedisHealth;
    private checkWeaviateHealth;
    private updateConnectionHealth;
}
export declare const databaseConnectionManager: DatabaseConnectionManager;
//# sourceMappingURL=DatabaseConnectionManager.d.ts.map