"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitoringService = exports.PerformanceMonitoringService = void 0;
const perf_hooks_1 = require("perf_hooks");
const logger_1 = require("../utils/logger");
const EnhancedCacheService_1 = require("./EnhancedCacheService");
class PerformanceMonitoringService {
    metrics = new Map();
    completedMetrics = [];
    systemMetricsHistory = [];
    alerts = [];
    thresholds = {
        apiResponseTime: 1000,
        memoryUsage: 80,
        cpuUsage: 80,
        errorRate: 5,
        cacheHitRate: 70,
        dbQueryTime: 500
    };
    constructor() {
        this.startSystemMonitoring();
        this.startAlertMonitoring();
    }
    startMetric(name, type, metadata) {
        const id = `${type}_${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const metric = {
            id,
            name,
            type,
            startTime: perf_hooks_1.performance.now(),
            success: false,
            metadata,
            timestamp: new Date()
        };
        this.metrics.set(id, metric);
        return id;
    }
    endMetric(id, success = true, error, additionalMetadata) {
        const metric = this.metrics.get(id);
        if (!metric) {
            logger_1.logger.warn(`Performance metric not found: ${id}`);
            return;
        }
        metric.endTime = perf_hooks_1.performance.now();
        metric.duration = metric.endTime - metric.startTime;
        metric.success = success;
        metric.error = error;
        if (additionalMetadata) {
            metric.metadata = { ...metric.metadata, ...additionalMetadata };
        }
        this.completedMetrics.push(metric);
        this.metrics.delete(id);
        if (this.completedMetrics.length > 10000) {
            this.completedMetrics = this.completedMetrics.slice(-10000);
        }
        this.checkPerformanceThresholds(metric);
        logger_1.logger.debug(`Performance metric completed: ${metric.name} (${metric.duration?.toFixed(2)}ms)`);
    }
    trackApiRequest(endpoint, method) {
        return this.startMetric(`${method} ${endpoint}`, 'api', { endpoint, method });
    }
    trackDatabaseQuery(query, database) {
        return this.startMetric(`${database} query`, 'database', {
            query: query.substring(0, 100),
            database
        });
    }
    trackCacheOperation(operation, key) {
        return this.startMetric(`cache ${operation}`, 'cache', { operation, key });
    }
    trackAIOperation(operation, model) {
        return this.startMetric(`AI ${operation}`, 'ai', { operation, model });
    }
    trackExternalAPI(service, endpoint) {
        return this.startMetric(`${service} API`, 'external', { service, endpoint });
    }
    getPerformanceStats(timeRange = 3600000) {
        const cutoff = Date.now() - timeRange;
        const recentMetrics = this.completedMetrics.filter(m => m.timestamp.getTime() > cutoff);
        const stats = {
            total: recentMetrics.length,
            byType: {},
            averageResponseTime: 0,
            successRate: 0,
            errorRate: 0,
            slowestOperations: [],
            fastestOperations: []
        };
        if (recentMetrics.length === 0)
            return stats;
        const typeGroups = recentMetrics.reduce((acc, metric) => {
            if (!acc[metric.type])
                acc[metric.type] = [];
            acc[metric.type].push(metric);
            return acc;
        }, {});
        Object.entries(typeGroups).forEach(([type, metrics]) => {
            const durations = metrics.filter(m => m.duration).map(m => m.duration);
            const successCount = metrics.filter(m => m.success).length;
            stats.byType[type] = {
                count: metrics.length,
                averageTime: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
                minTime: durations.length > 0 ? Math.min(...durations) : 0,
                maxTime: durations.length > 0 ? Math.max(...durations) : 0,
                successRate: (successCount / metrics.length) * 100,
                errorRate: ((metrics.length - successCount) / metrics.length) * 100
            };
        });
        const allDurations = recentMetrics.filter(m => m.duration).map(m => m.duration);
        const successCount = recentMetrics.filter(m => m.success).length;
        stats.averageResponseTime = allDurations.length > 0 ?
            allDurations.reduce((a, b) => a + b, 0) / allDurations.length : 0;
        stats.successRate = (successCount / recentMetrics.length) * 100;
        stats.errorRate = ((recentMetrics.length - successCount) / recentMetrics.length) * 100;
        const sortedByDuration = recentMetrics
            .filter(m => m.duration)
            .sort((a, b) => (b.duration || 0) - (a.duration || 0));
        stats.slowestOperations = sortedByDuration.slice(0, 10).map(m => ({
            name: m.name,
            type: m.type,
            duration: m.duration,
            timestamp: m.timestamp
        }));
        stats.fastestOperations = sortedByDuration.slice(-10).reverse().map(m => ({
            name: m.name,
            type: m.type,
            duration: m.duration,
            timestamp: m.timestamp
        }));
        return stats;
    }
    async getCurrentSystemMetrics() {
        const process = await Promise.resolve().then(() => __importStar(require('process')));
        const os = await Promise.resolve().then(() => __importStar(require('os')));
        const memUsage = process.memoryUsage();
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        const cacheStats = EnhancedCacheService_1.enhancedCacheService.getCacheStats();
        const perfStats = this.getPerformanceStats(60000);
        return {
            cpu: {
                usage: await this.getCPUUsage(),
                loadAverage: os.loadavg()
            },
            memory: {
                used: usedMem,
                free: freeMem,
                total: totalMem,
                percentage: (usedMem / totalMem) * 100
            },
            cache: {
                hitRate: cacheStats.hitRate,
                totalKeys: cacheStats.totalKeys,
                memoryUsage: cacheStats.memoryUsage
            },
            api: {
                requestsPerMinute: this.getRequestsPerMinute(),
                averageResponseTime: perfStats.averageResponseTime,
                errorRate: perfStats.errorRate
            },
            database: {
                activeConnections: await this.getDatabaseConnections(),
                queryTime: perfStats.byType.database?.averageTime || 0,
                errorRate: perfStats.byType.database?.errorRate || 0
            },
            timestamp: new Date()
        };
    }
    getAlerts(resolved = false) {
        return this.alerts.filter(alert => alert.resolved === resolved);
    }
    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            logger_1.logger.info(`Performance alert resolved: ${alert.message}`);
        }
    }
    getPerformanceReport(timeRange = 3600000) {
        const stats = this.getPerformanceStats(timeRange);
        const recentAlerts = this.alerts.filter(a => a.timestamp.getTime() > Date.now() - timeRange && !a.resolved);
        return {
            summary: {
                totalOperations: stats.total,
                averageResponseTime: stats.averageResponseTime,
                successRate: stats.successRate,
                errorRate: stats.errorRate,
                activeAlerts: recentAlerts.length
            },
            byType: stats.byType,
            slowestOperations: stats.slowestOperations,
            fastestOperations: stats.fastestOperations,
            alerts: recentAlerts,
            systemMetrics: this.systemMetricsHistory.slice(-60),
            recommendations: this.generateRecommendations(stats)
        };
    }
    async getCPUUsage() {
        const os = await Promise.resolve().then(() => __importStar(require('os')));
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        cpus.forEach(cpu => {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });
        return 100 - (totalIdle / totalTick) * 100;
    }
    getRequestsPerMinute() {
        const oneMinuteAgo = Date.now() - 60000;
        const recentApiMetrics = this.completedMetrics.filter(m => m.type === 'api' && m.timestamp.getTime() > oneMinuteAgo);
        return recentApiMetrics.length;
    }
    async getDatabaseConnections() {
        return Math.floor(Math.random() * 20) + 5;
    }
    checkPerformanceThresholds(metric) {
        if (!metric.duration)
            return;
        if (metric.type === 'api' && metric.duration > this.thresholds.apiResponseTime) {
            this.createAlert('warning', 'apiResponseTime', this.thresholds.apiResponseTime, metric.duration, `Slow API response: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
        }
        if (metric.type === 'database' && metric.duration > this.thresholds.dbQueryTime) {
            this.createAlert('warning', 'dbQueryTime', this.thresholds.dbQueryTime, metric.duration, `Slow database query: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
        }
    }
    createAlert(type, metric, threshold, currentValue, message) {
        const alert = {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type,
            metric,
            threshold,
            currentValue,
            message,
            timestamp: new Date(),
            resolved: false
        };
        this.alerts.push(alert);
        if (this.alerts.length > 1000) {
            this.alerts = this.alerts.slice(-1000);
        }
        logger_1.logger.warn(`Performance alert: ${message}`);
    }
    generateRecommendations(stats) {
        const recommendations = [];
        if (stats.errorRate > 5) {
            recommendations.push('High error rate detected. Review error logs and implement better error handling.');
        }
        if (stats.averageResponseTime > 1000) {
            recommendations.push('Slow average response time. Consider implementing caching or optimizing database queries.');
        }
        if (stats.byType.database?.averageTime > 500) {
            recommendations.push('Database queries are slow. Consider adding indexes or optimizing query structure.');
        }
        if (stats.byType.cache?.errorRate > 2) {
            recommendations.push('Cache errors detected. Check Redis connection and configuration.');
        }
        return recommendations;
    }
    startSystemMonitoring() {
        setInterval(async () => {
            try {
                const metrics = await this.getCurrentSystemMetrics();
                this.systemMetricsHistory.push(metrics);
                if (this.systemMetricsHistory.length > 1440) {
                    this.systemMetricsHistory = this.systemMetricsHistory.slice(-1440);
                }
                this.checkSystemThresholds(metrics);
            }
            catch (error) {
                logger_1.logger.error('Failed to collect system metrics:', error);
            }
        }, 60000);
    }
    startAlertMonitoring() {
        setInterval(() => {
            const currentTime = Date.now();
            const fiveMinutesAgo = currentTime - 300000;
            this.alerts.forEach(alert => {
                if (!alert.resolved && alert.timestamp.getTime() < fiveMinutesAgo) {
                    const recentMetrics = this.completedMetrics.filter(m => m.timestamp.getTime() > fiveMinutesAgo);
                    let shouldResolve = false;
                    if (alert.metric === 'apiResponseTime') {
                        const recentApiMetrics = recentMetrics.filter(m => m.type === 'api');
                        const avgTime = recentApiMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / recentApiMetrics.length;
                        shouldResolve = avgTime < alert.threshold;
                    }
                    if (shouldResolve) {
                        alert.resolved = true;
                        logger_1.logger.info(`Auto-resolved alert: ${alert.message}`);
                    }
                }
            });
        }, 300000);
    }
    checkSystemThresholds(metrics) {
        if (metrics.memory.percentage > this.thresholds.memoryUsage) {
            this.createAlert('critical', 'memoryUsage', this.thresholds.memoryUsage, metrics.memory.percentage, `High memory usage: ${metrics.memory.percentage.toFixed(1)}%`);
        }
        if (metrics.cpu.usage > this.thresholds.cpuUsage) {
            this.createAlert('warning', 'cpuUsage', this.thresholds.cpuUsage, metrics.cpu.usage, `High CPU usage: ${metrics.cpu.usage.toFixed(1)}%`);
        }
        if (metrics.cache.hitRate < this.thresholds.cacheHitRate) {
            this.createAlert('warning', 'cacheHitRate', this.thresholds.cacheHitRate, metrics.cache.hitRate, `Low cache hit rate: ${metrics.cache.hitRate.toFixed(1)}%`);
        }
    }
}
exports.PerformanceMonitoringService = PerformanceMonitoringService;
exports.performanceMonitoringService = new PerformanceMonitoringService();
//# sourceMappingURL=PerformanceMonitoringService.js.map