export interface PerformanceMetric {
    id: string;
    name: string;
    type: 'api' | 'database' | 'cache' | 'ai' | 'external';
    startTime: number;
    endTime?: number;
    duration?: number;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
}
export interface SystemMetrics {
    cpu: {
        usage: number;
        loadAverage: number[];
    };
    memory: {
        used: number;
        free: number;
        total: number;
        percentage: number;
    };
    cache: {
        hitRate: number;
        totalKeys: number;
        memoryUsage: number;
    };
    api: {
        requestsPerMinute: number;
        averageResponseTime: number;
        errorRate: number;
    };
    database: {
        activeConnections: number;
        queryTime: number;
        errorRate: number;
    };
    timestamp: Date;
}
export interface PerformanceAlert {
    id: string;
    type: 'warning' | 'critical';
    metric: string;
    threshold: number;
    currentValue: number;
    message: string;
    timestamp: Date;
    resolved?: boolean;
}
export declare class PerformanceMonitoringService {
    private metrics;
    private completedMetrics;
    private systemMetricsHistory;
    private alerts;
    private thresholds;
    constructor();
    startMetric(name: string, type: PerformanceMetric['type'], metadata?: Record<string, any>): string;
    endMetric(id: string, success?: boolean, error?: string, additionalMetadata?: Record<string, any>): void;
    trackApiRequest(endpoint: string, method: string): string;
    trackDatabaseQuery(query: string, database: string): string;
    trackCacheOperation(operation: string, key: string): string;
    trackAIOperation(operation: string, model?: string): string;
    trackExternalAPI(service: string, endpoint: string): string;
    getPerformanceStats(timeRange?: number): any;
    getCurrentSystemMetrics(): Promise<SystemMetrics>;
    getAlerts(resolved?: boolean): PerformanceAlert[];
    resolveAlert(alertId: string): void;
    getPerformanceReport(timeRange?: number): any;
    private getCPUUsage;
    private getRequestsPerMinute;
    private getDatabaseConnections;
    private checkPerformanceThresholds;
    private createAlert;
    private generateRecommendations;
    private startSystemMonitoring;
    private startAlertMonitoring;
    private checkSystemThresholds;
}
export declare const performanceMonitoringService: PerformanceMonitoringService;
//# sourceMappingURL=PerformanceMonitoringService.d.ts.map