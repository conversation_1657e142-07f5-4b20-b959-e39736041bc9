"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedWebSocketService = void 0;
const socket_io_1 = require("socket.io");
const logger_1 = require("../utils/logger");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
class EnhancedWebSocketService {
    io;
    connectedUsers = new Map();
    userSockets = new Map();
    socketUsers = new Map();
    researchRooms = new Map();
    roomMessages = new Map();
    activeResearchSessions = new Map();
    notifications = new Map();
    constructor(server) {
        this.io = new socket_io_1.Server(server, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:5175",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling'],
            pingTimeout: 60000,
            pingInterval: 25000
        });
        this.initializeEventHandlers();
        this.startCleanupTasks();
        logger_1.logger.info('🚀 Enhanced WebSocket Service initialized with multi-room support');
    }
    initializeEventHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info(`🔌 New WebSocket connection: ${socket.id}`);
            socket.on('authenticate', (data) => {
                this.handleUserAuthentication(socket, data);
            });
            socket.on('join_room', (data) => {
                this.handleJoinRoom(socket, data);
            });
            socket.on('leave_room', (data) => {
                this.handleLeaveRoom(socket, data);
            });
            socket.on('create_room', (data) => {
                this.handleCreateRoom(socket, data);
            });
            socket.on('start_research', (data) => {
                this.handleStartResearch(socket, data);
            });
            socket.on('research_progress', (data) => {
                this.handleResearchProgress(socket, data);
            });
            socket.on('send_message', (data) => {
                this.handleSendMessage(socket, data);
            });
            socket.on('add_reaction', (data) => {
                this.handleAddReaction(socket, data);
            });
            socket.on('update_status', (data) => {
                this.handleUpdateStatus(socket, data);
            });
            socket.on('typing_start', (data) => {
                this.handleTypingStart(socket, data);
            });
            socket.on('typing_stop', (data) => {
                this.handleTypingStop(socket, data);
            });
            socket.on('mark_notification_read', (data) => {
                this.handleMarkNotificationRead(socket, data);
            });
            socket.on('disconnect', (reason) => {
                this.handleDisconnect(socket, reason);
            });
            socket.on('error', (error) => {
                logger_1.logger.error('WebSocket error:', error, { socketId: socket.id });
            });
        });
    }
    handleUserAuthentication(socket, data) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackApiRequest('websocket_auth', 'POST');
        try {
            const { userId, username, avatar } = data;
            const userPresence = {
                userId,
                username,
                avatar,
                status: 'online',
                lastSeen: new Date()
            };
            this.connectedUsers.set(userId, userPresence);
            if (!this.userSockets.has(userId)) {
                this.userSockets.set(userId, new Set());
            }
            this.userSockets.get(userId).add(socket.id);
            this.socketUsers.set(socket.id, userId);
            socket.join(`user:${userId}`);
            socket.emit('authenticated', {
                success: true,
                user: userPresence,
                rooms: this.getUserRooms(userId),
                notifications: this.getUserNotifications(userId)
            });
            this.broadcastUserPresence(userId, 'online');
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info(`✅ User authenticated: ${username} (${userId})`);
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Auth failed');
            socket.emit('authentication_error', { error: 'Authentication failed' });
            logger_1.logger.error('Authentication error:', error);
        }
    }
    handleJoinRoom(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId) {
            socket.emit('error', { message: 'Not authenticated' });
            return;
        }
        const room = this.researchRooms.get(data.roomId);
        if (!room) {
            socket.emit('error', { message: 'Room not found' });
            return;
        }
        if (room.maxParticipants && room.participants.size >= room.maxParticipants) {
            socket.emit('error', { message: 'Room is full' });
            return;
        }
        socket.join(data.roomId);
        room.participants.add(userId);
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence) {
            userPresence.currentRoom = data.roomId;
            this.connectedUsers.set(userId, userPresence);
        }
        socket.emit('room_joined', {
            room: this.serializeRoom(room),
            messages: this.roomMessages.get(data.roomId)?.slice(-50) || [],
            participants: this.getRoomParticipants(data.roomId)
        });
        socket.to(data.roomId).emit('user_joined_room', {
            userId,
            username: userPresence?.username,
            timestamp: new Date()
        });
        logger_1.logger.info(`👥 User ${userId} joined room ${data.roomId}`);
    }
    handleLeaveRoom(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const room = this.researchRooms.get(data.roomId);
        if (room) {
            room.participants.delete(userId);
            socket.leave(data.roomId);
            const userPresence = this.connectedUsers.get(userId);
            if (userPresence && userPresence.currentRoom === data.roomId) {
                userPresence.currentRoom = undefined;
                this.connectedUsers.set(userId, userPresence);
            }
            socket.to(data.roomId).emit('user_left_room', {
                userId,
                username: userPresence?.username,
                timestamp: new Date()
            });
            logger_1.logger.info(`👋 User ${userId} left room ${data.roomId}`);
        }
    }
    handleCreateRoom(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId) {
            socket.emit('error', { message: 'Not authenticated' });
            return;
        }
        const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const room = {
            id: roomId,
            name: data.name || 'New Research Room',
            description: data.description,
            type: data.type || 'research',
            createdBy: userId,
            createdAt: new Date(),
            participants: new Set([userId]),
            isPrivate: data.isPrivate || false,
            maxParticipants: data.maxParticipants,
            settings: {
                allowComments: true,
                allowEditing: true,
                autoSave: true,
                notifications: true,
                ...data.settings
            }
        };
        this.researchRooms.set(roomId, room);
        this.roomMessages.set(roomId, []);
        socket.join(roomId);
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence) {
            userPresence.currentRoom = roomId;
            this.connectedUsers.set(userId, userPresence);
        }
        socket.emit('room_created', {
            room: this.serializeRoom(room)
        });
        logger_1.logger.info(`🏠 Room created: ${room.name} (${roomId}) by ${userId}`);
    }
    handleStartResearch(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const sessionId = `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const researchProgress = {
            sessionId,
            roomId: data.roomId,
            userId,
            type: data.type,
            status: 'started',
            progress: 0,
            currentStep: 'Initializing research...',
            totalSteps: 5,
            timestamp: new Date()
        };
        this.activeResearchSessions.set(sessionId, researchProgress);
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence) {
            userPresence.activeResearch = sessionId;
            this.connectedUsers.set(userId, userPresence);
        }
        socket.emit('research_started', {
            sessionId,
            progress: researchProgress,
            query: data.query
        });
        if (data.roomId) {
            this.io.to(data.roomId).emit('research_started', {
                sessionId,
                progress: researchProgress,
                query: data.query
            });
        }
        logger_1.logger.info(`🔬 Research started: ${sessionId} by ${userId}`);
    }
    handleResearchProgress(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId || !data.sessionId)
            return;
        const session = this.activeResearchSessions.get(data.sessionId);
        if (!session || session.userId !== userId)
            return;
        Object.assign(session, data, { timestamp: new Date() });
        this.activeResearchSessions.set(data.sessionId, session);
        socket.emit('research_progress_update', {
            sessionId: data.sessionId,
            progress: session
        });
        if (session.roomId) {
            this.io.to(session.roomId).emit('research_progress_update', {
                sessionId: data.sessionId,
                progress: session
            });
        }
        if (session.status === 'completed' || session.status === 'error') {
            setTimeout(() => {
                this.activeResearchSessions.delete(data.sessionId);
                const userPresence = this.connectedUsers.get(userId);
                if (userPresence && userPresence.activeResearch === data.sessionId) {
                    userPresence.activeResearch = undefined;
                    this.connectedUsers.set(userId, userPresence);
                }
            }, 60000);
        }
    }
    handleSendMessage(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const room = this.researchRooms.get(data.roomId);
        if (!room || !room.participants.has(userId)) {
            socket.emit('error', { message: 'Not in room or room not found' });
            return;
        }
        const userPresence = this.connectedUsers.get(userId);
        const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const message = {
            id: messageId,
            roomId: data.roomId,
            userId,
            username: userPresence?.username || 'Unknown',
            type: data.type || 'text',
            content: data.content,
            replyTo: data.replyTo,
            reactions: new Map(),
            timestamp: new Date()
        };
        if (!this.roomMessages.has(data.roomId)) {
            this.roomMessages.set(data.roomId, []);
        }
        const messages = this.roomMessages.get(data.roomId);
        messages.push(message);
        if (messages.length > 1000) {
            this.roomMessages.set(data.roomId, messages.slice(-1000));
        }
        this.io.to(data.roomId).emit('new_message', message);
        logger_1.logger.debug(`💬 Message sent in room ${data.roomId} by ${userId}`);
    }
    handleAddReaction(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        for (const [roomId, messages] of this.roomMessages) {
            const message = messages.find(m => m.id === data.messageId);
            if (message) {
                if (!message.reactions.has(data.emoji)) {
                    message.reactions.set(data.emoji, []);
                }
                const users = message.reactions.get(data.emoji);
                if (!users.includes(userId)) {
                    users.push(userId);
                    this.io.to(roomId).emit('reaction_added', {
                        messageId: data.messageId,
                        emoji: data.emoji,
                        userId,
                        totalReactions: message.reactions
                    });
                }
                break;
            }
        }
    }
    handleUpdateStatus(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence) {
            userPresence.status = data.status;
            userPresence.lastSeen = new Date();
            this.connectedUsers.set(userId, userPresence);
            this.broadcastUserPresence(userId, data.status);
        }
    }
    handleTypingStart(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        socket.to(data.roomId).emit('user_typing', {
            userId,
            username: this.connectedUsers.get(userId)?.username,
            isTyping: true
        });
    }
    handleTypingStop(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        socket.to(data.roomId).emit('user_typing', {
            userId,
            username: this.connectedUsers.get(userId)?.username,
            isTyping: false
        });
    }
    handleMarkNotificationRead(socket, data) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const userNotifications = this.notifications.get(userId) || [];
        const notificationIndex = userNotifications.findIndex(n => n.id === data.notificationId);
        if (notificationIndex !== -1) {
            userNotifications.splice(notificationIndex, 1);
            this.notifications.set(userId, userNotifications);
            socket.emit('notification_read', { notificationId: data.notificationId });
        }
    }
    handleDisconnect(socket, reason) {
        const userId = this.socketUsers.get(socket.id);
        if (!userId)
            return;
        const userSocketSet = this.userSockets.get(userId);
        if (userSocketSet) {
            userSocketSet.delete(socket.id);
            if (userSocketSet.size === 0) {
                this.userSockets.delete(userId);
                const userPresence = this.connectedUsers.get(userId);
                if (userPresence) {
                    userPresence.status = 'offline';
                    userPresence.lastSeen = new Date();
                    this.connectedUsers.set(userId, userPresence);
                    this.broadcastUserPresence(userId, 'offline');
                }
            }
        }
        this.socketUsers.delete(socket.id);
        logger_1.logger.info(`🔌 User disconnected: ${socket.id} (${userId}) - ${reason}`);
    }
    serializeRoom(room) {
        return {
            ...room,
            participants: Array.from(room.participants)
        };
    }
    getUserRooms(userId) {
        return Array.from(this.researchRooms.values())
            .filter(room => room.participants.has(userId))
            .map(room => this.serializeRoom(room));
    }
    getRoomParticipants(roomId) {
        const room = this.researchRooms.get(roomId);
        if (!room)
            return [];
        return Array.from(room.participants)
            .map(userId => this.connectedUsers.get(userId))
            .filter(Boolean);
    }
    getUserNotifications(userId) {
        return this.notifications.get(userId) || [];
    }
    broadcastUserPresence(userId, status) {
        const userPresence = this.connectedUsers.get(userId);
        if (!userPresence)
            return;
        for (const room of this.researchRooms.values()) {
            if (room.participants.has(userId)) {
                this.io.to(room.id).emit('user_presence_update', {
                    userId,
                    username: userPresence.username,
                    status,
                    lastSeen: userPresence.lastSeen
                });
            }
        }
    }
    startCleanupTasks() {
        setInterval(() => {
            const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            for (const [roomId, messages] of this.roomMessages) {
                const filteredMessages = messages.filter(m => m.timestamp > oneWeekAgo);
                this.roomMessages.set(roomId, filteredMessages);
            }
        }, 60 * 60 * 1000);
        setInterval(() => {
            const now = new Date();
            for (const [userId, notifications] of this.notifications) {
                const validNotifications = notifications.filter(n => !n.expiresAt || n.expiresAt > now);
                this.notifications.set(userId, validNotifications);
            }
        }, 10 * 60 * 1000);
    }
    sendNotification(notification) {
        const fullNotification = {
            ...notification,
            id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date()
        };
        if (notification.userId) {
            if (!this.notifications.has(notification.userId)) {
                this.notifications.set(notification.userId, []);
            }
            this.notifications.get(notification.userId).push(fullNotification);
            this.io.to(`user:${notification.userId}`).emit('new_notification', fullNotification);
        }
        else if (notification.roomId) {
            this.io.to(notification.roomId).emit('new_notification', fullNotification);
        }
        else {
            this.io.emit('new_notification', fullNotification);
        }
    }
    updateResearchProgress(sessionId, progress) {
        const session = this.activeResearchSessions.get(sessionId);
        if (!session)
            return;
        Object.assign(session, progress, { timestamp: new Date() });
        this.activeResearchSessions.set(sessionId, session);
        const userSockets = this.userSockets.get(session.userId);
        if (userSockets) {
            userSockets.forEach(socketId => {
                this.io.to(socketId).emit('research_progress_update', {
                    sessionId,
                    progress: session
                });
            });
        }
        if (session.roomId) {
            this.io.to(session.roomId).emit('research_progress_update', {
                sessionId,
                progress: session
            });
        }
    }
    getConnectedUsers() {
        return Array.from(this.connectedUsers.values());
    }
    getRooms() {
        return Array.from(this.researchRooms.values()).map(room => this.serializeRoom(room));
    }
    getActiveResearchSessions() {
        return Array.from(this.activeResearchSessions.values());
    }
}
exports.EnhancedWebSocketService = EnhancedWebSocketService;
exports.default = EnhancedWebSocketService;
//# sourceMappingURL=EnhancedWebSocketService.js.map