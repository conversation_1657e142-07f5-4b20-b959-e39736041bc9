"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler_1 = require("@/middleware/notFoundHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const EnhancedCacheService_1 = require("@/services/EnhancedCacheService");
const DatabaseConnectionManager_1 = require("@/services/DatabaseConnectionManager");
const performanceMiddleware_1 = require("@/middleware/performanceMiddleware");
const graph_1 = require("@/routes/graph");
const rag_1 = require("@/routes/rag");
const ai_1 = require("@/routes/ai");
const health_1 = require("@/routes/health");
const upload_1 = require("@/routes/upload");
const research_1 = __importDefault(require("@/routes/research"));
const researchAI_1 = __importDefault(require("@/routes/researchAI"));
const agui_1 = __importStar(require("@/routes/agui"));
const crawl_1 = __importDefault(require("@/routes/crawl"));
const userProfile_1 = __importDefault(require("@/routes/userProfile"));
const crewai_1 = __importDefault(require("@/routes/crewai"));
const enhancedResearch_1 = __importDefault(require("@/routes/enhancedResearch"));
const oracle_1 = __importDefault(require("@/routes/oracle"));
const monitoring_1 = __importDefault(require("@/routes/monitoring"));
const performance_1 = __importDefault(require("@/routes/performance"));
const realtime_1 = __importDefault(require("@/routes/realtime"));
const enhanced_ai_1 = __importDefault(require("@/routes/enhanced-ai"));
const medical_research_1 = __importDefault(require("@/routes/medical-research"));
const GraphWebSocketService_1 = require("@/services/GraphWebSocketService");
const EnhancedWebSocketService_1 = __importDefault(require("@/services/EnhancedWebSocketService"));
const RealTimeResearchOrchestrator_1 = __importDefault(require("@/services/RealTimeResearchOrchestrator"));
const CollaborativeWorkspaceManager_1 = __importDefault(require("@/services/CollaborativeWorkspaceManager"));
dotenv_1.default.config();
class Application {
    app;
    server;
    port;
    graphWebSocketService;
    enhancedWebSocketService;
    realTimeOrchestrator;
    collaborativeWorkspaceManager;
    constructor() {
        this.app = (0, express_1.default)();
        this.port = environment_1.config.port;
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: environment_1.config.corsOrigins,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        this.app.use('/api/', (0, performanceMiddleware_1.rateLimitWithPerformance)(15 * 60 * 1000, environment_1.config.isDevelopment ? 1000 : 100));
        this.app.use(express_1.default.json({ limit: '50mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
        this.app.use((0, performanceMiddleware_1.compressionWithPerformance)());
        this.app.use(performanceMiddleware_1.performanceMiddleware);
        this.app.use(performanceMiddleware_1.memoryMonitoringMiddleware);
        if (environment_1.config.isDevelopment) {
            this.app.use((0, morgan_1.default)('dev'));
        }
        else {
            this.app.use((0, morgan_1.default)('combined', { stream: { write: (message) => logger_1.logger.info(message.trim()) } }));
        }
        this.app.use(requestLogger_1.requestLogger);
        this.app.use(performanceMiddleware_1.healthCheckMiddleware);
    }
    initializeRoutes() {
        this.app.use('/api/health', health_1.healthRoutes);
        this.app.use('/api/graph', graph_1.graphRoutes);
        this.app.use('/api/rag', rag_1.ragRoutes);
        this.app.use('/api/ai', ai_1.aiRoutes);
        this.app.use('/api/upload', upload_1.uploadRoutes);
        this.app.use('/api/research', research_1.default);
        this.app.use('/api/research-ai', researchAI_1.default);
        this.app.use('/api/enhanced-research', enhancedResearch_1.default);
        this.app.use('/api/oracle', oracle_1.default);
        this.app.use('/api/monitoring', monitoring_1.default);
        this.app.use('/api/performance', performance_1.default);
        this.app.use('/api/realtime', realtime_1.default);
        this.app.use('/api/enhanced-ai', enhanced_ai_1.default);
        this.app.use('/api/medical-research', medical_research_1.default);
        this.app.use('/api/crawl', crawl_1.default);
        this.app.use('/api/profile', userProfile_1.default);
        this.app.use('/api/crewai', crewai_1.default);
        this.app.use('/agui', agui_1.default);
        this.app.get('/api', (_req, res) => {
            res.json({
                name: 'Suplementor Knowledge Graph API',
                version: '1.0.0',
                description: 'API for managing supplement knowledge graphs with AI integration',
                endpoints: {
                    health: '/api/health',
                    graph: '/api/graph',
                    rag: '/api/rag',
                    ai: '/api/ai',
                    upload: '/api/upload',
                    research: '/api/research',
                    researchAI: '/api/research-ai',
                    enhancedResearch: '/api/enhanced-research',
                    oracle: '/api/oracle',
                    monitoring: '/api/monitoring',
                    performance: '/api/performance',
                    realtime: '/api/realtime',
                    enhancedAi: '/api/enhanced-ai',
                    medicalResearch: '/api/medical-research',
                    profile: '/api/profile',
                    crewai: '/api/crewai',
                    agui: '/agui',
                },
                websockets: {
                    agui: '/agui/ws',
                },
                documentation: '/api/docs',
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(notFoundHandler_1.notFoundHandler);
        this.app.use(performanceMiddleware_1.errorTrackingMiddleware);
        this.app.use(errorHandler_1.errorHandler);
    }
    async initializeDatabases() {
        try {
            logger_1.logger.info('🚀 Initializing enhanced database connections with pooling...');
            await DatabaseConnectionManager_1.databaseConnectionManager.initializeConnections();
            logger_1.logger.info('🔄 Initializing enhanced cache service...');
            logger_1.logger.info('📊 Starting performance monitoring...');
            await this.warmUpCache();
            logger_1.logger.info('✅ All enhanced database services initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('❌ Enhanced database initialization failed:', error);
            throw error;
        }
    }
    async start() {
        try {
            await this.initializeDatabases();
            this.server = (0, http_1.createServer)(this.app);
            (0, agui_1.initializeAGUIWebSocket)(this.server);
            this.graphWebSocketService = new GraphWebSocketService_1.GraphWebSocketService(this.server);
            logger_1.logger.info('🔌 Graph WebSocket service initialized');
            this.enhancedWebSocketService = new EnhancedWebSocketService_1.default(this.server);
            logger_1.logger.info('🚀 Enhanced WebSocket service initialized');
            this.collaborativeWorkspaceManager = new CollaborativeWorkspaceManager_1.default();
            logger_1.logger.info('🤝 Collaborative Workspace Manager initialized');
            const { MCPResearchOrchestrator } = await Promise.resolve().then(() => __importStar(require('@/services/MCPResearchOrchestrator')));
            const ResearchAIServiceModule = await Promise.resolve().then(() => __importStar(require('@/services/ResearchAIService')));
            const mcpOrchestrator = new MCPResearchOrchestrator();
            const aiService = new ResearchAIServiceModule.default();
            this.realTimeOrchestrator = new RealTimeResearchOrchestrator_1.default(mcpOrchestrator, aiService);
            logger_1.logger.info('🤖 Real-Time Research Orchestrator initialized');
            this.server.listen(this.port, () => {
                logger_1.logger.info(`🚀 Server running on port ${this.port}`);
                logger_1.logger.info(`📊 Environment: ${environment_1.config.nodeEnv}`);
                logger_1.logger.info(`🔗 API URL: http://localhost:${this.port}/api`);
                logger_1.logger.info(`🔌 AG-UI WebSocket: ws://localhost:${this.port}/agui/ws`);
                logger_1.logger.info(`💚 Health check: http://localhost:${this.port}/health`);
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }
    async warmUpCache() {
        try {
            logger_1.logger.info('🔥 Warming up cache with frequently accessed data...');
            const warmupData = [
                {
                    key: 'system:config',
                    data: { version: '1.0.0', features: ['ai', 'research', 'graph'] },
                    options: { ttl: 3600, priority: 'high', tags: ['system'] }
                },
                {
                    key: 'research:popular_supplements',
                    data: ['Vitamin D', 'Magnesium', 'Omega-3', 'Vitamin B12', 'Zinc'],
                    options: { ttl: 1800, priority: 'medium', tags: ['research'] }
                }
            ];
            await EnhancedCacheService_1.enhancedCacheService.warmCache(warmupData);
            logger_1.logger.info('✅ Cache warmup completed');
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Cache warmup failed:', error);
        }
    }
    setupGracefulShutdown() {
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`📴 Received ${signal}. Starting enhanced graceful shutdown...`);
            if (this.server) {
                this.server.close(async () => {
                    logger_1.logger.info('🔌 HTTP server closed');
                    try {
                        await DatabaseConnectionManager_1.databaseConnectionManager.shutdown();
                        logger_1.logger.info('🔌 Enhanced database connections closed');
                        await EnhancedCacheService_1.enhancedCacheService.shutdown();
                        logger_1.logger.info('🔌 Enhanced cache service closed');
                        if (this.graphWebSocketService) {
                            logger_1.logger.info('🔌 Graph WebSocket service closed');
                        }
                        if (this.enhancedWebSocketService) {
                            logger_1.logger.info('🔌 Enhanced WebSocket service closed');
                        }
                        if (this.realTimeOrchestrator) {
                            this.realTimeOrchestrator.shutdown();
                            logger_1.logger.info('🤖 Real-Time Research Orchestrator closed');
                        }
                        if (this.collaborativeWorkspaceManager) {
                            logger_1.logger.info('🤝 Collaborative Workspace Manager closed');
                        }
                        logger_1.logger.info('✅ Enhanced graceful shutdown completed');
                        process.exit(0);
                    }
                    catch (error) {
                        logger_1.logger.error('❌ Error during enhanced graceful shutdown:', error);
                        process.exit(1);
                    }
                });
            }
            else {
                process.exit(0);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2'));
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('❌ Uncaught Exception:', error);
            gracefulShutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
            gracefulShutdown('unhandledRejection');
        });
    }
}
const app = new Application();
app.start().catch((error) => {
    logger_1.logger.error('❌ Failed to start application:', error);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=index.js.map