import express from 'express';
declare class Application {
    app: express.Application;
    server: any;
    private port;
    private graphWebSocketService;
    private enhancedWebSocketService?;
    private realTimeOrchestrator?;
    private collaborativeWorkspaceManager?;
    constructor();
    private initializeMiddleware;
    private initializeRoutes;
    private initializeErrorHandling;
    private initializeDatabases;
    start(): Promise<void>;
    private warmUpCache;
    private setupGracefulShutdown;
}
declare const app: Application;
export default app;
//# sourceMappingURL=index.d.ts.map