{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:35:13:3513"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:36:00:360"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:37:15:3715"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:204:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:40:4840"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:204:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:40:4840"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:54:4854"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:54:4854"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:49:08:498"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:49:08:498"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3069","port":3069,"stack":"Error: listen EADDRINUSE: address already in use :::3069\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:00:34:034"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:01:55:155"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:11:211"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:24:224"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:34:234"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:55:255"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:04:42:442"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:209:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:13:59:1359"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:09:259"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:13:2513"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:43:2543"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:08:268"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:19:2619"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:31:2631"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:41:2641"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:51:2651"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:28:06:286"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:28:19:2819"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:32:59:3259"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.840Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"0b3db91d-d103-4fb2-b954-404f1bb5f406","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.840Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.842Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f289c2e5-6b97-448a-aa79-d73ed93131f1","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.842Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.838Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3bc4713f-d364-4827-bfa6-1ebebf169834","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.838Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.738Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2118b1e7-11c6-4894-aa36-c3a711cbd931","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.738Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.753Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"9f4926a4-de5b-4be8-aff4-439c4ea746e8","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.753Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.764Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3a661dea-d9af-4707-b2c6-99ee6d317f8f","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.764Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:24.851Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f50caeef-e702-4a41-8973-cd345e7c0e33","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:24.851Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.592Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"92debb8a-5b82-46ab-b570-80752273facc","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.592Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.594Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"dd1cf63e-a39e-4403-b6c7-e962e0a99183","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.594Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.597Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"38496b3b-9a09-4442-8a54-93766972cf1c","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.597Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.618Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"6f0284df-c65e-4500-844c-b86d330287ae","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.618Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.624Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"768e280b-dd88-4440-94d3-a6c1d5811ea5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.624Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.634Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"684e4c78-868c-46bd-8e0d-d8a0d27109b4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.634Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.657Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"62a6d995-18c4-4fe3-9fc9-aaa517029427","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.657Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.663Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"31cc490c-1be8-4b19-8801-7df5fbd793a5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.663Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.685Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2280ae95-6744-48e6-b679-d489f119fdfd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.685Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.713Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"8d82b8b0-ee0d-4492-89ea-0a3095be85bd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.713Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.695Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"089a3279-7e0d-40bf-bdd4-0cc7320b10ad","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.695Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.741Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"5f9b2047-36d7-4e08-8ad6-b9adba41e739","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.741Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:34.611Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"d57767e3-25f0-4da8-bc3a-4c2eefc5aab4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:34.611Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934","userId":"research-demo-user"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:01:36:136"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T04:05:43.838Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f80f944a-fd70-43cc-8240-e6fc5b3c86e2","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T04:05:43.838Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T04:05:43.878Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"65ac7177-7d40-4bcc-9657-6bff5da02a94","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T04:05:43.878Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T04:05:43.906Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"85f33d9d-bfff-4942-a02d-e068601358b2","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T04:05:43.906Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 06:05:43:543","userId":"cosmic-user"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:228:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:08:54:854"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:220:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:09:13:913"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:223:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:09:23:923"}
{"level":"error","message":"❌ Enhanced database initialization failed: this.warmUpCache is not a function","stack":"TypeError: this.warmUpCache is not a function\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:193:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:7)","timestamp":"2025-06-05 06:09:36:936"}
{"level":"error","message":"❌ Enhanced database initialization failed: this.warmUpCache is not a function","stack":"TypeError: this.warmUpCache is not a function\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:193:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:7)","timestamp":"2025-06-05 06:09:36:936"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:216:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:09:57:957"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:10:28:1028"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:217:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:10:45:1045"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:218:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:10:57:1057"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:219:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 06:11:07:117"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:11:39:1139"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:11:56:1156"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:12:10:1210"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:12:27:1227"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:12:41:1241"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:12:59:1259"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:13:12:1312"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:13:30:1330"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:13:43:1343"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:14:01:141"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:14:14:1414"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:14:32:1432"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:14:45:1445"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:15:03:153"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:15:16:1516"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:15:34:1534"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:15:47:1547"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:16:05:165"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:16:18:1618"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:16:36:1636"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:16:50:1650"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:17:07:177"}
{"level":"error","message":"❌ Failed to start server: ResearchAIService is not a constructor","stack":"TypeError: ResearchAIService is not a constructor\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:238:25)","timestamp":"2025-06-05 06:18:12:1812"}
{"level":"error","message":"❌ Failed to start server: ResearchAIService is not a constructor","stack":"TypeError: ResearchAIService is not a constructor\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:238:25)","timestamp":"2025-06-05 06:18:12:1812"}
{"level":"error","message":"❌ Failed to start server: ResearchAIServiceModule.ResearchAIService is not a constructor","stack":"TypeError: ResearchAIServiceModule.ResearchAIService is not a constructor\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:238:25)","timestamp":"2025-06-05 06:18:24:1824"}
{"level":"error","message":"❌ Failed to start server: ResearchAIServiceModule.ResearchAIService is not a constructor","stack":"TypeError: ResearchAIServiceModule.ResearchAIService is not a constructor\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:238:25)","timestamp":"2025-06-05 06:18:24:1824"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:19:26:1926"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:19:26:1926"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:20:47:2047"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:245:19)","syscall":"listen","timestamp":"2025-06-05 06:21:05:215"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:21:18:2118"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:21:50:2150"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:22:05:225"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:22:21:2221"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:22:36:2236"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:22:52:2252"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:23:07:237"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:23:23:2323"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:23:38:2338"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:23:54:2354"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:24:09:249"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:24:25:2425"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:24:40:2440"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:24:56:2456"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:25:12:2512"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:25:27:2527"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:25:43:2543"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:25:58:2558"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:26:14:2614"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:26:30:2630"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:26:45:2645"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:27:01:271"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:27:16:2716"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:27:32:2732"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:27:47:2747"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:28:03:283"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:28:18:2818"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:28:34:2834"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:28:49:2849"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:29:05:295"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:29:20:2920"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:29:36:2936"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:29:52:2952"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:30:07:307"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:30:23:3023"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:30:39:3039"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:30:54:3054"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:31:10:3110"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:31:25:3125"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:31:41:3141"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:31:56:3156"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:32:12:3212"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:32:27:3227"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:32:43:3243"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:32:58:3258"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:33:14:3314"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:33:29:3329"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:33:45:3345"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:34:01:341"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:246:19)","syscall":"listen","timestamp":"2025-06-05 06:34:19:3419"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:247:19)","syscall":"listen","timestamp":"2025-06-05 06:34:30:3430"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:248:19)","syscall":"listen","timestamp":"2025-06-05 06:34:39:3439"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:35:10:3510"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:35:10:3510"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:35:41:3541"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:35:41:3541"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:36:12:3612"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:36:12:3612"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:36:43:3643"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:36:43:3643"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:37:14:3714"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:37:14:3714"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:37:45:3745"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:37:46:3746"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:38:17:3817"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:38:17:3817"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:38:48:3848"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:38:48:3848"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:39:19:3919"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:39:19:3919"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:39:50:3950"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:39:50:3950"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:40:21:4021"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:40:21:4021"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:40:52:4052"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:40:52:4052"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:41:23:4123"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:41:24:4124"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:41:55:4155"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:41:55:4155"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:42:26:4226"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:42:26:4226"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:42:57:4257"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:42:57:4257"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:43:28:4328"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:43:28:4328"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to search research papers","query":{"includeMetaAnalysis":true,"maxResults":10,"minEvidenceLevel":4,"supplement":"Vitamin D3"},"stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at ResearchAIService.searchResearchPapers (/home/<USER>/Suplementor/backend/src/services/ResearchAIService.ts:111:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:59:46)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349"}
{"error":"Redis client not initialized. Call connect() first.","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"POST","path":"/api/research-ai/search","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at ResearchAIService.searchResearchPapers (/home/<USER>/Suplementor/backend/src/services/ResearchAIService.ts:111:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:59:46)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userId":"research-demo-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/research-demo-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to search research papers","query":{"includeMetaAnalysis":true,"maxResults":10,"minEvidenceLevel":4,"supplement":"Vitamin D3"},"stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at ResearchAIService.searchResearchPapers (/home/<USER>/Suplementor/backend/src/services/ResearchAIService.ts:111:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:59:46)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349"}
{"error":"Redis client not initialized. Call connect() first.","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"POST","path":"/api/research-ai/search","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at ResearchAIService.searchResearchPapers (/home/<USER>/Suplementor/backend/src/services/ResearchAIService.ts:111:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:59:46)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:43:49:4349","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:43:59:4359"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:43:59:4359"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:44:30:4430"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:44:30:4430"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:45:01:451"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:45:02:452"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:28:4528","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:45:28:4528","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:28:4528","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:45:28:4528","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to analyze supplement stack","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1098:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:40)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/graph.ts:19:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","supplementIds":["magnesium-glycinate","omega-3","vitamin-d3"],"timestamp":"2025-06-05 06:45:28:4528"}
{"error":"Failed to analyze supplement stack","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"POST","path":"/api/graph/analyze-stack","stack":"Error: Failed to analyze supplement stack\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1199:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:21)","timestamp":"2025-06-05 06:45:28:4528","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:16:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:26:23)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/monitor/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:206:27)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:127:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:52)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:18:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 06:45:32:4532","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/research-ai/insights/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/researchAI.ts:116:27)","timestamp":"2025-06-05 06:45:32:4532","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:45:33:4533"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:45:33:4533"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:46:04:464"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:46:35:4635"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:47:06:476"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:47:37:4737"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:48:08:488"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:48:40:4840"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:49:11:4911"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:49:42:4942"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:50:13:5013"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:50:44:5044"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:51:15:5115"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:51:47:5147"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:52:18:5218"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:52:49:5249"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:53:20:5320"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:53:51:5351"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:54:22:5422"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:54:54:5454"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:55:25:5525"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:55:56:5556"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:56:27:5627"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:58:26:5826"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:58:57:5857"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:59:28:5928"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 06:59:59:5959"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:00:30:030"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:01:02:12"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:01:33:133"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:02:04:24"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:02:35:235"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:03:06:36"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:03:37:337"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:04:09:49"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:04:40:440"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:05:11:511"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:05:42:542"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:06:13:613"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:06:45:645"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:07:16:716"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:07:47:747"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:08:18:818"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:08:49:849"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:09:20:920"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:09:52:952"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:10:23:1023"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:10:54:1054"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:11:25:1125"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:11:56:1156"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:12:28:1228"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:12:59:1259"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:13:30:1330"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:14:01:141"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:14:32:1432"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:15:03:153"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:15:35:1535"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:16:06:166"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:16:37:1637"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:17:08:178"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:17:39:1739"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:18:11:1811"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:18:42:1842"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:19:13:1913"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:19:44:1944"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:20:15:2015"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:20:47:2047"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:21:18:2118"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:21:49:2149"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:22:20:2220"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:22:51:2251"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:23:23:2323"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:23:54:2354"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:24:25:2425"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:24:56:2456"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:25:28:2528"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:135:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:17:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 07:25:47:2547","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:197:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:23)","timestamp":"2025-06-05 07:25:47:2547","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to analyze supplement stack","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1098:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:40)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/graph.ts:19:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","supplementIds":["magnesium-glycinate","omega-3","vitamin-d3"],"timestamp":"2025-06-05 07:25:48:2548"}
{"error":"Failed to analyze supplement stack","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"POST","path":"/api/graph/analyze-stack","stack":"Error: Failed to analyze supplement stack\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1199:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:21)","timestamp":"2025-06-05 07:25:48:2548","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:135:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:17:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 07:25:48:2548","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:197:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:23)","timestamp":"2025-06-05 07:25:48:2548","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:25:59:2559"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:135:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:17:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 07:26:24:2624","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:197:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:23)","timestamp":"2025-06-05 07:26:24:2624","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get user profile","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:135:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:48)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:17:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-05 07:26:24:2624","userId":"cosmic-user"}
{"error":"Failed to retrieve user profile","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"GET","path":"/api/profile/cosmic-user","stack":"Error: Failed to retrieve user profile\n    at UserProfileService.getUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:197:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/userProfile.ts:27:23)","timestamp":"2025-06-05 07:26:24:2624","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to analyze supplement stack","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1098:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:40)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/home/<USER>/Suplementor/backend/src/routes/graph.ts:19:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/home/<USER>/Suplementor/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","supplementIds":["magnesium-glycinate","omega-3","vitamin-d3"],"timestamp":"2025-06-05 07:26:24:2624"}
{"error":"Failed to analyze supplement stack","ip":"::ffff:127.0.0.1","level":"error","message":"Request error","method":"POST","path":"/api/graph/analyze-stack","stack":"Error: Failed to analyze supplement stack\n    at GraphService.analyzeSupplementStack (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:1199:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:281:21)","timestamp":"2025-06-05 07:26:24:2624","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:26:30:2630"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:27:01:271"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:27:32:2732"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:28:04:284"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:28:35:2835"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:29:06:296"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:30:31:3031"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:31:02:312"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:31:34:3134"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:32:05:325"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:35:19:3519"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:36:30:3630"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:37:01:371"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:39:21:3921"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:39:52:3952"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:40:23:4023"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:40:54:4054"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:41:26:4126"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:42:04:424"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:42:36:4236"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:43:07:437"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:43:38:4338"}
{"error":"Can't find /api/graph/data?limit=1000&search= on this server!","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/data","stack":"Error: Can't find /api/graph/data?limit=1000&search= on this server!\n    at notFoundHandler (/home/<USER>/Suplementor/backend/src/middleware/notFoundHandler.ts:5:15)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at healthCheckMiddleware (/home/<USER>/Suplementor/backend/src/middleware/performanceMiddleware.ts:420:5)","timestamp":"2025-06-05 07:43:54:4354","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get graph stats","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:701:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:36)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)","timestamp":"2025-06-05 07:43:54:4354"}
{"error":"Failed to get graph statistics","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/stats","stack":"Error: Failed to get graph statistics\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:747:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:17)","timestamp":"2025-06-05 07:43:54:4354","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Can't find /api/graph/data?limit=1000&search= on this server!","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/data","stack":"Error: Can't find /api/graph/data?limit=1000&search= on this server!\n    at notFoundHandler (/home/<USER>/Suplementor/backend/src/middleware/notFoundHandler.ts:5:15)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at healthCheckMiddleware (/home/<USER>/Suplementor/backend/src/middleware/performanceMiddleware.ts:420:5)","timestamp":"2025-06-05 07:43:54:4354","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get graph stats","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:701:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:36)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)","timestamp":"2025-06-05 07:43:55:4355"}
{"error":"Failed to get graph statistics","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/stats","stack":"Error: Failed to get graph statistics\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:747:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:17)","timestamp":"2025-06-05 07:43:55:4355","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Can't find /api/graph/data?limit=1000&search= on this server!","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/data","stack":"Error: Can't find /api/graph/data?limit=1000&search= on this server!\n    at notFoundHandler (/home/<USER>/Suplementor/backend/src/middleware/notFoundHandler.ts:5:15)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at healthCheckMiddleware (/home/<USER>/Suplementor/backend/src/middleware/performanceMiddleware.ts:420:5)","timestamp":"2025-06-05 07:43:56:4356","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get graph stats","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:701:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:36)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)","timestamp":"2025-06-05 07:43:56:4356"}
{"error":"Failed to get graph statistics","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/stats","stack":"Error: Failed to get graph statistics\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:747:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:17)","timestamp":"2025-06-05 07:43:56:4356","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Can't find /api/graph/data?limit=1000&search= on this server!","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/data","stack":"Error: Can't find /api/graph/data?limit=1000&search= on this server!\n    at notFoundHandler (/home/<USER>/Suplementor/backend/src/middleware/notFoundHandler.ts:5:15)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:328:13)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at healthCheckMiddleware (/home/<USER>/Suplementor/backend/src/middleware/performanceMiddleware.ts:420:5)","timestamp":"2025-06-05 07:44:00:440","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Redis client not initialized. Call connect() first.","level":"error","message":"Failed to get graph stats","stack":"Error: Redis client not initialized. Call connect() first.\n    at RedisConnection.getClient (/home/<USER>/Suplementor/backend/src/config/redis.ts:78:13)\n    at RedisConnection.get (/home/<USER>/Suplementor/backend/src/config/redis.ts:105:25)\n    at cacheGet (/home/<USER>/Suplementor/backend/src/config/redis.ts:357:58)\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:701:28)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:36)\n    at <anonymous> (/home/<USER>/Suplementor/backend/src/middleware/errorHandler.ts:279:5)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/home/<USER>/Suplementor/backend/node_modules/express/lib/router/layer.js:95:5)","timestamp":"2025-06-05 07:44:00:440"}
{"error":"Failed to get graph statistics","ip":"::1","level":"error","message":"Request error","method":"GET","path":"/api/graph/stats","stack":"Error: Failed to get graph statistics\n    at GraphService.getGraphStats (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:747:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async <anonymous> (/home/<USER>/Suplementor/backend/src/routes/graph.ts:319:17)","timestamp":"2025-06-05 07:44:00:440","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:44:09:449"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:44:41:4441"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:45:12:4512"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:45:43:4543"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:248:19)","syscall":"listen","timestamp":"2025-06-05 07:49:48:4948"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:50:19:5019"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:50:50:5050"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:51:21:5121"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:249:19)","syscall":"listen","timestamp":"2025-06-05 07:51:55:5155"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:250:19)","syscall":"listen","timestamp":"2025-06-05 07:52:04:524"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::5420","port":5420,"stack":"Error: listen EADDRINUSE: address already in use :::5420\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:251:19)","syscall":"listen","timestamp":"2025-06-05 07:52:12:5212"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:52:43:5243"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:53:14:5314"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:53:46:5346"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:54:17:5417"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:54:48:5448"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:55:55:5555"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:56:26:5626"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:56:57:5657"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:57:28:5728"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:58:00:580"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:58:31:5831"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:59:02:592"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 07:59:33:5933"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:00:05:05"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:00:36:036"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:01:07:17"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:01:38:138"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:02:10:210"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:02:41:241"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:03:12:312"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:03:43:343"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:04:15:415"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:04:46:446"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:05:17:517"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:05:48:548"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:06:20:620"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:06:51:651"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:07:22:722"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:07:54:754"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:08:25:825"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:08:56:856"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:09:27:927"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:09:59:959"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:10:30:1030"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:11:01:111"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:11:33:1133"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:12:04:124"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:12:35:1235"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:13:06:136"}
{"level":"error","message":"Redis error: NOAUTH Authentication required.","timestamp":"2025-06-05 08:13:38:1338"}
