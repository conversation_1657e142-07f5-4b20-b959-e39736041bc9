import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// Extend AxiosRequestConfig to include metadata (used for timing)
// interface CustomAxiosRequestConfig extends AxiosRequestConfig {
//   metadata?: {
//     startTime: number;
//   };
// }

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5420/api';
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp (using any to bypass type checking)
    (config as any).metadata = { startTime: Date.now() };

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // Calculate request duration
    const duration = Date.now() - ((response.config as any).metadata?.startTime || 0);

    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        duration: `${duration}ms`,
        data: response.data,
      });
    }

    // Check if response indicates an error
    if (response.data && !response.data.success) {
      const error = new Error(response.data.message || 'API request failed');
      (error as any).response = response;
      (error as any).apiError = response.data.error;
      throw error;
    }

    return response;
  },
  (error) => {
    // Calculate request duration if available
    const duration = (error.config as any)?.metadata?.startTime
      ? Date.now() - (error.config as any).metadata.startTime
      : 0;

    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        duration: duration ? `${duration}ms` : 'unknown',
        message: error.message,
        data: error.response?.data,
      });
    }

    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const apiError = error.response.data?.error || {};
      const message = error.response.data?.message || 'An error occurred';
      
      const enhancedError = new Error(message);
      (enhancedError as any).status = error.response.status;
      (enhancedError as any).apiError = apiError;
      (enhancedError as any).response = error.response;
      
      return Promise.reject(enhancedError);
    } else if (error.request) {
      // Network error
      const networkError = new Error('Network error - please check your connection');
      (networkError as any).isNetworkError = true;
      return Promise.reject(networkError);
    } else {
      // Other error
      return Promise.reject(error);
    }
  }
);

// Generic API methods
export const apiClient = {
  // Base URL for external usage
  get baseURL() {
    return API_BASE_URL;
  },

  // GET request
  get: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> => {
    const response = await api.get<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },

  // POST request
  post: async <T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<T> => {
    const response = await api.post<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // PUT request
  put: async <T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<T> => {
    const response = await api.put<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // PATCH request
  patch: async <T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig
  ): Promise<T> => {
    const response = await api.patch<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // DELETE request
  delete: async <T = any>(
    url: string, 
    config?: AxiosRequestConfig
  ): Promise<T> => {
    const response = await api.delete<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },

  // Upload file
  upload: async <T = any>(
    url: string,
    formData: FormData,
    onUploadProgress?: (progressEvent: any) => void
  ): Promise<T> => {
    const response = await api.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
    return response.data.data as T;
  },

  // Download file
  download: async (
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> => {
    const response = await api.get(url, {
      ...config,
      responseType: 'blob',
    });

    // Create download link
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

// Health check
export const healthCheck = async (): Promise<boolean> => {
  try {
    await apiClient.get('/health');
    return true;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

// Error handling utilities
export const isNetworkError = (error: any): boolean => {
  return error?.isNetworkError === true;
};

export const isApiError = (error: any): boolean => {
  return error?.response?.data?.error !== undefined;
};

export const getErrorMessage = (error: any): string => {
  if (isNetworkError(error)) {
    return 'Network error - please check your connection';
  }
  
  if (isApiError(error)) {
    return error.response.data.message || 'An API error occurred';
  }
  
  return error.message || 'An unexpected error occurred';
};

export const getErrorCode = (error: any): string | undefined => {
  return error?.response?.data?.error?.code;
};

export const getErrorStatus = (error: any): number | undefined => {
  return error?.response?.status || error?.status;
};

// Request cancellation
export const createCancelToken = () => {
  return axios.CancelToken.source();
};

export const isRequestCancelled = (error: any): boolean => {
  return axios.isCancel(error);
};

// Enhanced Agentic API Services
export const agenticAPI = {
  // CrewAI Flow Management
  flows: {
    create: async (flowConfig: any): Promise<any> => {
      return apiClient.post('/flows', flowConfig);
    },

    start: async (flowId: string, params?: any): Promise<any> => {
      return apiClient.post(`/flows/${flowId}/start`, params);
    },

    stop: async (flowId: string): Promise<any> => {
      return apiClient.post(`/flows/${flowId}/stop`);
    },

    getStatus: async (flowId: string): Promise<any> => {
      return apiClient.get(`/flows/${flowId}/status`);
    },

    list: async (): Promise<any[]> => {
      return apiClient.get('/flows');
    },

    delete: async (flowId: string): Promise<void> => {
      return apiClient.delete(`/flows/${flowId}`);
    }
  },

  // Agent Management
  agents: {
    list: async (): Promise<any[]> => {
      return apiClient.get('/agents');
    },

    getStatus: async (agentId: string): Promise<any> => {
      return apiClient.get(`/agents/${agentId}/status`);
    },

    assign: async (agentId: string, task: any): Promise<any> => {
      return apiClient.post(`/agents/${agentId}/assign`, task);
    },

    getMetrics: async (agentId: string): Promise<any> => {
      return apiClient.get(`/agents/${agentId}/metrics`);
    }
  },

  // Enhanced Research Services with LM Studio Integration
  research: {
    startAutonomous: async (request: any): Promise<any> => {
      return apiClient.post('/enhanced-research/autonomous', request);
    },

    getResults: async (researchId: string): Promise<any> => {
      return apiClient.get(`/enhanced-research/${researchId}/results`);
    },

    getFlows: async (): Promise<any> => {
      return apiClient.get('/enhanced-research/flows');
    },

    stopFlow: async (flowId: string): Promise<any> => {
      return apiClient.post(`/enhanced-research/flows/${flowId}/stop`);
    },

    getFlowStatus: async (flowId: string): Promise<any> => {
      return apiClient.get(`/enhanced-research/flows/${flowId}/status`);
    },

    searchSupplements: async (query: string, options?: any): Promise<any> => {
      return apiClient.post('/enhanced-research/supplements/search', { query, ...options });
    },

    analyzeInteractions: async (supplements: string[]): Promise<any> => {
      return apiClient.post('/enhanced-research/interactions', { supplements });
    },

    getInsights: async (userId: string, supplements?: string[]): Promise<any> => {
      return apiClient.post('/enhanced-research/insights', { userId, supplements });
    },

    chat: async (message: string, options?: any): Promise<any> => {
      return apiClient.post('/enhanced-research/chat', { message, ...options });
    },

    getModels: async (): Promise<any> => {
      return apiClient.get('/enhanced-research/models');
    }
  },

  // AI Oracle Services
  oracle: {
    generatePredictions: async (userProfile: any, supplements: any[]): Promise<any> => {
      return apiClient.post('/oracle/predictions', { userProfile, supplements });
    },

    analyzeHealth: async (healthData: any): Promise<any> => {
      return apiClient.post('/oracle/health-analysis', healthData);
    },

    getRecommendations: async (userId: string, context?: any): Promise<any> => {
      return apiClient.post('/oracle/recommendations', { userId, context });
    }
  },

  // Real-time Monitoring
  monitoring: {
    getSystemStatus: async (): Promise<any> => {
      return apiClient.get('/monitoring/system');
    },

    getAgentMetrics: async (): Promise<any> => {
      return apiClient.get('/monitoring/agents');
    },

    getPerformanceStats: async (timeRange?: string): Promise<any> => {
      return apiClient.get('/monitoring/performance', { params: { timeRange } });
    }
  }
};

// WebSocket Service for Real-time Updates
export class AgenticWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Set<Function>> = new Map();

  constructor(private url: string = 'ws://localhost:5420/ws') {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('🔗 WebSocket connected');
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('🔌 WebSocket disconnected');
          this.handleReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  private handleMessage(data: any) {
    const { type, payload } = data;
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(listener => listener(payload));
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  subscribe(eventType: string, callback: Function) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(callback);
  }

  unsubscribe(eventType: string, callback: Function) {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  send(type: string, payload: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, payload }));
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Global WebSocket instance
export const agenticWS = new AgenticWebSocketService();

// Default export
export default api;
