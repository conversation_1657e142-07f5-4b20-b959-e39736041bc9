import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Activity, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  Zap,
  Heart,
  Shield,
  Target,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  Pill,
  Lightbulb
} from 'lucide-react';
import { neurotransmitterProfiles } from '@/data/neurotransmitterData';

const NeuromediatorsPage: React.FC = () => {
  const [neurotransmitterLevels, setNeurotransmitterLevels] = useState({
    serotonin: 45,
    dopamine: 65,
    gaba: 35,
    acetylcholine: 55
  });

  const [selectedComparison, setSelectedComparison] = useState<string[]>(['serotonin', 'dopamine']);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setNeurotransmitterLevels(prev => ({
        serotonin: Math.max(20, Math.min(80, prev.serotonin + (Math.random() - 0.5) * 1.5)),
        dopamine: Math.max(20, Math.min(80, prev.dopamine + (Math.random() - 0.5) * 1.5)),
        gaba: Math.max(20, Math.min(80, prev.gaba + (Math.random() - 0.5) * 1.5)),
        acetylcholine: Math.max(20, Math.min(80, prev.acetylcholine + (Math.random() - 0.5) * 1.5))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const neurotransmitterInfo = {
    serotonin: { icon: Heart, color: '#3b82f6', name: 'Serotonin', focus: 'Mood & Sleep' },
    dopamine: { icon: Zap, color: '#10b981', name: 'Dopamine', focus: 'Motivation & Energy' },
    gaba: { icon: Shield, color: '#a855f7', name: 'GABA', focus: 'Calm & Relaxation' },
    acetylcholine: { icon: Target, color: '#f59e0b', name: 'Acetylcholine', focus: 'Memory & Focus' }
  };

  const getBalanceStatus = (level: number) => {
    if (level >= 40 && level <= 60) return { status: 'Optimal', color: 'text-green-600', icon: CheckCircle };
    if (level < 40) return { status: 'Low', color: 'text-red-600', icon: TrendingDown };
    return { status: 'High', color: 'text-orange-600', icon: TrendingUp };
  };

  const overallBalance = Object.values(neurotransmitterLevels).reduce((acc, val) => acc + val, 0) / 4;
  const optimalSystems = Object.values(neurotransmitterLevels).filter(level => level >= 40 && level <= 60).length;

  const interactions = [
    {
      pair: ['serotonin', 'dopamine'],
      relationship: 'Balance',
      description: 'Serotonin and dopamine work together for mood and motivation balance',
      status: Math.abs(neurotransmitterLevels.serotonin - neurotransmitterLevels.dopamine) < 15 ? 'Balanced' : 'Imbalanced'
    },
    {
      pair: ['dopamine', 'gaba'],
      relationship: 'Opposition',
      description: 'High dopamine can suppress GABA, leading to overstimulation',
      status: neurotransmitterLevels.dopamine > 60 && neurotransmitterLevels.gaba < 40 ? 'Concerning' : 'Stable'
    },
    {
      pair: ['gaba', 'acetylcholine'],
      relationship: 'Synergy',
      description: 'GABA supports focused attention by reducing neural noise',
      status: neurotransmitterLevels.gaba >= 40 && neurotransmitterLevels.acetylcholine >= 40 ? 'Synergistic' : 'Suboptimal'
    }
  ];

  const recommendations = [
    {
      condition: neurotransmitterLevels.serotonin < 40,
      title: 'Boost Serotonin',
      supplements: ['5-HTP', 'Tryptophan', 'B6'],
      lifestyle: ['Morning sunlight', 'Social connection', 'Gratitude practice']
    },
    {
      condition: neurotransmitterLevels.dopamine > 60,
      title: 'Balance Dopamine',
      supplements: ['L-Theanine', 'Magnesium'],
      lifestyle: ['Reduce stimulants', 'Meditation', 'Regular sleep']
    },
    {
      condition: neurotransmitterLevels.gaba < 40,
      title: 'Support GABA',
      supplements: ['L-Theanine', 'Magnesium', 'Taurine'],
      lifestyle: ['Deep breathing', 'Yoga', 'Nature exposure']
    },
    {
      condition: neurotransmitterLevels.acetylcholine < 40,
      title: 'Enhance Acetylcholine',
      supplements: ['Alpha-GPC', 'CDP-Choline'],
      lifestyle: ['Learning new skills', 'Reading', 'Mental challenges']
    }
  ].filter(rec => rec.condition);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">
              <Brain className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Comprehensive Neuromediators Hub</h1>
              <p className="text-xl text-gray-600">
                Unified dashboard for all neurotransmitter systems with cross-references and interactions
              </p>
            </div>
          </div>
        </motion.div>

        {/* System Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <div className="cosmic-card text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Activity className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-1">{Math.round(overallBalance)}%</div>
            <div className="text-sm text-gray-600">Overall Balance</div>
          </div>

          <div className="cosmic-card text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full mx-auto mb-4 flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-1">{optimalSystems}/4</div>
            <div className="text-sm text-gray-600">Optimal Systems</div>
          </div>

          <div className="cosmic-card text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full mx-auto mb-4 flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-1">{recommendations.length}</div>
            <div className="text-sm text-gray-600">Active Recommendations</div>
          </div>

          <div className="cosmic-card text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full mx-auto mb-4 flex items-center justify-center">
              <BarChart3 className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-1">Live</div>
            <div className="text-sm text-gray-600">Real-time Data</div>
          </div>
        </motion.div>

        {/* Neurotransmitter Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {Object.entries(neurotransmitterLevels).map(([key, level]) => {
            const info = neurotransmitterInfo[key as keyof typeof neurotransmitterInfo];
            const status = getBalanceStatus(level);
            const Icon = info.icon;
            const StatusIcon = status.icon;

            return (
              <motion.div
                key={key}
                className="cosmic-card"
                whileHover={{ y: -4 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div 
                    className="w-12 h-12 rounded-xl flex items-center justify-center text-white"
                    style={{ backgroundColor: info.color }}
                  >
                    <Icon className="w-6 h-6" />
                  </div>
                  <div className="flex items-center space-x-1">
                    <StatusIcon className={`w-4 h-4 ${status.color}`} />
                    <span className={`text-xs font-medium ${status.color}`}>
                      {status.status}
                    </span>
                  </div>
                </div>

                <h3 className="font-bold text-gray-900 mb-1">{info.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{info.focus}</p>

                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Level</span>
                    <span className="font-medium">{level}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <motion.div
                      className="h-3 rounded-full"
                      style={{ backgroundColor: info.color }}
                      initial={{ width: 0 }}
                      animate={{ width: `${level}%` }}
                      transition={{ duration: 1, delay: 0.5 }}
                    />
                  </div>
                </div>

                <button 
                  className="w-full text-sm py-2 px-4 rounded-lg font-medium transition-colors"
                  style={{ 
                    backgroundColor: info.color + '20',
                    color: info.color
                  }}
                >
                  View Details
                </button>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Interactions Matrix */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="cosmic-card mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <Lightbulb className="w-6 h-6 mr-3 text-yellow-500" />
            Neurotransmitter Interactions
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {interactions.map((interaction, index) => (
              <div key={index} className="p-4 rounded-xl border-2 border-gray-200 hover:border-gray-300 transition-colors">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {interaction.pair.map((nt, i) => {
                      const info = neurotransmitterInfo[nt as keyof typeof neurotransmitterInfo];
                      const Icon = info.icon;
                      return (
                        <div key={nt} className="flex items-center">
                          <div 
                            className="w-8 h-8 rounded-lg flex items-center justify-center text-white"
                            style={{ backgroundColor: info.color }}
                          >
                            <Icon className="w-4 h-4" />
                          </div>
                          {i === 0 && <ArrowRight className="w-4 h-4 mx-2 text-gray-400" />}
                        </div>
                      );
                    })}
                  </div>
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                    interaction.status === 'Balanced' || interaction.status === 'Synergistic' || interaction.status === 'Stable'
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {interaction.status}
                  </span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{interaction.relationship}</h4>
                <p className="text-sm text-gray-600">{interaction.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Active Recommendations */}
        {recommendations.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="cosmic-card"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Pill className="w-6 h-6 mr-3 text-blue-500" />
              Personalized Recommendations
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {recommendations.map((rec, index) => (
                <div key={index} className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                  <h3 className="font-bold text-gray-900 mb-4">{rec.title}</h3>
                  
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-700 mb-2">Recommended Supplements:</h4>
                    <div className="flex flex-wrap gap-2">
                      {rec.supplements.map((supplement, i) => (
                        <span key={i} className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                          {supplement}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Lifestyle Factors:</h4>
                    <div className="flex flex-wrap gap-2">
                      {rec.lifestyle.map((factor, i) => (
                        <span key={i} className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                          {factor}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default NeuromediatorsPage;
