import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Moon, 
  Waves, 
  Brain, 
  Pill, 
  AlertCircle,
  Activity,
  Heart,
  Smile,
  Coffee,
  Leaf,
  Sunset
} from 'lucide-react';

const GABAPage: React.FC = () => {
  const [currentLevel] = useState(35); // Simulated current GABA level
  const [selectedTab, setSelectedTab] = useState('overview');

  const gabaData = {
    name: 'GABA',
    subtitle: 'The Calming Neurotransmitter',
    description: 'GABA (Gamma-Aminobutyric Acid) is the brain\'s primary inhibitory neurotransmitter, promoting relaxation, reducing anxiety, and supporting restful sleep. It acts as the brain\'s natural brake system.',
    currentLevel: currentLevel,
    optimalRange: [40, 60],
    color: '#a855f7',
    gradient: 'linear-gradient(135deg, #c084fc 0%, #7c3aed 100%)'
  };

  const functions = [
    { icon: Shield, title: 'Anxiety Reduction', description: 'Calms overactive neural circuits and reduces stress' },
    { icon: <PERSON>, title: 'Sleep Promotion', description: 'Facilitates deep, restorative sleep cycles' },
    { icon: Waves, title: 'Muscle Relaxation', description: 'Reduces muscle tension and promotes physical calm' },
    { icon: Heart, title: 'Blood Pressure', description: 'Helps regulate cardiovascular stress response' },
    { icon: Brain, title: 'Seizure Prevention', description: 'Prevents excessive neural excitation' },
    { icon: Smile, title: 'Emotional Balance', description: 'Stabilizes mood and emotional responses' }
  ];

  const supplements = [
    {
      name: 'L-Theanine',
      dosage: '100-400mg daily',
      timing: 'Morning or evening',
      mechanism: 'Enhances GABA activity and alpha brain waves',
      evidence: 'Strong',
      safety: 'Very safe, synergistic with caffeine'
    },
    {
      name: 'GABA Supplement',
      dosage: '250-750mg daily',
      timing: 'Evening, empty stomach',
      mechanism: 'Direct GABA supplementation',
      evidence: 'Moderate',
      safety: 'Generally safe, avoid with sedatives'
    },
    {
      name: 'Magnesium Glycinate',
      dosage: '200-400mg daily',
      timing: 'Evening with food',
      mechanism: 'GABA receptor activation and muscle relaxation',
      evidence: 'Strong',
      safety: 'Very safe, may cause loose stools'
    },
    {
      name: 'Taurine',
      dosage: '500-2000mg daily',
      timing: 'Evening',
      mechanism: 'Modulates GABA receptors and calms nervous system',
      evidence: 'Moderate',
      safety: 'Very safe, naturally occurring'
    },
    {
      name: 'Passionflower',
      dosage: '250-500mg daily',
      timing: '30 minutes before bed',
      mechanism: 'Increases GABA in the brain',
      evidence: 'Moderate',
      safety: 'Generally safe, mild sedative'
    },
    {
      name: 'Valerian Root',
      dosage: '300-600mg daily',
      timing: '1 hour before bed',
      mechanism: 'Enhances GABA receptor sensitivity',
      evidence: 'Strong',
      safety: 'Generally safe, may cause drowsiness'
    }
  ];

  const naturalSources = [
    { name: 'Fermented Foods', amount: 'Yogurt, kefir, kimchi', benefit: 'Natural GABA production' },
    { name: 'Sprouted Grains', amount: 'Brown rice, oats', benefit: 'Direct GABA content' },
    { name: 'Broccoli', amount: 'High GABA content', benefit: 'Natural source' },
    { name: 'Lentils', amount: 'GABA + B vitamins', benefit: 'Synthesis support' },
    { name: 'Cherries', amount: 'Natural melatonin + GABA', benefit: 'Sleep enhancement' },
    { name: 'Green Tea', amount: 'L-theanine content', benefit: 'GABA activity boost' }
  ];

  const lifestyle = [
    { icon: Sunset, title: 'Evening Routine', description: 'Consistent bedtime routine activates GABA naturally' },
    { icon: Waves, title: 'Deep Breathing', description: 'Activates parasympathetic nervous system' },
    { icon: Leaf, title: 'Nature Exposure', description: 'Reduces cortisol and promotes GABA activity' },
    { icon: Coffee, title: 'Limit Stimulants', description: 'Reduce caffeine and other stimulants after 2 PM' }
  ];

  const anxietySymptoms = [
    'Racing thoughts and worry',
    'Physical tension and restlessness',
    'Difficulty falling or staying asleep',
    'Irritability and mood swings',
    'Digestive issues from stress',
    'Muscle tension and headaches'
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Brain },
    { id: 'supplements', label: 'Supplements', icon: Pill },
    { id: 'nutrition', label: 'Nutrition', icon: Leaf },
    { id: 'lifestyle', label: 'Lifestyle', icon: Sunset }
  ];

  const isLow = currentLevel < gabaData.optimalRange[0];
  const isHigh = currentLevel > gabaData.optimalRange[1];
  const isOptimal = !isLow && !isHigh;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="cosmic-neurotransmitter-card cosmic-neurotransmitter-card--gaba">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div 
                  className="w-16 h-16 rounded-2xl flex items-center justify-center text-white"
                  style={{ background: gabaData.gradient }}
                >
                  <Shield className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{gabaData.name}</h1>
                  <p className="text-lg text-gray-600">{gabaData.subtitle}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-3xl font-bold text-gray-900 mb-1">{currentLevel}%</div>
                <div className={`text-sm font-medium ${isOptimal ? 'text-green-600' : isLow ? 'text-red-600' : 'text-orange-600'}`}>
                  {isOptimal ? 'Optimal' : isLow ? 'Below Optimal' : 'Above Optimal'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Target: {gabaData.optimalRange[0]}-{gabaData.optimalRange[1]}%
                </div>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed">{gabaData.description}</p>
          </div>
        </motion.div>

        {/* Status Alert */}
        {!isOptimal && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`mb-8 p-6 rounded-2xl border-2 ${
              isLow 
                ? 'bg-red-50 border-red-200' 
                : 'bg-orange-50 border-orange-200'
            }`}
          >
            <div className="flex items-center space-x-3">
              <AlertCircle className={`w-6 h-6 ${isLow ? 'text-red-600' : 'text-orange-600'}`} />
              <div>
                <h3 className={`font-semibold ${isLow ? 'text-red-900' : 'text-orange-900'}`}>
                  {isLow ? 'Low GABA Activity Detected' : 'Elevated GABA Levels'}
                </h3>
                <p className={`text-sm ${isLow ? 'text-red-700' : 'text-orange-700'}`}>
                  {isLow 
                    ? 'Consider L-Theanine and magnesium supplementation for natural GABA support.'
                    : 'Monitor for excessive sedation and consider reducing GABAergic supplements.'
                  }
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Low GABA Symptoms */}
        {isLow && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8 cosmic-card"
          >
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
              Common Low GABA Symptoms
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {anxietySymptoms.map((symptom, index) => (
                <div key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-3" />
                  {symptom}
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white p-1 rounded-xl border border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                  selectedTab === tab.id
                    ? 'bg-purple-100 text-purple-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={selectedTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {functions.map((func, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <func.icon className="w-6 h-6 text-purple-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{func.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{func.description}</p>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'supplements' && (
            <div className="space-y-6">
              {supplements.map((supplement, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{supplement.name}</h3>
                      <p className="text-gray-600">{supplement.mechanism}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-purple-600 mb-1">Evidence: {supplement.evidence}</div>
                      <div className="text-xs text-gray-500">{supplement.dosage}</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Timing</div>
                      <div className="text-gray-600">{supplement.timing}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Safety</div>
                      <div className="text-gray-600">{supplement.safety}</div>
                    </div>
                    <div>
                      <button className="w-full cosmic-button text-sm py-2 text-white" style={{ background: gabaData.gradient }}>
                        Add to Stack
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'nutrition' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {naturalSources.map((source, index) => (
                <div key={index} className="cosmic-card">
                  <h3 className="font-semibold text-gray-900 mb-2">{source.name}</h3>
                  <div className="text-sm text-gray-600 mb-2">{source.amount}</div>
                  <div className="text-sm text-purple-600 font-medium">{source.benefit}</div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'lifestyle' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {lifestyle.map((item, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <item.icon className="w-6 h-6 text-purple-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{item.title}</h3>
                  </div>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default GABAPage;
