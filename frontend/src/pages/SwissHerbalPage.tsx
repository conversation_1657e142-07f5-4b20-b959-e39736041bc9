import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Mountain, 
  Search, 
  Filter, 
  Star, 
  Clock,
  Shield,
  Heart,
  Brain,
  Zap,
  Target,
  BookOpen,
  Award,
  Globe
} from 'lucide-react';

interface HerbalRemedy {
  id: string;
  name: string;
  scientificName: string;
  origin: string;
  category: string;
  targetNeurotransmitter: string[];
  traditionalUse: string;
  modernResearch: string;
  activeCompounds: string[];
  dosage: string;
  preparation: string;
  safetyRating: number;
  evidenceLevel: 'Traditional' | 'Preliminary' | 'Moderate' | 'Strong';
  image: string;
  benefits: string[];
  contraindications: string[];
}

const SwissHerbalPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedNeurotransmitter, setSelectedNeurotransmitter] = useState('all');

  const herbalRemedies: HerbalRemedy[] = [
    {
      id: 'st-johns-wort',
      name: '<PERSON><PERSON> <PERSON>\'s Wort',
      scientificName: 'Hypericum perforatum',
      origin: 'Swiss Alps',
      category: 'Mood Support',
      targetNeurotransmitter: ['serotonin', 'dopamine'],
      traditionalUse: 'Used for centuries in Swiss folk medicine for melancholy and nervous disorders',
      modernResearch: 'Clinical studies show effectiveness comparable to SSRIs for mild-moderate depression',
      activeCompounds: ['Hypericin', 'Hyperforin', 'Flavonoids'],
      dosage: '300-900mg daily (0.3% hypericin extract)',
      preparation: 'Standardized extract, tea, tincture',
      safetyRating: 7,
      evidenceLevel: 'Strong',
      image: '/herbs/st-johns-wort.jpg',
      benefits: ['Mood enhancement', 'Anxiety reduction', 'Sleep improvement', 'Seasonal depression'],
      contraindications: ['SSRIs', 'Birth control', 'Blood thinners', 'Immunosuppressants']
    },
    {
      id: 'valerian',
      name: 'Valerian Root',
      scientificName: 'Valeriana officinalis',
      origin: 'Swiss Valleys',
      category: 'Sleep & Relaxation',
      targetNeurotransmitter: ['gaba'],
      traditionalUse: 'Traditional Swiss remedy for insomnia and nervous tension since medieval times',
      modernResearch: 'Studies confirm GABAergic activity and sleep-promoting effects',
      activeCompounds: ['Valerenic acid', 'Isovaleric acid', 'Valepotriates'],
      dosage: '300-600mg before bedtime',
      preparation: 'Root extract, tea, capsules',
      safetyRating: 8,
      evidenceLevel: 'Strong',
      image: '/herbs/valerian.jpg',
      benefits: ['Sleep induction', 'Anxiety relief', 'Muscle relaxation', 'Stress reduction'],
      contraindications: ['Sedatives', 'Alcohol', 'Surgery (discontinue 2 weeks prior)']
    },
    {
      id: 'lemon-balm',
      name: 'Lemon Balm',
      scientificName: 'Melissa officinalis',
      origin: 'Swiss Monasteries',
      category: 'Cognitive Support',
      targetNeurotransmitter: ['gaba', 'acetylcholine'],
      traditionalUse: 'Cultivated in Swiss monastery gardens for memory and calm focus',
      modernResearch: 'Research shows acetylcholinesterase inhibition and GABA enhancement',
      activeCompounds: ['Rosmarinic acid', 'Citronellal', 'Geraniol'],
      dosage: '300-500mg daily or 1-2 cups tea',
      preparation: 'Fresh leaves, dried herb tea, extract',
      safetyRating: 9,
      evidenceLevel: 'Moderate',
      image: '/herbs/lemon-balm.jpg',
      benefits: ['Memory enhancement', 'Calm focus', 'Digestive support', 'Antiviral properties'],
      contraindications: ['Thyroid medications', 'Sedatives']
    },
    {
      id: 'rhodiola',
      name: 'Rhodiola Rosea',
      scientificName: 'Rhodiola rosea',
      origin: 'Swiss High Alps',
      category: 'Adaptogen',
      targetNeurotransmitter: ['dopamine', 'serotonin'],
      traditionalUse: 'Used by Swiss mountain climbers for endurance and mental clarity',
      modernResearch: 'Adaptogenic properties support stress resilience and cognitive function',
      activeCompounds: ['Salidroside', 'Rosavin', 'Tyrosol'],
      dosage: '200-400mg daily (3% rosavins, 1% salidroside)',
      preparation: 'Standardized extract, root powder',
      safetyRating: 8,
      evidenceLevel: 'Strong',
      image: '/herbs/rhodiola.jpg',
      benefits: ['Stress adaptation', 'Mental fatigue reduction', 'Physical endurance', 'Mood support'],
      contraindications: ['Bipolar disorder', 'Autoimmune conditions']
    },
    {
      id: 'passionflower',
      name: 'Passionflower',
      scientificName: 'Passiflora incarnata',
      origin: 'Swiss Botanical Gardens',
      category: 'Anxiety Relief',
      targetNeurotransmitter: ['gaba'],
      traditionalUse: 'Introduced to Swiss herbal medicine for nervous disorders and restlessness',
      modernResearch: 'Clinical trials demonstrate anxiolytic effects via GABA modulation',
      activeCompounds: ['Vitexin', 'Chrysin', 'Apigenin'],
      dosage: '250-500mg daily or 1 cup tea',
      preparation: 'Dried aerial parts, extract, tea',
      safetyRating: 8,
      evidenceLevel: 'Moderate',
      image: '/herbs/passionflower.jpg',
      benefits: ['Anxiety reduction', 'Sleep quality', 'Muscle relaxation', 'Blood pressure support'],
      contraindications: ['Sedatives', 'MAOIs', 'Pregnancy']
    },
    {
      id: 'ginkgo',
      name: 'Ginkgo Biloba',
      scientificName: 'Ginkgo biloba',
      origin: 'Swiss Research Institutes',
      category: 'Cognitive Enhancement',
      targetNeurotransmitter: ['acetylcholine', 'dopamine'],
      traditionalUse: 'Adopted in Swiss phytotherapy for circulation and mental clarity',
      modernResearch: 'Extensive research on cognitive enhancement and neuroprotection',
      activeCompounds: ['Flavonoids', 'Terpenoids', 'Ginkgolides'],
      dosage: '120-240mg daily (24% flavonoids, 6% terpenoids)',
      preparation: 'Standardized leaf extract',
      safetyRating: 7,
      evidenceLevel: 'Strong',
      image: '/herbs/ginkgo.jpg',
      benefits: ['Memory improvement', 'Circulation enhancement', 'Antioxidant protection', 'Focus support'],
      contraindications: ['Blood thinners', 'Seizure disorders', 'Surgery']
    }
  ];

  const categories = ['all', 'Mood Support', 'Sleep & Relaxation', 'Cognitive Support', 'Adaptogen', 'Anxiety Relief', 'Cognitive Enhancement'];
  const neurotransmitters = ['all', 'serotonin', 'dopamine', 'gaba', 'acetylcholine'];

  const filteredRemedies = herbalRemedies.filter(remedy => {
    const matchesSearch = remedy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         remedy.scientificName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         remedy.traditionalUse.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || remedy.category === selectedCategory;
    const matchesNeurotransmitter = selectedNeurotransmitter === 'all' || 
                                   remedy.targetNeurotransmitter.includes(selectedNeurotransmitter);
    
    return matchesSearch && matchesCategory && matchesNeurotransmitter;
  });

  const neurotransmitterIcons = {
    serotonin: Heart,
    dopamine: Zap,
    gaba: Shield,
    acetylcholine: Target
  };

  const neurotransmitterColors = {
    serotonin: '#3b82f6',
    dopamine: '#10b981',
    gaba: '#a855f7',
    acetylcholine: '#f59e0b'
  };

  const getEvidenceBadgeColor = (level: string) => {
    switch (level) {
      case 'Strong': return 'bg-green-100 text-green-700';
      case 'Moderate': return 'bg-blue-100 text-blue-700';
      case 'Preliminary': return 'bg-yellow-100 text-yellow-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center">
              <Mountain className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Swiss Herbal Analysis</h1>
              <p className="text-xl text-gray-600">
                Traditional Swiss remedies with modern neurotransmitter research
              </p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Swiss Herbal Wisdom</h2>
                <p className="text-gray-600">
                  Discover time-tested remedies from Swiss alpine regions, validated by modern neuroscience
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Globe className="w-5 h-5 text-green-600" />
                <span className="text-sm text-gray-600">www.swissherbal.pl</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="cosmic-card mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search herbs, compounds, or uses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>

            {/* Neurotransmitter Filter */}
            <div className="relative">
              <Brain className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={selectedNeurotransmitter}
                onChange={(e) => setSelectedNeurotransmitter(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none"
              >
                {neurotransmitters.map(nt => (
                  <option key={nt} value={nt}>
                    {nt === 'all' ? 'All Neurotransmitters' : nt.charAt(0).toUpperCase() + nt.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </motion.div>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="mb-6"
        >
          <p className="text-gray-600">
            Found <span className="font-semibold text-gray-900">{filteredRemedies.length}</span> herbal remedies
          </p>
        </motion.div>

        {/* Herbal Remedies Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredRemedies.map((remedy, index) => (
            <motion.div
              key={remedy.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              className="cosmic-herbal-card"
            >
              {/* Herb Image Placeholder */}
              <div className="cosmic-herbal-image bg-gradient-to-br from-green-100 to-emerald-100 flex items-center justify-center">
                <Leaf className="w-16 h-16 text-green-600" />
              </div>

              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{remedy.name}</h3>
                  <p className="text-sm italic text-gray-600">{remedy.scientificName}</p>
                  <p className="text-xs text-green-600 font-medium">{remedy.origin}</p>
                </div>
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < remedy.safetyRating / 2 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Category and Evidence */}
              <div className="flex items-center justify-between mb-4">
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                  {remedy.category}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getEvidenceBadgeColor(remedy.evidenceLevel)}`}>
                  {remedy.evidenceLevel} Evidence
                </span>
              </div>

              {/* Target Neurotransmitters */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Target Systems:</h4>
                <div className="flex flex-wrap gap-2">
                  {remedy.targetNeurotransmitter.map((nt) => {
                    const Icon = neurotransmitterIcons[nt as keyof typeof neurotransmitterIcons];
                    const color = neurotransmitterColors[nt as keyof typeof neurotransmitterColors];
                    
                    return (
                      <div
                        key={nt}
                        className="flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-medium"
                        style={{ backgroundColor: color + '20', color }}
                      >
                        <Icon className="w-3 h-3" />
                        <span>{nt.charAt(0).toUpperCase() + nt.slice(1)}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Traditional Use */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  Traditional Use:
                </h4>
                <p className="text-sm text-gray-600">{remedy.traditionalUse}</p>
              </div>

              {/* Modern Research */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                  <BookOpen className="w-4 h-4 mr-1" />
                  Modern Research:
                </h4>
                <p className="text-sm text-gray-600">{remedy.modernResearch}</p>
              </div>

              {/* Dosage */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-1">Dosage:</h4>
                <p className="text-sm text-gray-600">{remedy.dosage}</p>
              </div>

              {/* Benefits */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Benefits:</h4>
                <div className="flex flex-wrap gap-1">
                  {remedy.benefits.slice(0, 3).map((benefit, i) => (
                    <span key={i} className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">
                      {benefit}
                    </span>
                  ))}
                  {remedy.benefits.length > 3 && (
                    <span className="px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs">
                      +{remedy.benefits.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Action Button */}
              <button className="w-full cosmic-button cosmic-button--primary text-sm py-3">
                <Award className="w-4 h-4 mr-2" />
                View Full Profile
              </button>
            </motion.div>
          ))}
        </motion.div>

        {/* No Results */}
        {filteredRemedies.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Leaf className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No herbs found</h3>
            <p className="text-gray-600">Try adjusting your search criteria or filters</p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default SwissHerbalPage;
