import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft, 
  RotateCcw,
  TrendingUp,
  Heart,
  Zap,
  Shield,
  Target
} from 'lucide-react';

interface Question {
  id: number;
  text: string;
  neurotransmitter: 'dopamine' | 'serotonin' | 'gaba' | 'acetylcholine';
}

const BravermanTestPage: React.FC = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [showResults, setShowResults] = useState(false);

  const questions: Question[] = [
    // Dopamine questions (motivation, energy, focus)
    { id: 1, text: "I am highly motivated and goal-oriented", neurotransmitter: 'dopamine' },
    { id: 2, text: "I have high energy levels throughout the day", neurotransmitter: 'dopamine' },
    { id: 3, text: "I enjoy taking risks and seeking new experiences", neurotransmitter: 'dopamine' },
    { id: 4, text: "I am competitive and driven to succeed", neurotransmitter: 'dopamine' },
    { id: 5, text: "I have good focus and concentration", neurotransmitter: 'dopamine' },
    
    // Serotonin questions (mood, sleep, social)
    { id: 6, text: "I generally feel happy and optimistic", neurotransmitter: 'serotonin' },
    { id: 7, text: "I sleep well and wake up refreshed", neurotransmitter: 'serotonin' },
    { id: 8, text: "I enjoy social interactions and feel connected to others", neurotransmitter: 'serotonin' },
    { id: 9, text: "I have good emotional stability", neurotransmitter: 'serotonin' },
    { id: 10, text: "I have a healthy appetite and eating patterns", neurotransmitter: 'serotonin' },
    
    // GABA questions (calm, relaxation, anxiety)
    { id: 11, text: "I feel calm and relaxed most of the time", neurotransmitter: 'gaba' },
    { id: 12, text: "I handle stress well without feeling overwhelmed", neurotransmitter: 'gaba' },
    { id: 13, text: "I rarely feel anxious or worried", neurotransmitter: 'gaba' },
    { id: 14, text: "I can easily unwind and relax after a busy day", neurotransmitter: 'gaba' },
    { id: 15, text: "I have good muscle relaxation and flexibility", neurotransmitter: 'gaba' },
    
    // Acetylcholine questions (memory, learning, attention)
    { id: 16, text: "I have excellent memory and recall abilities", neurotransmitter: 'acetylcholine' },
    { id: 17, text: "I learn new information quickly and easily", neurotransmitter: 'acetylcholine' },
    { id: 18, text: "I have sharp attention to detail", neurotransmitter: 'acetylcholine' },
    { id: 19, text: "I am creative and have good problem-solving skills", neurotransmitter: 'acetylcholine' },
    { id: 20, text: "I have good coordination and motor skills", neurotransmitter: 'acetylcholine' }
  ];

  const neurotransmitterInfo = {
    dopamine: {
      name: 'Dopamine Dominant',
      icon: Zap,
      color: '#10b981',
      gradient: 'linear-gradient(135deg, #34d399 0%, #059669 100%)',
      description: 'You are driven, energetic, and goal-oriented. You thrive on challenges and new experiences.',
      traits: ['High motivation', 'Goal-oriented', 'Risk-taking', 'Competitive', 'Energetic'],
      supplements: ['L-Tyrosine', 'Mucuna Pruriens', 'Iron', 'B6']
    },
    serotonin: {
      name: 'Serotonin Dominant',
      icon: Heart,
      color: '#3b82f6',
      gradient: 'linear-gradient(135deg, #60a5fa 0%, #2563eb 100%)',
      description: 'You are emotionally stable, social, and optimistic. You value harmony and connection.',
      traits: ['Emotional stability', 'Social', 'Optimistic', 'Good sleep', 'Empathetic'],
      supplements: ['5-HTP', 'Tryptophan', 'B6', 'Magnesium']
    },
    gaba: {
      name: 'GABA Dominant',
      icon: Shield,
      color: '#a855f7',
      gradient: 'linear-gradient(135deg, #c084fc 0%, #7c3aed 100%)',
      description: 'You are calm, relaxed, and handle stress well. You prefer stability and peace.',
      traits: ['Calm demeanor', 'Stress resilient', 'Relaxed', 'Stable', 'Patient'],
      supplements: ['L-Theanine', 'GABA', 'Magnesium', 'Taurine']
    },
    acetylcholine: {
      name: 'Acetylcholine Dominant',
      icon: Target,
      color: '#f59e0b',
      gradient: 'linear-gradient(135deg, #fbbf24 0%, #d97706 100%)',
      description: 'You are intelligent, creative, and detail-oriented. You excel at learning and memory.',
      traits: ['Sharp memory', 'Quick learner', 'Detail-oriented', 'Creative', 'Coordinated'],
      supplements: ['Alpha-GPC', 'CDP-Choline', 'Phosphatidylserine', 'Huperzine A']
    }
  };

  const handleAnswer = (value: number) => {
    setAnswers(prev => ({ ...prev, [questions[currentQuestion].id]: value }));
  };

  const nextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    } else {
      calculateResults();
    }
  };

  const prevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = () => {
    const scores = {
      dopamine: 0,
      serotonin: 0,
      gaba: 0,
      acetylcholine: 0
    };

    questions.forEach(question => {
      const answer = answers[question.id] || 0;
      scores[question.neurotransmitter] += answer;
    });

    // Normalize scores to percentages
    const maxScore = 25; // 5 questions × 5 points max each
    const normalizedScores = {
      dopamine: Math.round((scores.dopamine / maxScore) * 100),
      serotonin: Math.round((scores.serotonin / maxScore) * 100),
      gaba: Math.round((scores.gaba / maxScore) * 100),
      acetylcholine: Math.round((scores.acetylcholine / maxScore) * 100)
    };

    setResults(normalizedScores);
    setShowResults(true);
  };

  const [results, setResults] = useState<Record<string, number>>({});

  const resetTest = () => {
    setCurrentQuestion(0);
    setAnswers({});
    setShowResults(false);
    setResults({});
  };

  const getDominantType = () => {
    const maxScore = Math.max(...Object.values(results));
    const dominantType = Object.entries(results).find(([_, score]) => score === maxScore)?.[0];
    return dominantType as keyof typeof neurotransmitterInfo;
  };

  const progress = ((currentQuestion + 1) / questions.length) * 100;

  if (showResults) {
    const dominantType = getDominantType();
    const dominant = neurotransmitterInfo[dominantType];

    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Your Braverman Test Results</h1>
            <p className="text-xl text-gray-600">Discover your dominant neurotransmitter type</p>
          </motion.div>

          {/* Dominant Type Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="cosmic-card mb-8 text-center"
          >
            <div 
              className="w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center text-white"
              style={{ background: dominant.gradient }}
            >
              <dominant.icon className="w-12 h-12" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">{dominant.name}</h2>
            <p className="text-lg text-gray-600 mb-6">{dominant.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mb-6">
              {dominant.traits.map((trait, index) => (
                <span
                  key={index}
                  className="px-3 py-2 text-sm font-medium rounded-full"
                  style={{ 
                    backgroundColor: dominant.color + '20',
                    color: dominant.color
                  }}
                >
                  {trait}
                </span>
              ))}
            </div>
          </motion.div>

          {/* All Scores */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {Object.entries(results).map(([type, score]) => {
              const info = neurotransmitterInfo[type as keyof typeof neurotransmitterInfo];
              const Icon = info.icon;
              
              return (
                <div key={type} className="cosmic-card text-center">
                  <div 
                    className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-white"
                    style={{ background: info.gradient }}
                  >
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{info.name}</h3>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{score}%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full transition-all duration-1000"
                      style={{ 
                        width: `${score}%`,
                        background: info.gradient
                      }}
                    />
                  </div>
                </div>
              );
            })}
          </motion.div>

          {/* Recommended Supplements */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="cosmic-card"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Recommended Supplements</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {dominant.supplements.map((supplement, index) => (
                <div
                  key={index}
                  className="p-4 rounded-xl border-2 text-center hover:shadow-lg transition-all"
                  style={{ borderColor: dominant.color + '40' }}
                >
                  <div className="font-medium text-gray-900">{supplement}</div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Actions */}
          <div className="flex justify-center space-x-4 mt-8">
            <button
              onClick={resetTest}
              className="cosmic-button cosmic-button--outline flex items-center space-x-2"
            >
              <RotateCcw className="w-5 h-5" />
              <span>Retake Test</span>
            </button>
            <button className="cosmic-button cosmic-button--primary flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Get Personalized Plan</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Brain className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Braverman Neurotransmitter Assessment</h1>
          <p className="text-xl text-gray-600">Discover your dominant neurotransmitter type and optimize your brain health</p>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Question {currentQuestion + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <motion.div
              className="h-3 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>

        {/* Question Card */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestion}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="cosmic-test-interface mb-8"
          >
            <div className="cosmic-test-question">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                {questions[currentQuestion].text}
              </h2>
              
              <div className="cosmic-test-options">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => handleAnswer(value)}
                    className={`cosmic-test-option ${
                      answers[questions[currentQuestion].id] === value ? 'cosmic-test-option--selected' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span>
                        {value === 1 && 'Strongly Disagree'}
                        {value === 2 && 'Disagree'}
                        {value === 3 && 'Neutral'}
                        {value === 4 && 'Agree'}
                        {value === 5 && 'Strongly Agree'}
                      </span>
                      {answers[questions[currentQuestion].id] === value && (
                        <CheckCircle className="w-5 h-5 text-primary-600" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={prevQuestion}
            disabled={currentQuestion === 0}
            className="cosmic-button cosmic-button--outline flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Previous</span>
          </button>
          
          <button
            onClick={nextQuestion}
            disabled={!answers[questions[currentQuestion].id]}
            className="cosmic-button cosmic-button--primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>{currentQuestion === questions.length - 1 ? 'Get Results' : 'Next'}</span>
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default BravermanTestPage;
