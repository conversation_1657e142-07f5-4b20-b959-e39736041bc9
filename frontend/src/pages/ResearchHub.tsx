import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ResearchPage from './ResearchPage';
import { SupplementResearchInterface } from '@/components/supplement/SupplementResearchInterface';

const ResearchHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'general' | 'supplement'>('general');

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="mb-6">
        <TabsList>
          <TabsTrigger value="general">General Research</TabsTrigger>
          <TabsTrigger value="supplement">Supplement Research</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general">
          <ResearchPage />
        </TabsContent>
        
        <TabsContent value="supplement">
          <SupplementResearchInterface />
        </TabsContent>
      </Tabs>
      
      <div className="mt-6 text-sm text-gray-500">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span>Recent searches</span>
        </div>
        {/* Search history will be implemented in next step */}
      </div>
    </div>
  );
};

export default ResearchHub;