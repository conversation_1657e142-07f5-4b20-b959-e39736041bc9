import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Target, 
  TrendingUp, 
  Brain, 
  Pill, 
  AlertCircle,
  Activity,
  Trophy,
  Focus,
  Gamepad2,
  Coffee,
  Dumbbell
} from 'lucide-react';

const DopaminePage: React.FC = () => {
  const [currentLevel] = useState(65); // Simulated current dopamine level
  const [selectedTab, setSelectedTab] = useState('overview');

  const dopamineData = {
    name: 'Dopamine',
    subtitle: 'The Motivation Neurotransmitter',
    description: 'Dopamine drives motivation, reward-seeking behavior, focus, and motor control. It\'s essential for goal achievement, pleasure, and maintaining drive in daily activities.',
    currentLevel: currentLevel,
    optimalRange: [40, 60],
    color: '#10b981',
    gradient: 'linear-gradient(135deg, #34d399 0%, #059669 100%)'
  };

  const functions = [
    { icon: Target, title: 'Motivation & Drive', description: 'Fuels goal-directed behavior and ambition' },
    { icon: Focus, title: 'Focus & Attention', description: 'Enhances concentration and cognitive control' },
    { icon: Activity, title: 'Motor Control', description: 'Coordinates movement and physical coordination' },
    { icon: Trophy, title: 'Reward Processing', description: 'Processes pleasure and satisfaction' },
    { icon: Brain, title: 'Working Memory', description: 'Supports short-term memory and planning' },
    { icon: TrendingUp, title: 'Learning', description: 'Facilitates skill acquisition and adaptation' }
  ];

  const supplements = [
    {
      name: 'L-Tyrosine',
      dosage: '500-2000mg daily',
      timing: 'Morning, empty stomach',
      mechanism: 'Precursor to dopamine via L-DOPA',
      evidence: 'Strong',
      safety: 'Generally safe, avoid with MAOIs'
    },
    {
      name: 'Mucuna Pruriens',
      dosage: '100-500mg daily',
      timing: 'Morning with food',
      mechanism: 'Natural L-DOPA source',
      evidence: 'Strong',
      safety: 'Monitor with Parkinson\'s meds'
    },
    {
      name: 'Rhodiola Rosea',
      dosage: '200-400mg daily',
      timing: 'Morning, empty stomach',
      mechanism: 'Supports dopamine receptor sensitivity',
      evidence: 'Moderate',
      safety: 'Generally safe, may cause agitation'
    },
    {
      name: 'Iron',
      dosage: '8-18mg daily',
      timing: 'With vitamin C',
      mechanism: 'Cofactor for dopamine synthesis',
      evidence: 'Strong',
      safety: 'Monitor levels, avoid excess'
    }
  ];

  const naturalSources = [
    { name: 'Lean Proteins', amount: 'High tyrosine', benefit: 'Direct precursor support' },
    { name: 'Almonds', amount: 'Tyrosine + healthy fats', benefit: 'Sustained production' },
    { name: 'Apples', amount: 'Quercetin + tyrosine', benefit: 'Antioxidant protection' },
    { name: 'Beets', amount: 'Nitrates + folate', benefit: 'Enhanced circulation' },
    { name: 'Green Tea', amount: 'L-theanine + caffeine', benefit: 'Balanced stimulation' },
    { name: 'Dark Chocolate', amount: 'Phenylethylamine', benefit: 'Mood enhancement' }
  ];

  const lifestyle = [
    { icon: Dumbbell, title: 'Regular Exercise', description: 'Increases dopamine receptor density' },
    { icon: Trophy, title: 'Goal Setting', description: 'Activates reward pathways naturally' },
    { icon: Coffee, title: 'Strategic Caffeine', description: 'Enhances dopamine signaling when used wisely' },
    { icon: Gamepad2, title: 'Novel Experiences', description: 'Stimulates dopamine through novelty' }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Brain },
    { id: 'supplements', label: 'Supplements', icon: Pill },
    { id: 'nutrition', label: 'Nutrition', icon: Coffee },
    { id: 'lifestyle', label: 'Lifestyle', icon: Dumbbell }
  ];

  const isLow = currentLevel < dopamineData.optimalRange[0];
  const isHigh = currentLevel > dopamineData.optimalRange[1];
  const isOptimal = !isLow && !isHigh;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="cosmic-neurotransmitter-card cosmic-neurotransmitter-card--dopamine">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div 
                  className="w-16 h-16 rounded-2xl flex items-center justify-center text-white"
                  style={{ background: dopamineData.gradient }}
                >
                  <Zap className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{dopamineData.name}</h1>
                  <p className="text-lg text-gray-600">{dopamineData.subtitle}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-3xl font-bold text-gray-900 mb-1">{currentLevel}%</div>
                <div className={`text-sm font-medium ${isOptimal ? 'text-green-600' : isLow ? 'text-red-600' : 'text-orange-600'}`}>
                  {isOptimal ? 'Optimal' : isLow ? 'Below Optimal' : 'Above Optimal'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Target: {dopamineData.optimalRange[0]}-{dopamineData.optimalRange[1]}%
                </div>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed">{dopamineData.description}</p>
          </div>
        </motion.div>

        {/* Status Alert */}
        {!isOptimal && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`mb-8 p-6 rounded-2xl border-2 ${
              isLow 
                ? 'bg-red-50 border-red-200' 
                : 'bg-orange-50 border-orange-200'
            }`}
          >
            <div className="flex items-center space-x-3">
              <AlertCircle className={`w-6 h-6 ${isLow ? 'text-red-600' : 'text-orange-600'}`} />
              <div>
                <h3 className={`font-semibold ${isLow ? 'text-red-900' : 'text-orange-900'}`}>
                  {isLow ? 'Low Dopamine Detected' : 'Elevated Dopamine Levels'}
                </h3>
                <p className={`text-sm ${isLow ? 'text-red-700' : 'text-orange-700'}`}>
                  {isLow 
                    ? 'Consider L-Tyrosine supplementation and motivation-building activities.'
                    : 'Monitor for overstimulation and consider reducing stimulants and dopaminergic supplements.'
                  }
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white p-1 rounded-xl border border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                  selectedTab === tab.id
                    ? 'bg-green-100 text-green-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={selectedTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {functions.map((func, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <func.icon className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{func.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{func.description}</p>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'supplements' && (
            <div className="space-y-6">
              {supplements.map((supplement, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{supplement.name}</h3>
                      <p className="text-gray-600">{supplement.mechanism}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600 mb-1">Evidence: {supplement.evidence}</div>
                      <div className="text-xs text-gray-500">{supplement.dosage}</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Timing</div>
                      <div className="text-gray-600">{supplement.timing}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Safety</div>
                      <div className="text-gray-600">{supplement.safety}</div>
                    </div>
                    <div>
                      <button className="w-full cosmic-button text-sm py-2 text-white" style={{ background: dopamineData.gradient }}>
                        Add to Stack
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'nutrition' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {naturalSources.map((source, index) => (
                <div key={index} className="cosmic-card">
                  <h3 className="font-semibold text-gray-900 mb-2">{source.name}</h3>
                  <div className="text-sm text-gray-600 mb-2">{source.amount}</div>
                  <div className="text-sm text-green-600 font-medium">{source.benefit}</div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'lifestyle' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {lifestyle.map((item, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <item.icon className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{item.title}</h3>
                  </div>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default DopaminePage;
