import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON>, 
  Smile, 
  <PERSON>, 
  Pill, 
  AlertCircle,
  TrendingUp,
  Clock,
  Brain,
  Utensils,
  Sun,
  Activity
} from 'lucide-react';

const SerotoninPage: React.FC = () => {
  const [currentLevel] = useState(45); // Simulated current serotonin level
  const [selectedTab, setSelectedTab] = useState('overview');

  const serotoninData = {
    name: 'Serotonin',
    subtitle: 'The Happiness Neurotransmitter',
    description: 'Serotonin is crucial for mood regulation, sleep quality, appetite control, and social behavior. Often called the "happiness neurotransmitter," it plays a vital role in emotional well-being.',
    currentLevel: currentLevel,
    optimalRange: [40, 60],
    color: '#3b82f6',
    gradient: 'linear-gradient(135deg, #60a5fa 0%, #2563eb 100%)'
  };

  const functions = [
    { icon: Smile, title: 'Mood Regulation', description: 'Controls emotional stability and happiness' },
    { icon: Moon, title: 'Sleep Cycles', description: 'Regulates circadian rhythms and sleep quality' },
    { icon: Utensils, title: 'Appetite Control', description: 'Manages hunger and satiety signals' },
    { icon: Users, title: 'Social Behavior', description: 'Influences empathy and social connections' },
    { icon: Brain, title: 'Cognitive Function', description: 'Affects memory and decision-making' },
    { icon: Activity, title: 'Pain Perception', description: 'Modulates pain sensitivity and tolerance' }
  ];

  const supplements = [
    {
      name: '5-HTP',
      dosage: '50-300mg daily',
      timing: 'Evening with food',
      mechanism: 'Direct serotonin precursor',
      evidence: 'Strong',
      safety: 'Generally safe, avoid with SSRIs'
    },
    {
      name: 'L-Tryptophan',
      dosage: '500-2000mg daily',
      timing: 'Empty stomach or with carbs',
      mechanism: 'Converts to 5-HTP then serotonin',
      evidence: 'Strong',
      safety: 'Very safe, competes with other amino acids'
    },
    {
      name: 'Vitamin B6',
      dosage: '25-100mg daily',
      timing: 'With meals',
      mechanism: 'Cofactor for serotonin synthesis',
      evidence: 'Strong',
      safety: 'Safe, avoid high doses long-term'
    },
    {
      name: 'Magnesium',
      dosage: '200-400mg daily',
      timing: 'Evening',
      mechanism: 'Supports serotonin receptor function',
      evidence: 'Moderate',
      safety: 'Very safe, may cause loose stools'
    }
  ];

  const naturalSources = [
    { name: 'Turkey', amount: 'High tryptophan', benefit: 'Direct precursor' },
    { name: 'Salmon', amount: 'Omega-3 + tryptophan', benefit: 'Dual support' },
    { name: 'Eggs', amount: 'Complete protein', benefit: 'Balanced amino acids' },
    { name: 'Pumpkin Seeds', amount: 'Tryptophan + magnesium', benefit: 'Synergistic nutrients' },
    { name: 'Dark Chocolate', amount: 'Moderate tryptophan', benefit: 'Mood enhancement' },
    { name: 'Bananas', amount: 'Tryptophan + B6', benefit: 'Natural cofactors' }
  ];

  const lifestyle = [
    { icon: Sun, title: 'Morning Sunlight', description: 'Supports circadian rhythm regulation' },
    { icon: Activity, title: 'Regular Exercise', description: 'Increases tryptophan availability' },
    { icon: Users, title: 'Social Connection', description: 'Naturally boosts serotonin levels' },
    { icon: Moon, title: 'Quality Sleep', description: 'Essential for serotonin production' }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Brain },
    { id: 'supplements', label: 'Supplements', icon: Pill },
    { id: 'nutrition', label: 'Nutrition', icon: Utensils },
    { id: 'lifestyle', label: 'Lifestyle', icon: Sun }
  ];

  const isLow = currentLevel < serotoninData.optimalRange[0];
  const isHigh = currentLevel > serotoninData.optimalRange[1];
  const isOptimal = !isLow && !isHigh;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="cosmic-neurotransmitter-card cosmic-neurotransmitter-card--serotonin">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div 
                  className="w-16 h-16 rounded-2xl flex items-center justify-center text-white"
                  style={{ background: serotoninData.gradient }}
                >
                  <Heart className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{serotoninData.name}</h1>
                  <p className="text-lg text-gray-600">{serotoninData.subtitle}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-3xl font-bold text-gray-900 mb-1">{currentLevel}%</div>
                <div className={`text-sm font-medium ${isOptimal ? 'text-green-600' : isLow ? 'text-red-600' : 'text-orange-600'}`}>
                  {isOptimal ? 'Optimal' : isLow ? 'Below Optimal' : 'Above Optimal'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Target: {serotoninData.optimalRange[0]}-{serotoninData.optimalRange[1]}%
                </div>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed">{serotoninData.description}</p>
          </div>
        </motion.div>

        {/* Status Alert */}
        {!isOptimal && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`mb-8 p-6 rounded-2xl border-2 ${
              isLow 
                ? 'bg-red-50 border-red-200' 
                : 'bg-orange-50 border-orange-200'
            }`}
          >
            <div className="flex items-center space-x-3">
              <AlertCircle className={`w-6 h-6 ${isLow ? 'text-red-600' : 'text-orange-600'}`} />
              <div>
                <h3 className={`font-semibold ${isLow ? 'text-red-900' : 'text-orange-900'}`}>
                  {isLow ? 'Low Serotonin Detected' : 'Elevated Serotonin Levels'}
                </h3>
                <p className={`text-sm ${isLow ? 'text-red-700' : 'text-orange-700'}`}>
                  {isLow 
                    ? 'Consider 5-HTP supplementation and lifestyle modifications to support serotonin production.'
                    : 'Monitor for serotonin syndrome symptoms and consider reducing serotonergic supplements.'
                  }
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white p-1 rounded-xl border border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                  selectedTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={selectedTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {functions.map((func, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <func.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{func.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{func.description}</p>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'supplements' && (
            <div className="space-y-6">
              {supplements.map((supplement, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{supplement.name}</h3>
                      <p className="text-gray-600">{supplement.mechanism}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-blue-600 mb-1">Evidence: {supplement.evidence}</div>
                      <div className="text-xs text-gray-500">{supplement.dosage}</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Timing</div>
                      <div className="text-gray-600">{supplement.timing}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Safety</div>
                      <div className="text-gray-600">{supplement.safety}</div>
                    </div>
                    <div>
                      <button className="w-full cosmic-button cosmic-button--primary text-sm py-2">
                        Add to Stack
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'nutrition' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {naturalSources.map((source, index) => (
                <div key={index} className="cosmic-card">
                  <h3 className="font-semibold text-gray-900 mb-2">{source.name}</h3>
                  <div className="text-sm text-gray-600 mb-2">{source.amount}</div>
                  <div className="text-sm text-blue-600 font-medium">{source.benefit}</div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'lifestyle' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {lifestyle.map((item, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                      <item.icon className="w-6 h-6 text-yellow-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{item.title}</h3>
                  </div>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default SerotoninPage;
