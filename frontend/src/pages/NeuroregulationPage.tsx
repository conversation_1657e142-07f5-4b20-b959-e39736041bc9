import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Activity, 
  TrendingUp, 
  Users, 
  Zap, 
  Target,
  BarChart3,
  Lightbulb,
  Heart,
  Shield
} from 'lucide-react';
import NeurotransmitterCard from '@/components/neuro/NeurotransmitterCard';
import { neurotransmitterProfiles } from '@/data/neurotransmitterData';

const NeuroregulationPage: React.FC = () => {
  const [selectedNeurotransmitter, setSelectedNeurotransmitter] = useState<string | null>(null);
  const [neurotransmitterLevels, setNeurotransmitterLevels] = useState({
    serotonin: 45,
    dopamine: 65,
    gaba: 35,
    acetylcholine: 55
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setNeurotransmitterLevels(prev => ({
        serotonin: Math.max(20, Math.min(80, prev.serotonin + (Math.random() - 0.5) * 2)),
        dopamine: Math.max(20, Math.min(80, prev.dopamine + (Math.random() - 0.5) * 2)),
        gaba: Math.max(20, Math.min(80, prev.gaba + (Math.random() - 0.5) * 2)),
        acetylcholine: Math.max(20, Math.min(80, prev.acetylcholine + (Math.random() - 0.5) * 2))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const overallBalance = Object.values(neurotransmitterLevels).reduce((acc, val) => acc + val, 0) / 4;
  const balanceStatus = overallBalance >= 40 && overallBalance <= 60 ? 'Balanced' : 
                       overallBalance < 40 ? 'Needs Support' : 'Overactive';

  const stats = [
    {
      label: 'Overall Balance',
      value: `${Math.round(overallBalance)}%`,
      status: balanceStatus,
      icon: Activity,
      color: overallBalance >= 40 && overallBalance <= 60 ? '#10b981' : '#f59e0b'
    },
    {
      label: 'Optimal Systems',
      value: `${Object.values(neurotransmitterLevels).filter(level => level >= 40 && level <= 60).length}/4`,
      status: 'Active',
      icon: Target,
      color: '#3b82f6'
    },
    {
      label: 'Mood Stability',
      value: neurotransmitterLevels.serotonin >= 40 && neurotransmitterLevels.serotonin <= 60 ? 'Stable' : 'Variable',
      status: 'Tracking',
      icon: Heart,
      color: '#ec4899'
    },
    {
      label: 'Cognitive Function',
      value: neurotransmitterLevels.acetylcholine >= 40 && neurotransmitterLevels.acetylcholine <= 60 ? 'Sharp' : 'Needs Support',
      status: 'Monitoring',
      icon: Lightbulb,
      color: '#f59e0b'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center">
              <Brain className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Neuroregulation Dashboard</h1>
              <p className="text-xl text-gray-600">
                Comprehensive neurotransmitter balance and optimization
              </p>
            </div>
          </div>
          
          <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">System Overview</h2>
                <p className="text-gray-600">
                  Real-time monitoring of your neurotransmitter systems for optimal brain health
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">Live Data</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <div key={index} className="cosmic-card">
              <div className="flex items-center justify-between mb-4">
                <div 
                  className="w-12 h-12 rounded-xl flex items-center justify-center"
                  style={{ backgroundColor: stat.color + '20' }}
                >
                  <stat.icon className="w-6 h-6" style={{ color: stat.color }} />
                </div>
                <span 
                  className="text-xs font-medium px-2 py-1 rounded-full"
                  style={{ 
                    backgroundColor: stat.color + '20',
                    color: stat.color
                  }}
                >
                  {stat.status}
                </span>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Neurotransmitter Cards Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"
        >
          {neurotransmitterProfiles.map((neurotransmitter, index) => (
            <NeurotransmitterCard
              key={neurotransmitter.id}
              neurotransmitter={neurotransmitter}
              currentLevel={neurotransmitterLevels[neurotransmitter.id as keyof typeof neurotransmitterLevels]}
              onDetailsClick={() => setSelectedNeurotransmitter(neurotransmitter.id)}
            />
          ))}
        </motion.div>

        {/* Insights and Recommendations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
        >
          {/* Balance Insights */}
          <div className="cosmic-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Balance Insights</h3>
                <p className="text-gray-600">AI-powered analysis of your neurotransmitter patterns</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Dopamine Elevation</span>
                </div>
                <p className="text-sm text-blue-800">
                  Your dopamine levels are above optimal range. Consider reducing stimulants and adding GABA support.
                </p>
              </div>

              <div className="p-4 bg-orange-50 rounded-xl border border-orange-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="w-5 h-5 text-orange-600" />
                  <span className="font-medium text-orange-900">GABA Support Needed</span>
                </div>
                <p className="text-sm text-orange-800">
                  Low GABA levels detected. L-Theanine and magnesium supplementation recommended.
                </p>
              </div>

              <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Users className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">Serotonin & Acetylcholine Balanced</span>
                </div>
                <p className="text-sm text-green-800">
                  Your mood and cognitive systems are well-balanced. Maintain current lifestyle factors.
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="cosmic-card">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
                <p className="text-gray-600">Immediate steps to optimize your neurotransmitter balance</p>
              </div>
            </div>

            <div className="space-y-3">
              <button className="w-full p-4 text-left bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl border border-purple-200 hover:border-purple-300 transition-colors">
                <div className="font-medium text-purple-900 mb-1">Take Braverman Assessment</div>
                <div className="text-sm text-purple-700">Identify your dominant neurotransmitter type</div>
              </button>

              <button className="w-full p-4 text-left bg-gradient-to-r from-green-50 to-teal-50 rounded-xl border border-green-200 hover:border-green-300 transition-colors">
                <div className="font-medium text-green-900 mb-1">Get Personalized Supplements</div>
                <div className="text-sm text-green-700">AI-recommended supplements for your profile</div>
              </button>

              <button className="w-full p-4 text-left bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl border border-orange-200 hover:border-orange-300 transition-colors">
                <div className="font-medium text-orange-900 mb-1">Explore Swiss Herbal Database</div>
                <div className="text-sm text-orange-700">Traditional remedies for neurotransmitter support</div>
              </button>

              <button className="w-full p-4 text-left bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 hover:border-blue-300 transition-colors">
                <div className="font-medium text-blue-900 mb-1">View Interaction Matrix</div>
                <div className="text-sm text-blue-700">Check supplement interactions and synergies</div>
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default NeuroregulationPage;
