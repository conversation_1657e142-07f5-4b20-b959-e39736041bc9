import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  Brain, 
  BookOpen, 
  Lightbulb, 
  Pill, 
  AlertCircle,
  Activity,
  Zap,
  Eye,
  Coffee,
  Egg,
  Fish
} from 'lucide-react';

const AcetylcholinePage: React.FC = () => {
  const [currentLevel] = useState(55); // Simulated current acetylcholine level
  const [selectedTab, setSelectedTab] = useState('overview');

  const acetylcholineData = {
    name: 'Acetylcholine',
    subtitle: 'The Learning Neurotransmitter',
    description: 'Acetylcholine is essential for memory formation, learning, attention, and cognitive processing. It\'s the key neurotransmitter for neuroplasticity and mental sharpness.',
    currentLevel: currentLevel,
    optimalRange: [40, 60],
    color: '#f59e0b',
    gradient: 'linear-gradient(135deg, #fbbf24 0%, #d97706 100%)'
  };

  const functions = [
    { icon: Brain, title: 'Memory Formation', description: 'Critical for encoding and retrieving memories' },
    { icon: BookO<PERSON>, title: 'Learning Enhancement', description: 'Facilitates acquisition of new information and skills' },
    { icon: Target, title: 'Attention & Focus', description: 'Maintains sustained attention and concentration' },
    { icon: Lightbulb, title: 'Neuroplasticity', description: 'Promotes brain adaptability and neural connections' },
    { icon: Activity, title: 'Motor Control', description: 'Coordinates muscle movement and fine motor skills' },
    { icon: Eye, title: 'Sensory Processing', description: 'Enhances sensory perception and processing' }
  ];

  const supplements = [
    {
      name: 'Alpha-GPC',
      dosage: '300-600mg daily',
      timing: 'Morning with food',
      mechanism: 'Highly bioavailable choline source for ACh synthesis',
      evidence: 'Strong',
      safety: 'Very safe, well-tolerated'
    },
    {
      name: 'CDP-Choline (Citicoline)',
      dosage: '250-500mg daily',
      timing: 'Morning, empty stomach',
      mechanism: 'Choline source + phospholipid membrane support',
      evidence: 'Strong',
      safety: 'Very safe, neuroprotective'
    },
    {
      name: 'Choline Bitartrate',
      dosage: '500-1000mg daily',
      timing: 'With meals',
      mechanism: 'Basic choline supplementation',
      evidence: 'Moderate',
      safety: 'Safe, may cause fishy odor'
    },
    {
      name: 'Phosphatidylserine',
      dosage: '100-300mg daily',
      timing: 'With meals',
      mechanism: 'Supports ACh receptor function and membrane health',
      evidence: 'Strong',
      safety: 'Very safe, cognitive enhancing'
    },
    {
      name: 'Huperzine A',
      dosage: '50-200mcg daily',
      timing: 'Morning, empty stomach',
      mechanism: 'Acetylcholinesterase inhibitor (increases ACh levels)',
      evidence: 'Strong',
      safety: 'Generally safe, cycle usage recommended'
    },
    {
      name: 'Bacopa Monnieri',
      dosage: '300-600mg daily',
      timing: 'With meals',
      mechanism: 'Enhances ACh activity and memory consolidation',
      evidence: 'Strong',
      safety: 'Very safe, adaptogenic herb'
    }
  ];

  const naturalSources = [
    { name: 'Eggs (Yolks)', amount: 'Highest choline content', benefit: 'Direct ACh precursor' },
    { name: 'Fish & Seafood', amount: 'High choline + omega-3', benefit: 'Synergistic brain support' },
    { name: 'Meat & Poultry', amount: 'Complete choline profile', benefit: 'Sustained ACh production' },
    { name: 'Nuts & Seeds', amount: 'Choline + healthy fats', benefit: 'Brain membrane support' },
    { name: 'Cruciferous Vegetables', amount: 'Moderate choline', benefit: 'Antioxidant protection' },
    { name: 'Whole Grains', amount: 'B-vitamins + choline', benefit: 'Cofactor support' }
  ];

  const lifestyle = [
    { icon: BookOpen, title: 'Continuous Learning', description: 'Engage in new skills and challenging mental activities' },
    { icon: Coffee, title: 'Strategic Caffeine', description: 'Moderate caffeine enhances ACh receptor sensitivity' },
    { icon: Activity, title: 'Physical Exercise', description: 'Increases BDNF and supports ACh neuron health' },
    { icon: Brain, title: 'Mental Challenges', description: 'Puzzles, games, and complex tasks stimulate ACh' }
  ];

  const cognitiveSymptoms = [
    'Memory problems and forgetfulness',
    'Difficulty learning new information',
    'Poor concentration and focus',
    'Mental fatigue and brain fog',
    'Reduced creativity and problem-solving',
    'Slower information processing'
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Brain },
    { id: 'supplements', label: 'Supplements', icon: Pill },
    { id: 'nutrition', label: 'Nutrition', icon: Egg },
    { id: 'lifestyle', label: 'Lifestyle', icon: BookOpen }
  ];

  const isLow = currentLevel < acetylcholineData.optimalRange[0];
  const isHigh = currentLevel > acetylcholineData.optimalRange[1];
  const isOptimal = !isLow && !isHigh;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="cosmic-neurotransmitter-card cosmic-neurotransmitter-card--acetylcholine">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div 
                  className="w-16 h-16 rounded-2xl flex items-center justify-center text-white"
                  style={{ background: acetylcholineData.gradient }}
                >
                  <Target className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{acetylcholineData.name}</h1>
                  <p className="text-lg text-gray-600">{acetylcholineData.subtitle}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-3xl font-bold text-gray-900 mb-1">{currentLevel}%</div>
                <div className={`text-sm font-medium ${isOptimal ? 'text-green-600' : isLow ? 'text-red-600' : 'text-orange-600'}`}>
                  {isOptimal ? 'Optimal' : isLow ? 'Below Optimal' : 'Above Optimal'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Target: {acetylcholineData.optimalRange[0]}-{acetylcholineData.optimalRange[1]}%
                </div>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed">{acetylcholineData.description}</p>
          </div>
        </motion.div>

        {/* Status Alert */}
        {!isOptimal && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`mb-8 p-6 rounded-2xl border-2 ${
              isLow 
                ? 'bg-red-50 border-red-200' 
                : 'bg-orange-50 border-orange-200'
            }`}
          >
            <div className="flex items-center space-x-3">
              <AlertCircle className={`w-6 h-6 ${isLow ? 'text-red-600' : 'text-orange-600'}`} />
              <div>
                <h3 className={`font-semibold ${isLow ? 'text-red-900' : 'text-orange-900'}`}>
                  {isLow ? 'Low Acetylcholine Activity' : 'Elevated Acetylcholine Levels'}
                </h3>
                <p className={`text-sm ${isLow ? 'text-red-700' : 'text-orange-700'}`}>
                  {isLow 
                    ? 'Consider Alpha-GPC or CDP-Choline supplementation for cognitive enhancement.'
                    : 'Monitor for overstimulation and consider reducing cholinergic supplements.'
                  }
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Low ACh Symptoms */}
        {isLow && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8 cosmic-card"
          >
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
              Common Low Acetylcholine Symptoms
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {cognitiveSymptoms.map((symptom, index) => (
                <div key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-3" />
                  {symptom}
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white p-1 rounded-xl border border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all ${
                  selectedTab === tab.id
                    ? 'bg-orange-100 text-orange-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={selectedTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {functions.map((func, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                      <func.icon className="w-6 h-6 text-orange-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{func.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{func.description}</p>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'supplements' && (
            <div className="space-y-6">
              {supplements.map((supplement, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{supplement.name}</h3>
                      <p className="text-gray-600">{supplement.mechanism}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-orange-600 mb-1">Evidence: {supplement.evidence}</div>
                      <div className="text-xs text-gray-500">{supplement.dosage}</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Timing</div>
                      <div className="text-gray-600">{supplement.timing}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Safety</div>
                      <div className="text-gray-600">{supplement.safety}</div>
                    </div>
                    <div>
                      <button className="w-full cosmic-button text-sm py-2 text-white" style={{ background: acetylcholineData.gradient }}>
                        Add to Stack
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'nutrition' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {naturalSources.map((source, index) => (
                <div key={index} className="cosmic-card">
                  <h3 className="font-semibold text-gray-900 mb-2">{source.name}</h3>
                  <div className="text-sm text-gray-600 mb-2">{source.amount}</div>
                  <div className="text-sm text-orange-600 font-medium">{source.benefit}</div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'lifestyle' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {lifestyle.map((item, index) => (
                <div key={index} className="cosmic-card">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                      <item.icon className="w-6 h-6 text-orange-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{item.title}</h3>
                  </div>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default AcetylcholinePage;
