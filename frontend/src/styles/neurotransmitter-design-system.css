/* 🧠 NEUROTRANSMITTER DESIGN SYSTEM - Suplementor Health Platform */
/* Enhanced Cosmic Design with Neurotransmitter-Specific Styling */

:root {
  /* 🧠 Neurotransmitter Color Palette */
  
  /* <PERSON><PERSON><PERSON> - Mood, Sleep, Happiness (Blue Spectrum) */
  --serotonin-50: #eff6ff;
  --serotonin-100: #dbeafe;
  --serotonin-200: #bfdbfe;
  --serotonin-300: #93c5fd;
  --serotonin-400: #60a5fa;
  --serotonin-500: #3b82f6;
  --serotonin-600: #2563eb;
  --serotonin-700: #1d4ed8;
  --serotonin-800: #1e40af;
  --serotonin-900: #1e3a8a;

  /* Dopamine - Motivation, Reward, Energy (Green Spectrum) */
  --dopamine-50: #ecfdf5;
  --dopamine-100: #d1fae5;
  --dopamine-200: #a7f3d0;
  --dopamine-300: #6ee7b7;
  --dopamine-400: #34d399;
  --dopamine-500: #10b981;
  --dopamine-600: #059669;
  --dopamine-700: #047857;
  --dopamine-800: #065f46;
  --dopamine-900: #064e3b;

  /* GABA - Relaxation, Calm, Anxiety Relief (Purple Spectrum) */
  --gaba-50: #faf5ff;
  --gaba-100: #f3e8ff;
  --gaba-200: #e9d5ff;
  --gaba-300: #d8b4fe;
  --gaba-400: #c084fc;
  --gaba-500: #a855f7;
  --gaba-600: #9333ea;
  --gaba-700: #7c3aed;
  --gaba-800: #6b21a8;
  --gaba-900: #581c87;

  /* Acetylcholine - Focus, Memory, Learning (Orange Spectrum) */
  --acetylcholine-50: #fffbeb;
  --acetylcholine-100: #fef3c7;
  --acetylcholine-200: #fde68a;
  --acetylcholine-300: #fcd34d;
  --acetylcholine-400: #fbbf24;
  --acetylcholine-500: #f59e0b;
  --acetylcholine-600: #d97706;
  --acetylcholine-700: #b45309;
  --acetylcholine-800: #92400e;
  --acetylcholine-900: #78350f;

  /* Norepinephrine - Alertness, Stress Response (Red Spectrum) */
  --norepinephrine-50: #fef2f2;
  --norepinephrine-100: #fee2e2;
  --norepinephrine-200: #fecaca;
  --norepinephrine-300: #fca5a5;
  --norepinephrine-400: #f87171;
  --norepinephrine-500: #ef4444;
  --norepinephrine-600: #dc2626;
  --norepinephrine-700: #b91c1c;
  --norepinephrine-800: #991b1b;
  --norepinephrine-900: #7f1d1d;

  /* 🎨 Neurotransmitter Gradients */
  --gradient-serotonin: linear-gradient(135deg, var(--serotonin-400) 0%, var(--serotonin-600) 100%);
  --gradient-dopamine: linear-gradient(135deg, var(--dopamine-400) 0%, var(--dopamine-600) 100%);
  --gradient-gaba: linear-gradient(135deg, var(--gaba-400) 0%, var(--gaba-600) 100%);
  --gradient-acetylcholine: linear-gradient(135deg, var(--acetylcholine-400) 0%, var(--acetylcholine-600) 100%);
  --gradient-norepinephrine: linear-gradient(135deg, var(--norepinephrine-400) 0%, var(--norepinephrine-600) 100%);

  /* 🌟 Neurotransmitter Shadows */
  --shadow-serotonin: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
  --shadow-dopamine: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
  --shadow-gaba: 0 4px 14px 0 rgba(168, 85, 247, 0.15);
  --shadow-acetylcholine: 0 4px 14px 0 rgba(245, 158, 11, 0.15);
  --shadow-norepinephrine: 0 4px 14px 0 rgba(239, 68, 68, 0.15);
}

/* 🧠 Neurotransmitter Card Components */

.cosmic-neurotransmitter-card {
  background: white;
  border-radius: calc(var(--cosmic-radius) * var(--cosmic-phi));
  padding: var(--space-lg);
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.cosmic-neurotransmitter-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--duration-normal) ease;
}

.cosmic-neurotransmitter-card:hover {
  transform: translateY(-4px);
}

/* Serotonin Card */
.cosmic-neurotransmitter-card--serotonin {
  border-color: var(--serotonin-200);
  background: linear-gradient(135deg, white 0%, var(--serotonin-50) 100%);
  box-shadow: var(--shadow-serotonin);
}

.cosmic-neurotransmitter-card--serotonin::before {
  background: var(--gradient-serotonin);
}

.cosmic-neurotransmitter-card--serotonin:hover {
  border-color: var(--serotonin-300);
  box-shadow: var(--shadow-serotonin), 0 8px 25px -5px rgba(59, 130, 246, 0.25);
}

/* Dopamine Card */
.cosmic-neurotransmitter-card--dopamine {
  border-color: var(--dopamine-200);
  background: linear-gradient(135deg, white 0%, var(--dopamine-50) 100%);
  box-shadow: var(--shadow-dopamine);
}

.cosmic-neurotransmitter-card--dopamine::before {
  background: var(--gradient-dopamine);
}

.cosmic-neurotransmitter-card--dopamine:hover {
  border-color: var(--dopamine-300);
  box-shadow: var(--shadow-dopamine), 0 8px 25px -5px rgba(16, 185, 129, 0.25);
}

/* GABA Card */
.cosmic-neurotransmitter-card--gaba {
  border-color: var(--gaba-200);
  background: linear-gradient(135deg, white 0%, var(--gaba-50) 100%);
  box-shadow: var(--shadow-gaba);
}

.cosmic-neurotransmitter-card--gaba::before {
  background: var(--gradient-gaba);
}

.cosmic-neurotransmitter-card--gaba:hover {
  border-color: var(--gaba-300);
  box-shadow: var(--shadow-gaba), 0 8px 25px -5px rgba(168, 85, 247, 0.25);
}

/* Acetylcholine Card */
.cosmic-neurotransmitter-card--acetylcholine {
  border-color: var(--acetylcholine-200);
  background: linear-gradient(135deg, white 0%, var(--acetylcholine-50) 100%);
  box-shadow: var(--shadow-acetylcholine);
}

.cosmic-neurotransmitter-card--acetylcholine::before {
  background: var(--gradient-acetylcholine);
}

.cosmic-neurotransmitter-card--acetylcholine:hover {
  border-color: var(--acetylcholine-300);
  box-shadow: var(--shadow-acetylcholine), 0 8px 25px -5px rgba(245, 158, 11, 0.25);
}

/* 📊 Neurotransmitter Progress Rings */
.cosmic-progress-ring {
  width: var(--cosmic-137);
  height: var(--cosmic-137);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: conic-gradient(from 0deg, var(--gray-200) 0deg, var(--gray-200) 360deg);
}

.cosmic-progress-ring--serotonin {
  background: conic-gradient(from 0deg, var(--serotonin-500) 0deg, var(--serotonin-500) var(--progress-angle, 0deg), var(--gray-200) var(--progress-angle, 0deg), var(--gray-200) 360deg);
}

.cosmic-progress-ring--dopamine {
  background: conic-gradient(from 0deg, var(--dopamine-500) 0deg, var(--dopamine-500) var(--progress-angle, 0deg), var(--gray-200) var(--progress-angle, 0deg), var(--gray-200) 360deg);
}

.cosmic-progress-ring--gaba {
  background: conic-gradient(from 0deg, var(--gaba-500) 0deg, var(--gaba-500) var(--progress-angle, 0deg), var(--gray-200) var(--progress-angle, 0deg), var(--gray-200) 360deg);
}

.cosmic-progress-ring--acetylcholine {
  background: conic-gradient(from 0deg, var(--acetylcholine-500) 0deg, var(--acetylcholine-500) var(--progress-angle, 0deg), var(--gray-200) var(--progress-angle, 0deg), var(--gray-200) 360deg);
}

.cosmic-progress-ring__inner {
  width: calc(100% - 16px);
  height: calc(100% - 16px);
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-weight: 700;
  color: var(--gray-900);
}

.cosmic-progress-ring__value {
  font-size: var(--text-2xl);
  line-height: 1;
}

.cosmic-progress-ring__label {
  font-size: var(--text-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 4px;
}

/* 🔗 Interaction Matrix */
.cosmic-interaction-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  padding: var(--space-lg);
}

.cosmic-interaction-item {
  background: white;
  border-radius: var(--cosmic-radius);
  padding: var(--space-md);
  border: 1px solid var(--gray-200);
  transition: all var(--duration-normal) ease;
}

.cosmic-interaction-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--cosmic-shadow);
}

.cosmic-interaction-item--synergistic {
  border-color: var(--success-300);
  background: linear-gradient(135deg, white 0%, var(--success-50) 100%);
}

.cosmic-interaction-item--competitive {
  border-color: var(--warning-300);
  background: linear-gradient(135deg, white 0%, var(--warning-50) 100%);
}

.cosmic-interaction-item--contraindicated {
  border-color: var(--error-300);
  background: linear-gradient(135deg, white 0%, var(--error-50) 100%);
}

/* 🎯 Recommendation Panel */
.cosmic-recommendation-panel {
  background: white;
  border-radius: calc(var(--cosmic-radius) * var(--cosmic-phi));
  padding: var(--space-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--cosmic-shadow);
}

.cosmic-recommendation-item {
  display: flex;
  align-items: center;
  padding: var(--space-md);
  border-radius: var(--cosmic-radius);
  background: var(--gray-50);
  margin-bottom: var(--space-sm);
  transition: all var(--duration-normal) ease;
}

.cosmic-recommendation-item:hover {
  background: var(--gray-100);
  transform: translateX(4px);
}

.cosmic-recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-md);
  background: var(--gradient-cosmic);
  color: white;
}

/* 🧪 Test Interface */
.cosmic-test-interface {
  background: white;
  border-radius: calc(var(--cosmic-radius) * var(--cosmic-phi));
  padding: var(--space-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--cosmic-shadow);
}

.cosmic-test-question {
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  border-radius: var(--cosmic-radius);
  background: var(--gray-50);
  border-left: 4px solid var(--primary-500);
}

.cosmic-test-options {
  display: grid;
  gap: var(--space-sm);
  margin-top: var(--space-md);
}

.cosmic-test-option {
  padding: var(--space-md);
  border-radius: var(--cosmic-radius);
  border: 2px solid var(--gray-200);
  background: white;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
}

.cosmic-test-option:hover {
  border-color: var(--primary-300);
  background: var(--primary-50);
}

.cosmic-test-option--selected {
  border-color: var(--primary-500);
  background: var(--primary-100);
}

/* 🌿 Herbal Explorer */
.cosmic-herbal-card {
  background: white;
  border-radius: calc(var(--cosmic-radius) * var(--cosmic-phi));
  padding: var(--space-lg);
  border: 1px solid var(--secondary-200);
  box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.15);
  transition: all var(--duration-normal) ease;
}

.cosmic-herbal-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px -5px rgba(34, 197, 94, 0.25);
  border-color: var(--secondary-300);
}

.cosmic-herbal-image {
  width: 100%;
  height: 200px;
  border-radius: var(--cosmic-radius);
  object-fit: cover;
  margin-bottom: var(--space-md);
}

/* 🎭 Neurotransmitter Animations */
@keyframes neurotransmitter-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes neurotransmitter-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.cosmic-neurotransmitter-pulse {
  animation: neurotransmitter-pulse 3s ease-in-out infinite;
}

.cosmic-neurotransmitter-float {
  animation: neurotransmitter-float 4s ease-in-out infinite;
}

/* 📱 Responsive Neurotransmitter Design */
@media (max-width: 768px) {
  .cosmic-neurotransmitter-card {
    padding: var(--space-md);
  }

  .cosmic-progress-ring {
    width: calc(var(--cosmic-137) * 0.8);
    height: calc(var(--cosmic-137) * 0.8);
  }

  .cosmic-interaction-matrix {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
    padding: var(--space-md);
  }

  .cosmic-recommendation-panel {
    padding: var(--space-lg);
  }

  .cosmic-test-interface {
    padding: var(--space-lg);
  }
}
