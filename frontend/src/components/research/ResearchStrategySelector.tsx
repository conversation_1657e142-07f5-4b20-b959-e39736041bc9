import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Zap,
  Compass,
  Target,
  Clock,
  TrendingUp,
  Users,
  Star,
  CheckCircle,
  ArrowRight,
  BarChart3,
  Lightbulb,
  Shield,
  Layers
} from 'lucide-react';

interface ResearchStrategy {
  id: string;
  name: string;
  description: string;
  type: 'comprehensive' | 'focused' | 'exploratory' | 'validation' | 'synthesis';
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  estimatedDuration: number;
  performance: {
    usageCount: number;
    successRate: number;
    averageQuality: number;
    averageTime: number;
    userSatisfaction: number;
    lastUsed: Date;
  };
  metadata?: {
    difficulty: string;
    recommended_for: string[];
  };
}

interface ResearchContext {
  query: string;
  domain: string;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  timeConstraint?: number;
  qualityRequirement: 'basic' | 'standard' | 'high' | 'expert';
  userExpertise: 'beginner' | 'intermediate' | 'expert';
}

interface ResearchStrategySelectorProps {
  onStrategySelected?: (strategy: ResearchStrategy, context: ResearchContext) => void;
  className?: string;
}

export const ResearchStrategySelector: React.FC<ResearchStrategySelectorProps> = ({
  onStrategySelected,
  className = ''
}) => {
  const [strategies, setStrategies] = useState<ResearchStrategy[]>([]);
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null);
  const [context, setContext] = useState<ResearchContext>({
    query: '',
    domain: 'general',
    complexity: 'moderate',
    qualityRequirement: 'standard',
    userExpertise: 'intermediate'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const fetchStrategies = async () => {
    try {
      const response = await fetch('/api/enhanced-ai/strategies');
      const data = await response.json();
      
      if (data.success) {
        setStrategies(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch strategies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStrategies();
  }, []);

  const getStrategyIcon = (type: string) => {
    switch (type) {
      case 'comprehensive': return <Layers className="w-6 h-6" />;
      case 'focused': return <Target className="w-6 h-6" />;
      case 'exploratory': return <Compass className="w-6 h-6" />;
      case 'validation': return <Shield className="w-6 h-6" />;
      case 'synthesis': return <Lightbulb className="w-6 h-6" />;
      default: return <Search className="w-6 h-6" />;
    }
  };

  const getStrategyColor = (type: string) => {
    switch (type) {
      case 'comprehensive': return 'from-blue-500 to-indigo-600';
      case 'focused': return 'from-green-500 to-emerald-600';
      case 'exploratory': return 'from-purple-500 to-violet-600';
      case 'validation': return 'from-orange-500 to-red-600';
      case 'synthesis': return 'from-pink-500 to-rose-600';
      default: return 'from-gray-500 to-slate-600';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-blue-600 bg-blue-100';
      case 'complex': return 'text-orange-600 bg-orange-100';
      case 'expert': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    return `${minutes} min`;
  };

  const handleStrategySelect = (strategy: ResearchStrategy) => {
    setSelectedStrategy(strategy.id);
    onStrategySelected?.(strategy, context);
  };

  const getRecommendedStrategy = () => {
    if (strategies.length === 0) return null;
    
    // Simple recommendation logic based on context
    if (context.qualityRequirement === 'expert') {
      return strategies.find(s => s.type === 'comprehensive');
    }
    if (context.timeConstraint && context.timeConstraint < 600000) { // Less than 10 minutes
      return strategies.find(s => s.type === 'focused');
    }
    return strategies.find(s => s.type === 'exploratory');
  };

  const recommendedStrategy = getRecommendedStrategy();

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading research strategies...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Context Configuration */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Research Context</h3>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>
        </div>

        <div className="space-y-4">
          {/* Research Query */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Research Query
            </label>
            <input
              type="text"
              value={context.query}
              onChange={(e) => setContext(prev => ({ ...prev, query: e.target.value }))}
              placeholder="What would you like to research?"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Basic Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Domain
              </label>
              <select
                value={context.domain}
                onChange={(e) => setContext(prev => ({ ...prev, domain: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="general">General</option>
                <option value="health">Health & Medicine</option>
                <option value="technology">Technology</option>
                <option value="science">Science</option>
                <option value="business">Business</option>
                <option value="education">Education</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quality Requirement
              </label>
              <select
                value={context.qualityRequirement}
                onChange={(e) => setContext(prev => ({ ...prev, qualityRequirement: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="basic">Basic</option>
                <option value="standard">Standard</option>
                <option value="high">High</option>
                <option value="expert">Expert</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Expertise
              </label>
              <select
                value={context.userExpertise}
                onChange={(e) => setContext(prev => ({ ...prev, userExpertise: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="expert">Expert</option>
              </select>
            </div>
          </div>

          {/* Advanced Options */}
          <AnimatePresence>
            {showAdvanced && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200"
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Complexity Preference
                  </label>
                  <select
                    value={context.complexity}
                    onChange={(e) => setContext(prev => ({ ...prev, complexity: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="simple">Simple</option>
                    <option value="moderate">Moderate</option>
                    <option value="complex">Complex</option>
                    <option value="expert">Expert</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time Constraint (minutes)
                  </label>
                  <input
                    type="number"
                    value={context.timeConstraint ? context.timeConstraint / 60000 : ''}
                    onChange={(e) => setContext(prev => ({ 
                      ...prev, 
                      timeConstraint: e.target.value ? parseInt(e.target.value) * 60000 : undefined 
                    }))}
                    placeholder="No limit"
                    min="1"
                    max="120"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Recommended Strategy */}
      {recommendedStrategy && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border-2 border-blue-200"
        >
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <Star className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-blue-900">Recommended Strategy</h3>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-800">{recommendedStrategy.name}</h4>
              <p className="text-sm text-blue-600">{recommendedStrategy.description}</p>
            </div>
            <button
              onClick={() => handleStrategySelect(recommendedStrategy)}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2"
            >
              <span>Use This</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      )}

      {/* Strategy Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <AnimatePresence>
          {strategies.map((strategy, index) => (
            <motion.div
              key={strategy.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-200 cursor-pointer ${
                selectedStrategy === strategy.id 
                  ? 'border-blue-500 shadow-xl transform scale-105' 
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-xl'
              }`}
              onClick={() => handleStrategySelect(strategy)}
            >
              {/* Strategy Header */}
              <div className={`bg-gradient-to-r ${getStrategyColor(strategy.type)} rounded-t-xl p-4 text-white`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStrategyIcon(strategy.type)}
                    <div>
                      <h3 className="font-semibold">{strategy.name}</h3>
                      <p className="text-sm opacity-90">{strategy.type}</p>
                    </div>
                  </div>
                  {selectedStrategy === strategy.id && (
                    <CheckCircle className="w-6 h-6 text-green-300" />
                  )}
                </div>
              </div>

              {/* Strategy Content */}
              <div className="p-4 space-y-4">
                <p className="text-sm text-gray-600">{strategy.description}</p>

                {/* Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getComplexityColor(strategy.complexity)}`}>
                      {strategy.complexity}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(strategy.estimatedDuration)}</span>
                    </div>
                  </div>
                </div>

                {/* Performance Stats */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Success Rate:</span>
                    <span className="font-medium">{strategy.performance.successRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Quality:</span>
                    <span className="font-medium">{strategy.performance.averageQuality.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Satisfaction:</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium">{strategy.performance.userSatisfaction.toFixed(1)}</span>
                    </div>
                  </div>
                </div>

                {/* Usage Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-200">
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{strategy.performance.usageCount} uses</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BarChart3 className="w-4 h-4" />
                    <span>Avg {formatDuration(strategy.performance.averageTime)}</span>
                  </div>
                </div>

                {/* Recommended For */}
                {strategy.metadata?.recommended_for && (
                  <div className="pt-2 border-t border-gray-200">
                    <span className="text-xs text-gray-500 block mb-1">Recommended for:</span>
                    <div className="flex flex-wrap gap-1">
                      {strategy.metadata.recommended_for.map((rec, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs"
                        >
                          {rec.replace(/_/g, ' ')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Action Button */}
      {selectedStrategy && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-center"
        >
          <button
            onClick={() => {
              const strategy = strategies.find(s => s.id === selectedStrategy);
              if (strategy) handleStrategySelect(strategy);
            }}
            className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
          >
            <Zap className="w-5 h-5" />
            <span>Start Research with Selected Strategy</span>
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default ResearchStrategySelector;
