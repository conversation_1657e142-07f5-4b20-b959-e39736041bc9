import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  MessageCircle,
  Edit3,
  Eye,
  Share2,
  Clock,
  FileText,
  Plus,
  MoreVertical,
  UserPlus,
  Settings,
  Activity,
  Zap,
  Star,
  Lock,
  Globe
} from 'lucide-react';

interface User {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  cursor?: { x: number; y: number };
  isTyping?: boolean;
}

interface Document {
  id: string;
  title: string;
  content: string;
  lastModified: Date;
  modifiedBy: string;
  isLocked?: boolean;
  lockedBy?: string;
}

interface Workspace {
  id: string;
  name: string;
  description: string;
  type: 'research' | 'analysis' | 'collaboration';
  ownerId: string;
  memberCount: number;
  documentCount: number;
  lastActivity: Date;
  isOnline: boolean;
  settings: {
    isPublic: boolean;
    allowGuestAccess: boolean;
    requireApproval: boolean;
    enableVersioning: boolean;
    enableComments: boolean;
    enableRealTimeEditing: boolean;
  };
}

interface Comment {
  id: string;
  content: string;
  author: User;
  timestamp: Date;
  position?: { x: number; y: number };
  resolved?: boolean;
}

interface CollaborativeWorkspaceProps {
  workspaceId: string;
  userId: string;
  className?: string;
}

export const CollaborativeWorkspace: React.FC<CollaborativeWorkspaceProps> = ({
  workspaceId,
  userId,
  className = ''
}) => {
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [activeUsers, setActiveUsers] = useState<User[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUserList, setShowUserList] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const workspaceRef = useRef<HTMLDivElement>(null);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockWorkspace: Workspace = {
      id: workspaceId,
      name: 'Vitamin D Research Project',
      description: 'Collaborative research on vitamin D supplementation',
      type: 'research',
      ownerId: 'user_123',
      memberCount: 3,
      documentCount: 5,
      lastActivity: new Date(Date.now() - 300000),
      isOnline: true,
      settings: {
        isPublic: false,
        allowGuestAccess: false,
        requireApproval: true,
        enableVersioning: true,
        enableComments: true,
        enableRealTimeEditing: true
      }
    };

    const mockUsers: User[] = [
      {
        id: 'user_1',
        name: 'Dr. Sarah Johnson',
        status: 'online',
        cursor: { x: 150, y: 200 },
        isTyping: false
      },
      {
        id: 'user_2',
        name: 'Prof. Michael Chen',
        status: 'online',
        cursor: { x: 300, y: 150 },
        isTyping: true
      },
      {
        id: 'user_3',
        name: 'Research Assistant',
        status: 'away',
        isTyping: false
      }
    ];

    const mockDocuments: Document[] = [
      {
        id: 'doc_1',
        title: 'Literature Review',
        content: 'Comprehensive review of vitamin D research...',
        lastModified: new Date(Date.now() - 120000),
        modifiedBy: 'Dr. Sarah Johnson'
      },
      {
        id: 'doc_2',
        title: 'Methodology',
        content: 'Research methodology and approach...',
        lastModified: new Date(Date.now() - 300000),
        modifiedBy: 'Prof. Michael Chen',
        isLocked: true,
        lockedBy: 'Prof. Michael Chen'
      },
      {
        id: 'doc_3',
        title: 'Data Analysis',
        content: 'Statistical analysis of collected data...',
        lastModified: new Date(Date.now() - 600000),
        modifiedBy: 'Research Assistant'
      }
    ];

    const mockComments: Comment[] = [
      {
        id: 'comment_1',
        content: 'This section needs more recent studies',
        author: mockUsers[0],
        timestamp: new Date(Date.now() - 180000),
        position: { x: 200, y: 100 }
      },
      {
        id: 'comment_2',
        content: 'Great analysis! Could we add more context?',
        author: mockUsers[1],
        timestamp: new Date(Date.now() - 120000),
        resolved: false
      }
    ];

    setWorkspace(mockWorkspace);
    setActiveUsers(mockUsers);
    setDocuments(mockDocuments);
    setComments(mockComments);
    setIsLoading(false);
  }, [workspaceId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getWorkspaceTypeIcon = (type: string) => {
    switch (type) {
      case 'research': return <FileText className="w-5 h-5" />;
      case 'analysis': return <Activity className="w-5 h-5" />;
      case 'collaboration': return <Users className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  const addComment = () => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: `comment_${Date.now()}`,
      content: newComment,
      author: activeUsers.find(u => u.id === userId) || activeUsers[0],
      timestamp: new Date(),
      resolved: false
    };

    setComments(prev => [...prev, comment]);
    setNewComment('');
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading workspace...</span>
        </div>
      </div>
    );
  }

  if (!workspace) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <p className="text-gray-600">Workspace not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-gray-50 ${className}`} ref={workspaceRef}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white`}>
              {getWorkspaceTypeIcon(workspace.type)}
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{workspace.name}</h1>
              <p className="text-sm text-gray-600">{workspace.description}</p>
            </div>
            <div className="flex items-center space-x-2">
              {workspace.settings.isPublic ? (
                <Globe className="w-4 h-4 text-green-600" />
              ) : (
                <Lock className="w-4 h-4 text-gray-600" />
              )}
              <span className="text-sm text-gray-600">
                {workspace.settings.isPublic ? 'Public' : 'Private'}
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Active Users */}
            <div className="flex items-center space-x-2">
              <div className="flex -space-x-2">
                {activeUsers.slice(0, 4).map((user) => (
                  <motion.div
                    key={user.id}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="relative"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium border-2 border-white">
                      {user.name.charAt(0)}
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 ${getStatusColor(user.status)} rounded-full border border-white`}></div>
                    {user.isTyping && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                  </motion.div>
                ))}
              </div>
              {activeUsers.length > 4 && (
                <span className="text-sm text-gray-600">+{activeUsers.length - 4} more</span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowUserList(!showUserList)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Users className="w-5 h-5" />
              </button>
              <button
                onClick={() => setShowComments(!showComments)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors relative"
              >
                <MessageCircle className="w-5 h-5" />
                {comments.filter(c => !c.resolved).length > 0 && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {comments.filter(c => !c.resolved).length}
                  </div>
                )}
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Share2 className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Documents */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold text-gray-900">Documents</h2>
              <button className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            <AnimatePresence>
              {documents.map((doc, index) => (
                <motion.div
                  key={doc.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                    selectedDocument === doc.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedDocument(doc.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4 text-gray-600" />
                        <h3 className="font-medium text-gray-900 text-sm">{doc.title}</h3>
                        {doc.isLocked && (
                          <Lock className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                        {doc.content.substring(0, 60)}...
                      </p>
                      <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                        <span>{doc.modifiedBy}</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{new Date(doc.lastModified).toLocaleTimeString()}</span>
                        </div>
                      </div>
                    </div>
                    <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                      <MoreVertical className="w-3 h-3" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {selectedDocument ? (
            <div className="flex-1 p-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {documents.find(d => d.id === selectedDocument)?.title}
                  </h2>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Eye className="w-4 h-4" />
                      <span>{activeUsers.length} viewing</span>
                    </div>
                    <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Document Content */}
                <div className="relative">
                  <textarea
                    className="w-full h-96 p-4 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={documents.find(d => d.id === selectedDocument)?.content || ''}
                    placeholder="Start typing..."
                  />

                  {/* Live Cursors */}
                  {activeUsers.map((user) => (
                    user.cursor && (
                      <motion.div
                        key={user.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="absolute pointer-events-none"
                        style={{ left: user.cursor.x, top: user.cursor.y }}
                      >
                        <div className="w-0.5 h-6 bg-blue-500"></div>
                        <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-md mt-1">
                          {user.name}
                        </div>
                      </motion.div>
                    )
                  ))}
                </div>

                {/* Real-time Status */}
                <div className="flex items-center justify-between mt-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Auto-saved</span>
                    </div>
                    {activeUsers.some(u => u.isTyping) && (
                      <div className="flex items-center space-x-1">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
                          <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span>Someone is typing...</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <Zap className="w-4 h-4 text-yellow-500" />
                    <span>Real-time collaboration</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a document</h3>
                <p className="text-gray-600">Choose a document from the sidebar to start collaborating</p>
              </div>
            </div>
          )}
        </div>

        {/* Comments Sidebar */}
        <AnimatePresence>
          {showComments && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 320, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              className="bg-white border-l border-gray-200 flex flex-col"
            >
              <div className="p-4 border-b border-gray-200">
                <h2 className="font-semibold text-gray-900">Comments</h2>
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {comments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                        {comment.author.name.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-900">{comment.author.name}</span>
                          <span className="text-xs text-gray-500">
                            {comment.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700">{comment.content}</p>
                        {!comment.resolved && (
                          <button className="text-xs text-blue-600 hover:text-blue-700 mt-1">
                            Mark as resolved
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Add a comment..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && addComment()}
                  />
                  <button
                    onClick={addComment}
                    className="px-3 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors"
                  >
                    Add
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default CollaborativeWorkspace;
