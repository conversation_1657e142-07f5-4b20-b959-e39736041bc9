import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain, Heart, Shield, Zap, Plus, Minus, Star, AlertTriangle,
  TrendingUp, DollarSign, Clock, CheckCircle, XCircle, Info,
  Search, Filter, ShoppingCart, Bookmark, Share2, Download
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';

interface SupplementCard {
  id: string;
  name: string;
  category: string;
  description: string;
  benefits: string[];
  dosage: string;
  interactions: string[];
  contraindications: string[];
  evidenceLevel: 'high' | 'moderate' | 'low' | 'preliminary';
  aiConfidence: number;
  price: number;
  availability: 'in-stock' | 'low-stock' | 'out-of-stock';
  imageUrl?: string;
  researchLinks: string[];
  userRating: number;
  reviewCount: number;
}

interface PatientProfile {
  age: number;
  gender: string;
  conditions: string[];
  medications: string[];
  allergies: string[];
  goals: string[];
}

interface MedicalAnalysis {
  patientProfile: PatientProfile;
  recommendations: SupplementCard[];
  warnings: string[];
  interactions: Array<{
    supplement1: string;
    supplement2: string;
    severity: 'mild' | 'moderate' | 'severe';
    description: string;
  }>;
  totalCost: number;
  confidenceScore: number;
}

const MedicalResearchModule: React.FC = () => {
  const [patientProfile, setPatientProfile] = useState<PatientProfile>({
    age: 35,
    gender: 'female',
    conditions: [],
    medications: [],
    allergies: [],
    goals: []
  });

  const [analysis, setAnalysis] = useState<MedicalAnalysis | null>(null);
  const [userRegimen, setUserRegimen] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAnalysisForm, setShowAnalysisForm] = useState(true);

  // Mock categories for filtering
  const categories = [
    'all', 'Essential Fatty Acids', 'Vitamins', 'Minerals', 
    'Herbal Extracts', 'Probiotics', 'Adaptogens'
  ];

  const performAnalysis = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/medical-research/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          patientProfile,
          symptoms: ['fatigue', 'stress'],
          goals: patientProfile.goals,
          budget: 200
        })
      });

      if (response.ok) {
        const result = await response.json();
        setAnalysis(result.data);
        setShowAnalysisForm(false);
      }
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToRegimen = async (supplementId: string) => {
    try {
      const response = await fetch('/api/medical-research/supplements/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'demo-user',
          supplementId,
          dosage: 'As recommended',
          frequency: 'Daily'
        })
      });

      if (response.ok) {
        setUserRegimen(prev => [...prev, supplementId]);
      }
    } catch (error) {
      console.error('Failed to add supplement:', error);
    }
  };

  const removeFromRegimen = async (supplementId: string) => {
    try {
      const response = await fetch(`/api/medical-research/supplements/${supplementId}/remove`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: 'demo-user' })
      });

      if (response.ok) {
        setUserRegimen(prev => prev.filter(id => id !== supplementId));
      }
    } catch (error) {
      console.error('Failed to remove supplement:', error);
    }
  };

  const getEvidenceBadgeColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-orange-100 text-orange-800';
      case 'preliminary': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in-stock': return 'text-green-600';
      case 'low-stock': return 'text-yellow-600';
      case 'out-of-stock': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const filteredSupplements = analysis?.recommendations.filter(supplement => {
    const matchesSearch = supplement.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplement.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || supplement.category === selectedCategory;
    return matchesSearch && matchesCategory;
  }) || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
              <Brain className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Medical Research & AI Analysis
              </h1>
              <p className="text-gray-600 mt-2">
                AI-powered supplement recommendations based on your health profile
              </p>
            </div>
          </div>
        </motion.div>

        {/* Patient Profile Form */}
        <AnimatePresence>
          {showAnalysisForm && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white rounded-2xl shadow-xl p-8 mb-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Heart className="w-6 h-6 mr-3 text-red-500" />
                Health Profile Assessment
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Age</label>
                  <input
                    type="number"
                    value={patientProfile.age}
                    onChange={(e) => setPatientProfile(prev => ({ ...prev, age: Number(e.target.value) }))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                  <select
                    value={patientProfile.gender}
                    onChange={(e) => setPatientProfile(prev => ({ ...prev, gender: e.target.value }))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="female">Female</option>
                    <option value="male">Male</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Health Goals</label>
                  <div className="flex flex-wrap gap-2">
                    {['Energy & Vitality', 'Stress Management', 'Immune Support', 'Cognitive Enhancement', 'Heart Health', 'Joint Health'].map(goal => (
                      <button
                        key={goal}
                        onClick={() => {
                          setPatientProfile(prev => ({
                            ...prev,
                            goals: prev.goals.includes(goal) 
                              ? prev.goals.filter(g => g !== goal)
                              : [...prev.goals, goal]
                          }));
                        }}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                          patientProfile.goals.includes(goal)
                            ? 'bg-blue-500 text-white shadow-lg'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {goal}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-center">
                <Button
                  onClick={performAnalysis}
                  disabled={loading}
                  className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Analyzing...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Brain className="w-5 h-5 mr-2" />
                      Start AI Analysis
                    </div>
                  )}
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Analysis Results */}
        {analysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {/* Analysis Summary */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  <TrendingUp className="w-6 h-6 mr-3 text-green-500" />
                  AI Analysis Results
                </h2>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{analysis.confidenceScore}%</div>
                    <div className="text-sm text-gray-500">AI Confidence</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">${analysis.totalCost.toFixed(2)}</div>
                    <div className="text-sm text-gray-500">Total Cost</div>
                  </div>
                </div>
              </div>

              {/* Warnings */}
              {analysis.warnings.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center mb-2">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                    <h3 className="font-semibold text-yellow-800">Important Considerations</h3>
                  </div>
                  <ul className="list-disc list-inside text-yellow-700 space-y-1">
                    {analysis.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Search and Filter */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search supplements..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="relative">
                  <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category === 'all' ? 'All Categories' : category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Supplement Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence>
                {filteredSupplements.map((supplement, index) => (
                  <motion.div
                    key={supplement.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
                  >
                    {/* Card Header */}
                    <div className="relative p-6 pb-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-gray-900 mb-1">{supplement.name}</h3>
                          <p className="text-sm text-gray-600">{supplement.category}</p>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getEvidenceBadgeColor(supplement.evidenceLevel)}`}>
                            {supplement.evidenceLevel} evidence
                          </div>
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-400 mr-1" />
                            <span className="text-sm font-medium">{supplement.userRating}</span>
                            <span className="text-xs text-gray-500 ml-1">({supplement.reviewCount})</span>
                          </div>
                        </div>
                      </div>

                      {/* AI Confidence Bar */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs font-medium text-gray-700">AI Confidence</span>
                          <span className="text-xs font-bold text-blue-600">{supplement.aiConfidence}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${supplement.aiConfidence}%` }}
                          ></div>
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 mb-4">{supplement.description}</p>

                      {/* Benefits */}
                      <div className="mb-4">
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Benefits</h4>
                        <div className="flex flex-wrap gap-1">
                          {supplement.benefits.slice(0, 3).map((benefit, idx) => (
                            <span
                              key={idx}
                              className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
                            >
                              {benefit}
                            </span>
                          ))}
                          {supplement.benefits.length > 3 && (
                            <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-full">
                              +{supplement.benefits.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Dosage */}
                      <div className="mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="w-4 h-4 mr-2" />
                          {supplement.dosage}
                        </div>
                      </div>

                      {/* Price and Availability */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 text-green-600 mr-1" />
                          <span className="text-lg font-bold text-gray-900">${supplement.price}</span>
                        </div>
                        <div className={`text-sm font-medium ${getAvailabilityColor(supplement.availability)}`}>
                          {supplement.availability.replace('-', ' ')}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-2">
                        {userRegimen.includes(supplement.id) ? (
                          <Button
                            onClick={() => removeFromRegimen(supplement.id)}
                            className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg font-medium transition-colors flex items-center justify-center"
                          >
                            <Minus className="w-4 h-4 mr-2" />
                            Remove
                          </Button>
                        ) : (
                          <Button
                            onClick={() => addToRegimen(supplement.id)}
                            className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg font-medium transition-colors flex items-center justify-center"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add to Regimen
                          </Button>
                        )}
                        <Button
                          className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Info className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Expandable Details */}
                    <motion.div
                      initial={false}
                      className="border-t border-gray-100 p-4 bg-gray-50"
                    >
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="font-medium text-gray-700">Interactions:</span>
                          <p className="text-gray-600 mt-1">
                            {supplement.interactions.length > 0
                              ? supplement.interactions.slice(0, 2).join(', ')
                              : 'None known'
                            }
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Research:</span>
                          <p className="text-blue-600 mt-1">
                            {supplement.researchLinks.length} studies available
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* User Regimen Summary */}
            {userRegimen.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8"
              >
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <ShoppingCart className="w-6 h-6 mr-3 text-green-600" />
                  Your Supplement Regimen ({userRegimen.length} items)
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userRegimen.map(supplementId => {
                    const supplement = analysis.recommendations.find(s => s.id === supplementId);
                    return supplement ? (
                      <div key={supplementId} className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-gray-900">{supplement.name}</h4>
                            <p className="text-sm text-gray-600">{supplement.dosage}</p>
                          </div>
                          <Button
                            onClick={() => removeFromRegimen(supplementId)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <XCircle className="w-5 h-5" />
                          </Button>
                        </div>
                      </div>
                    ) : null;
                  })}
                </div>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* New Analysis Button */}
        {analysis && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center mt-8"
          >
            <Button
              onClick={() => {
                setShowAnalysisForm(true);
                setAnalysis(null);
              }}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all"
            >
              Start New Analysis
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MedicalResearchModule;
