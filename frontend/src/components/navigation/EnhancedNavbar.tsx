import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu, X, Search, Upload, BarChart3, Settings, Info, Network, Bell, User,
  Brain, Microscope, Pill, Sparkles, ChevronDown, ChevronRight, Heart, Zap,
  Shield, Target, Database, Bot, FlaskConical, Leaf, Activity, TrendingUp,
  Layers, Cpu, Globe, BookOpen, Lightbulb, Workflow
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: string;
  color?: string;
  description?: string;
}

interface NavSection {
  title: string;
  items: NavItem[];
  icon: React.ComponentType<any>;
  color: string;
  expanded?: boolean;
}

interface EnhancedNavbarProps {
  children: React.ReactNode;
}

const EnhancedNavbar: React.FC<EnhancedNavbarProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    coreplatform: true,
    neuroscience: true,
    research: false,
    aimodels: false,
    datamanagement: false
  });
  
  const location = useLocation();

  // Navigation sections based on the Overall Interface Design Plan
  const navigationSections: NavSection[] = [    {
      title: 'Core Platform',
      icon: Activity,
      color: '#3b82f6',
      items: [
        { name: 'Dashboard', href: '/', icon: Activity, description: 'System overview and metrics' },
        { name: 'Graph', href: '/graph', icon: Network, description: 'Knowledge graph visualization' },
        { name: 'Infinite Graph', href: '/infinite-graph', icon: Sparkles, badge: 'Demo', description: 'Advanced graph exploration' },
        { name: 'Supplements', href: '/supplements', icon: Pill, badge: 'New', description: 'Supplement database' },
        { name: 'Search', href: '/search', icon: Search, description: 'Global search interface' },
        { name: 'Analytics', href: '/analytics', icon: BarChart3, description: 'Data analytics dashboard' }
      ]
    },
    {
      title: 'Neuroscience',
      icon: Brain,
      color: '#8b5cf6',
      items: [
        { name: 'Neuroregulation', href: '/neuroregulation', icon: Brain, badge: 'Overview', color: '#8b5cf6', description: 'Neural system overview' },
        { name: 'Neuromediators Hub', href: '/neuromediators', icon: Microscope, badge: 'Unified', color: '#6366f1', description: 'Unified neurotransmitter dashboard' },
        { name: 'Serotonin', href: '/serotonin', icon: Heart, badge: 'Mood', color: '#3b82f6', description: 'Mood and happiness system' },
        { name: 'Dopamine', href: '/dopamine', icon: Zap, badge: 'Energy', color: '#10b981', description: 'Motivation and energy system' },
        { name: 'GABA', href: '/gaba', icon: Shield, badge: 'Calm', color: '#a855f7', description: 'Relaxation and anxiety management' },
        { name: 'Acetylcholine', href: '/acetylcholine', icon: Target, badge: 'Focus', color: '#f59e0b', description: 'Memory and cognitive enhancement' },
        { name: 'Braverman Test', href: '/braverman-test', icon: Brain, badge: 'Assessment', color: '#ec4899', description: 'Neurotransmitter personality test' },
        { name: 'Swiss Herbal', href: '/swiss-herbal', icon: Leaf, badge: 'Traditional', color: '#22c55e', description: 'Traditional herbal medicine' }
      ]
    },
    {
      title: 'Research & Discovery',
      icon: FlaskConical,
      color: '#10b981',
      items: [
        { name: 'Research Hub', href: '/research', icon: Microscope, badge: 'AI', description: 'AI-powered research orchestration' },
        { name: 'Knowledge Discovery', href: '/knowledge-discovery', icon: Lightbulb, badge: 'New', description: 'Autonomous knowledge expansion' },
        { name: 'Research Runs', href: '/research-runs', icon: TrendingUp, description: 'Active research monitoring' },
        { name: 'Scientific Papers', href: '/papers', icon: BookOpen, description: 'Research paper analysis' },
        { name: 'Upload', href: '/upload', icon: Upload, description: 'Data upload interface' }
      ]
    },
    {
      title: 'AI Models Management',
      icon: Bot,
      color: '#f59e0b',
      items: [
        { name: 'AI Dashboard', href: '/ai-models', icon: Bot, badge: 'Enhanced', description: 'AI model monitoring and management' },
        { name: 'Gemma3 Medical', href: '/gemma3', icon: Cpu, badge: 'Medical', description: 'Medical AI model interface' },
        { name: 'Model Performance', href: '/model-performance', icon: TrendingUp, description: 'Performance metrics and optimization' },
        { name: 'AG-UI Status', href: '/ag-ui', icon: Workflow, badge: 'Live', description: 'Agent communication status' }
      ]
    },
    {
      title: 'Data Management',
      icon: Database,
      color: '#ec4899',
      items: [
        { name: 'Neo4j Graph', href: '/neo4j', icon: Network, description: 'Graph database management' },
        { name: 'Weaviate Vector', href: '/weaviate', icon: Layers, description: 'Vector database for semantic search' },
        { name: 'MongoDB Documents', href: '/mongodb', icon: Database, description: 'Document database management' },
        { name: 'Generative Data', href: '/generative-data', icon: Sparkles, badge: 'AI', description: 'AI-generated content management' },
        { name: 'Data Review', href: '/data-review', icon: Shield, description: 'Content review and approval' }
      ]
    }
  ];  const toggleSection = (sectionTitle: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle.toLowerCase().replace(/\s+/g, '')]: !prev[sectionTitle.toLowerCase().replace(/\s+/g, '')]
    }));
  };

  const isActiveRoute = (href: string) => {
    return location.pathname === href;
  };

  const getActiveSectionForRoute = () => {
    for (const section of navigationSections) {
      for (const item of section.items) {
        if (isActiveRoute(item.href)) {
          return section.title;
        }
      }
    }
    return null;
  };

  // Auto-expand section containing active route
  useEffect(() => {
    const activeSection = getActiveSectionForRoute();
    if (activeSection) {
      const sectionKey = activeSection.toLowerCase().replace(/\s+/g, '');
      setExpandedSections(prev => ({
        ...prev,
        [sectionKey]: true
      }));
    }
  }, [location.pathname]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : '-100%',
        }}
        className="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:static lg:translate-x-0 lg:shadow-none"
      >        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">Suplementor</h1>
              <p className="text-xs text-gray-500">Health Intelligence Platform</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation sections */}
        <div className="flex-1 overflow-y-auto py-6">
          <nav className="px-4 space-y-2">
            {navigationSections.map((section) => {
              const sectionKey = section.title.toLowerCase().replace(/\s+/g, '');
              const isExpanded = expandedSections[sectionKey];
              const SectionIcon = section.icon;

              return (
                <div key={section.title} className="space-y-1">
                  {/* Section header */}
                  <button
                    onClick={() => toggleSection(section.title)}
                    className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 transition-colors group"
                  >
                    <div 
                      className="w-8 h-8 rounded-lg flex items-center justify-center mr-3 text-white"
                      style={{ backgroundColor: section.color }}
                    >
                      <SectionIcon className="w-4 h-4" />
                    </div>
                    <span className="flex-1 text-left">{section.title}</span>
                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </motion.div>
                  </button>

                  {/* Section items */}
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-6 space-y-1"
                      >                        {section.items.map((item) => {
                          const isActive = isActiveRoute(item.href);
                          const Icon = item.icon;

                          return (
                            <Link
                              key={item.name}
                              to={item.href}
                              className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 group ${
                                isActive
                                  ? 'text-white shadow-lg transform scale-105'
                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:transform hover:scale-102'
                              }`}
                              style={isActive ? { 
                                background: item.color ? `linear-gradient(135deg, ${item.color}dd 0%, ${item.color} 100%)` : section.color,
                                boxShadow: `0 4px 14px 0 ${item.color || section.color}40`
                              } : {}}
                              onClick={() => setSidebarOpen(false)}
                              title={item.description}
                            >
                              <div 
                                className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                                  isActive ? 'bg-white bg-opacity-20' : 'bg-gray-100'
                                }`}
                              >
                                <Icon 
                                  className="w-4 h-4" 
                                  style={{ color: isActive ? 'white' : (item.color || section.color) }}
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate">{item.name}</div>
                                {item.badge && (
                                  <div className={`text-xs mt-0.5 ${isActive ? 'text-white text-opacity-80' : 'text-gray-500'}`}>
                                    {item.badge}
                                  </div>
                                )}
                              </div>
                            </Link>
                          );
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              );
            })}
          </nav>
        </div>

        {/* Sidebar footer */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">Health Researcher</p>
              <p className="text-xs text-gray-500 truncate"><EMAIL></p>
            </div>            <Link
              to="/settings"
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Settings className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </motion.div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top header */}
        <header className="bg-white shadow-sm border-b border-gray-200 lg:shadow-none">
          <div className="flex items-center justify-between h-16 px-6">
            {/* Mobile menu button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <Menu className="w-5 h-5" />
            </button>

            {/* Search bar */}
            <div className="flex-1 max-w-2xl mx-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search supplements, research, or ask AI..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Header actions */}
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <div className="relative">
                <button
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors relative"
                >
                  <Bell className="w-5 h-5" />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>
              </div>

              {/* User menu */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default EnhancedNavbar;