import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu, X, Search, Bell, User, Settings, ChevronRight, Globe,
  Activity, Network, Sparkles, Pill, BarChart3, Brain, Microscope,
  Heart, Zap, Shield, Target, Leaf, FlaskConical, Lightbulb,
  TrendingUp, BookOpen, Upload, Bot, Cpu, Workflow, Database, Layers
} from 'lucide-react';

// Modern navbar based on research from Context7, Brave Search, and Tavily
// Implements best practices from Airbnb, Dropbox, and modern React patterns

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: string;
  color?: string;
  description?: string;
  isNew?: boolean;
}

interface NavSection {
  title: string;
  items: NavItem[];
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

interface ModernNavbarProps {
  children: React.ReactNode;
}

const ModernNavbar: React.FC<ModernNavbarProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    coreplatform: true,
    neuroscience: true,
    research: false,
    aimodels: false,
    datamanagement: false
  });
  
  const location = useLocation();

  // Navigation sections populated from /docs/najlepsze documentation
  const navigationSections: NavSection[] = [
    {
      title: 'Core Platform',
      icon: Activity,
      color: '#3b82f6',
      description: 'Essential platform features',
      items: [
        { name: 'Dashboard', href: '/', icon: Activity, description: 'System overview and metrics' },
        { name: 'Knowledge Graph', href: '/graph', icon: Network, description: 'Interactive visualization' },
        { name: 'Infinite Graph', href: '/infinite-graph', icon: Sparkles, badge: 'Demo', description: 'Advanced exploration' },
        { name: 'Supplements', href: '/supplements', icon: Pill, badge: 'Enhanced', description: 'Supplement database' },
        { name: 'Global Search', href: '/search', icon: Search, description: 'AI-powered search' },
        { name: 'Analytics', href: '/analytics', icon: BarChart3, description: 'Data analytics' }
      ]
    }
  ];

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  const isActiveRoute = (href: string) => {
    return location.pathname === href;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : -320,
        }}
        className="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:translate-x-0 lg:static lg:inset-0"
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Suplementor</span>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-6 overflow-y-auto">
            {navigationSections.map((section, sectionIndex) => {
              const sectionKey = section.title.toLowerCase().replace(/\s+/g, '');
              const isExpanded = expandedSections[sectionKey];
              const SectionIcon = section.icon;

              return (
                <div key={sectionIndex} className="space-y-2">
                  <button
                    onClick={() => toggleSection(sectionKey)}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-8 h-8 rounded-lg flex items-center justify-center"
                        style={{ backgroundColor: `${section.color}20` }}
                      >
                        <SectionIcon
                          className="w-4 h-4"
                          style={{ color: section.color }}
                        />
                      </div>
                      <div className="text-left">
                        <div className="text-sm font-medium text-gray-900">
                          {section.title}
                        </div>
                        <div className="text-xs text-gray-500">
                          {section.description}
                        </div>
                      </div>
                    </div>
                    <ChevronRight
                      className={`w-4 h-4 text-gray-400 transition-transform ${
                        isExpanded ? 'rotate-90' : ''
                      }`}
                    />
                  </button>

                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="ml-4 space-y-1 overflow-hidden"
                      >
                        {section.items.map((item, itemIndex) => {
                          const ItemIcon = item.icon;
                          const isActive = isActiveRoute(item.href);

                          return (
                            <Link
                              key={itemIndex}
                              to={item.href}
                              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                                isActive
                                  ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                              }`}
                            >
                              <ItemIcon className="w-4 h-4" />
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium">
                                    {item.name}
                                  </span>
                                  {item.badge && (
                                    <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                      {item.badge}
                                    </span>
                                  )}
                                  {item.isNew && (
                                    <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                      New
                                    </span>
                                  )}
                                </div>
                                {item.description && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {item.description}
                                  </div>
                                )}
                              </div>
                            </Link>
                          );
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              );
            })}
          </nav>
        </div>
      </motion.div>

      {/* Main content */}
      <div className="lg:pl-80">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="relative flex flex-1">
              {/* Search can be added here */}
            </div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <Bell className="h-6 w-6" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <Settings className="h-6 w-6" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <User className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-10">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ModernNavbar;