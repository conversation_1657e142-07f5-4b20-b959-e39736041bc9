import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu, X, Search, Bell, User, Settings, ChevronRight, Globe,
  Activity, Network, Sparkles, Pill, BarChart3, Brain, Microscope,
  Heart, Zap, Shield, Target, Leaf, FlaskConical, Lightbulb,
  TrendingUp, BookOpen, Upload, Bot, Cpu, Workflow, Database, Layers
} from 'lucide-react';

// Modern navbar based on research from Context7, Brave Search, and Tavily
// Implements best practices from Airbnb, Dropbox, and modern React patterns

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: string;
  color?: string;
  description?: string;
  isNew?: boolean;
}

interface NavSection {
  title: string;
  items: NavItem[];
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

interface ModernNavbarProps {
  children: React.ReactNode;
}

const ModernNavbar: React.FC<ModernNavbarProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    coreplatform: true,
    neuroscience: true,
    research: false,
    aimodels: false,
    datamanagement: false
  });
  
  const location = useLocation();

  // Navigation sections populated from /docs/najlepsze documentation
  const navigationSections: NavSection[] = [
    {
      title: 'Core Platform',
      icon: Activity,
      color: '#3b82f6',
      description: 'Essential platform features',
      items: [
        { name: 'Dashboard', href: '/', icon: Activity, description: 'System overview and metrics' },
        { name: 'Knowledge Graph', href: '/graph', icon: Network, description: 'Interactive visualization' },
        { name: 'Infinite Graph', href: '/infinite-graph', icon: Sparkles, badge: 'Demo', description: 'Advanced exploration' },
        { name: 'Supplements', href: '/supplements', icon: Pill, badge: 'Enhanced', description: 'Supplement database' },
        { name: 'Global Search', href: '/search', icon: Search, description: 'AI-powered search' },
        { name: 'Analytics', href: '/analytics', icon: BarChart3, description: 'Data analytics' }
      ]
    },