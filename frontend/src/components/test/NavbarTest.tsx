import React from 'react';
import { Brain, Microscope, Bot, Database, Activity, CheckCircle } from 'lucide-react';

const NavbarTest: React.FC = () => {
  const sections = [
    {
      name: 'Core Platform',
      icon: Activity,
      color: '#3b82f6',
      items: 6,
      description: 'Dashboard, Graph, Supplements, Search, Analytics'
    },
    {
      name: 'Neuroscience Intelligence',
      icon: Brain,
      color: '#8b5cf6',
      items: 8,
      description: 'Neuroregulation, Serotonin, Dopamine, GABA, Acetylcholine, Braverman Test'
    },
    {
      name: 'Research & Discovery',
      icon: Microscope,
      color: '#10b981',
      items: 5,
      description: 'Research Hub, Knowledge Discovery, Research Runs, Papers'
    },
    {
      name: 'AI Models Management',
      icon: Bot,
      color: '#f59e0b',
      items: 4,
      description: 'AI Dashboard, Gemma3 Medical, Model Performance, AG-UI Status'
    },
    {
      name: 'Data Management',
      icon: Database,
      color: '#ec4899',
      items: 5,
      description: 'Neo4j, Weaviate, MongoDB, Generative Data, Data Review'
    }
  ];

  const totalItems = sections.reduce((sum, section) => sum + section.items, 0);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center mb-6">
        <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Navbar Status</h2>
          <p className="text-gray-600">Comprehensive navigation system loaded successfully</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {sections.map((section) => {
          const Icon = section.icon;
          return (
            <div
              key={section.name}
              className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-3">
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center mr-3"
                  style={{ backgroundColor: section.color }}
                >
                  <Icon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{section.name}</h3>
                  <p className="text-sm text-gray-500">{section.items} items</p>
                </div>
              </div>
              <p className="text-sm text-gray-600">{section.description}</p>
            </div>
          );
        })}
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Implementation Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Total Sections:</span>
            <p className="text-2xl font-bold text-blue-600">{sections.length}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Total Items:</span>
            <p className="text-2xl font-bold text-green-600">{totalItems}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Tools Used:</span>
            <p className="text-sm text-gray-600">Context7, Tavily, Brave, Desktop Commander</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Status:</span>
            <p className="text-sm font-semibold text-green-600">✅ Fully Operational</p>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-900 mb-2">How to Access Enhanced Navbar:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>Mobile:</strong> Tap the menu button (☰) in the top-left corner</li>
          <li>• <strong>Desktop:</strong> Click "Enhanced Navbar" button or use the permanent sidebar</li>
          <li>• <strong>Navigation:</strong> Click section headers to expand/collapse categories</li>
          <li>• <strong>Features:</strong> Color-coded sections, badges, smooth animations, responsive design</li>
        </ul>
      </div>
    </div>
  );
};

export default NavbarTest;
