import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  DollarSign,
  Zap,
  Target,
  Users,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  RefreshCw,
  Download,
  Filter,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';

interface MetricCard {
  id: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color: string;
  description?: string;
}

interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string;
    fill?: boolean;
  }>;
}

interface PerformanceAnalyticsProps {
  timeRange?: '1h' | '24h' | '7d' | '30d';
  className?: string;
}

export const PerformanceAnalytics: React.FC<PerformanceAnalyticsProps> = ({
  timeRange = '24h',
  className = ''
}) => {
  const [metrics, setMetrics] = useState<MetricCard[]>([]);
  const [chartData, setChartData] = useState<Record<string, ChartData>>({});
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchAnalyticsData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockMetrics: MetricCard[] = [
        {
          id: 'total_requests',
          title: 'Total Requests',
          value: '4,480',
          change: 12.5,
          changeType: 'increase',
          icon: <Activity className="w-6 h-6" />,
          color: 'from-blue-500 to-indigo-600',
          description: 'AI model requests processed'
        },
        {
          id: 'avg_response_time',
          title: 'Avg Response Time',
          value: '1.59s',
          change: -8.3,
          changeType: 'decrease',
          icon: <Clock className="w-6 h-6" />,
          color: 'from-green-500 to-emerald-600',
          description: 'Average AI response time'
        },
        {
          id: 'quality_score',
          title: 'Quality Score',
          value: '89.2%',
          change: 3.7,
          changeType: 'increase',
          icon: <Target className="w-6 h-6" />,
          color: 'from-purple-500 to-violet-600',
          description: 'Average response quality'
        },
        {
          id: 'cost_saved',
          title: 'Cost Saved',
          value: '$1,247',
          change: 15.2,
          changeType: 'increase',
          icon: <DollarSign className="w-6 h-6" />,
          color: 'from-orange-500 to-red-600',
          description: 'Savings from local models'
        },
        {
          id: 'active_users',
          title: 'Active Users',
          value: '23',
          change: 0,
          changeType: 'neutral',
          icon: <Users className="w-6 h-6" />,
          color: 'from-pink-500 to-rose-600',
          description: 'Currently active users'
        },
        {
          id: 'success_rate',
          title: 'Success Rate',
          value: '94.2%',
          change: 2.1,
          changeType: 'increase',
          icon: <Zap className="w-6 h-6" />,
          color: 'from-cyan-500 to-blue-600',
          description: 'Request success rate'
        }
      ];

      const mockChartData = {
        requests_over_time: {
          labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
          datasets: [
            {
              label: 'Requests',
              data: [120, 89, 156, 234, 189, 167],
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              fill: true
            }
          ]
        },
        model_performance: {
          labels: ['GPT-4', 'Claude-3', 'Gemini', 'Local'],
          datasets: [
            {
              label: 'Quality Score',
              data: [92, 90, 88, 85],
              backgroundColor: [
                'rgba(34, 197, 94, 0.8)',
                'rgba(59, 130, 246, 0.8)',
                'rgba(239, 68, 68, 0.8)',
                'rgba(147, 51, 234, 0.8)'
              ]
            }
          ]
        },
        response_times: {
          labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
          datasets: [
            {
              label: 'Response Time (ms)',
              data: [1800, 1650, 1590, 1520, 1590],
              borderColor: 'rgb(16, 185, 129)',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              fill: true
            }
          ]
        }
      };

      setMetrics(mockMetrics);
      setChartData(mockChartData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedTimeRange]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchAnalyticsData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getChangeIcon = (changeType: string) => {
    switch (changeType) {
      case 'increase': return <ArrowUp className="w-4 h-4 text-green-600" />;
      case 'decrease': return <ArrowDown className="w-4 h-4 text-red-600" />;
      default: return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const getChangeColor = (changeType: string) => {
    switch (changeType) {
      case 'increase': return 'text-green-600';
      case 'decrease': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const timeRangeOptions = [
    { value: '1h', label: '1 Hour' },
    { value: '24h', label: '24 Hours' },
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' }
  ];

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Performance Analytics</h2>
          <p className="text-gray-600">Real-time insights into system performance</p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Auto Refresh Toggle */}
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`p-2 rounded-lg transition-colors ${
              autoRefresh 
                ? 'bg-green-100 text-green-600 hover:bg-green-200' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <RefreshCw className={`w-5 h-5 ${autoRefresh ? 'animate-spin' : ''}`} />
          </button>

          {/* Manual Refresh */}
          <button
            onClick={fetchAnalyticsData}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Download className="w-5 h-5" />
          </button>

          {/* Filters */}
          <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <Filter className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Last Updated */}
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <Calendar className="w-4 h-4" />
        <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
        {autoRefresh && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Auto-refreshing</span>
          </div>
        )}
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <AnimatePresence>
          {metrics.map((metric, index) => (
            <motion.div
              key={metric.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
            >
              <div className={`bg-gradient-to-r ${metric.color} p-4 text-white`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {metric.icon}
                    <div>
                      <p className="text-sm opacity-90">{metric.title}</p>
                      <p className="text-2xl font-bold">{metric.value}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between">
                  <div className={`flex items-center space-x-1 ${getChangeColor(metric.changeType)}`}>
                    {getChangeIcon(metric.changeType)}
                    <span className="text-sm font-medium">
                      {Math.abs(metric.change)}%
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">vs last period</span>
                </div>
                {metric.description && (
                  <p className="text-xs text-gray-600 mt-2">{metric.description}</p>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Requests Over Time */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Requests Over Time</h3>
            <LineChart className="w-5 h-5 text-gray-600" />
          </div>
          
          <div className="h-64 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-blue-500 mx-auto mb-2" />
              <p className="text-gray-600">Chart visualization would go here</p>
              <p className="text-sm text-gray-500">Integration with Chart.js or D3.js</p>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-blue-600">1,247</p>
              <p className="text-sm text-gray-600">Peak Requests</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">156</p>
              <p className="text-sm text-gray-600">Avg/Hour</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-purple-600">94.2%</p>
              <p className="text-sm text-gray-600">Success Rate</p>
            </div>
          </div>
        </motion.div>

        {/* Model Performance */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Model Performance</h3>
            <PieChart className="w-5 h-5 text-gray-600" />
          </div>

          <div className="h-64 flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg">
            <div className="text-center">
              <PieChart className="w-12 h-12 text-green-500 mx-auto mb-2" />
              <p className="text-gray-600">Pie chart visualization would go here</p>
              <p className="text-sm text-gray-500">Model quality comparison</p>
            </div>
          </div>

          <div className="mt-4 space-y-2">
            {[
              { name: 'GPT-4 Turbo', quality: 92, color: 'bg-green-500' },
              { name: 'Claude-3 Sonnet', quality: 90, color: 'bg-blue-500' },
              { name: 'Gemini Pro', quality: 88, color: 'bg-red-500' },
              { name: 'Local Gemma3', quality: 85, color: 'bg-purple-500' }
            ].map((model, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 ${model.color} rounded-full`}></div>
                  <span className="text-sm text-gray-700">{model.name}</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{model.quality}%</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Response Times */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Response Times</h3>
            <Clock className="w-5 h-5 text-gray-600" />
          </div>

          <div className="h-64 flex items-center justify-center bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg">
            <div className="text-center">
              <TrendingUp className="w-12 h-12 text-purple-500 mx-auto mb-2" />
              <p className="text-gray-600">Line chart visualization would go here</p>
              <p className="text-sm text-gray-500">Response time trends</p>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">1.59s</p>
              <p className="text-sm text-gray-600">Current Avg</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">0.82s</p>
              <p className="text-sm text-gray-600">Best Time</p>
            </div>
          </div>
        </motion.div>

        {/* System Health */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
            <Activity className="w-5 h-5 text-gray-600" />
          </div>

          <div className="space-y-4">
            {[
              { name: 'API Endpoints', status: 'healthy', uptime: '99.9%', color: 'text-green-600' },
              { name: 'Database', status: 'healthy', uptime: '99.8%', color: 'text-green-600' },
              { name: 'Cache Layer', status: 'healthy', uptime: '99.7%', color: 'text-green-600' },
              { name: 'WebSocket', status: 'healthy', uptime: '99.5%', color: 'text-green-600' },
              { name: 'AI Models', status: 'degraded', uptime: '98.2%', color: 'text-yellow-600' }
            ].map((service, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    service.status === 'healthy' ? 'bg-green-500' : 
                    service.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm font-medium text-gray-900">{service.name}</span>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${service.color}`}>{service.status}</p>
                  <p className="text-xs text-gray-600">{service.uptime} uptime</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">All systems operational</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">Last incident: 3 days ago</p>
          </div>
        </motion.div>
      </div>

      {/* Real-time Activity Feed */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Real-time Activity</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live feed</span>
          </div>
        </div>

        <div className="space-y-3 max-h-64 overflow-y-auto">
          {[
            { time: '2 seconds ago', event: 'GPT-4 Turbo processed research query', type: 'success' },
            { time: '15 seconds ago', event: 'New user joined collaborative workspace', type: 'info' },
            { time: '32 seconds ago', event: 'Claude-3 Sonnet completed analysis task', type: 'success' },
            { time: '1 minute ago', event: 'Cache hit rate improved to 94.2%', type: 'success' },
            { time: '2 minutes ago', event: 'Local model fallback activated', type: 'warning' },
            { time: '3 minutes ago', event: 'Research strategy adapted for user context', type: 'info' }
          ].map((activity, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <div className={`w-2 h-2 rounded-full ${
                activity.type === 'success' ? 'bg-green-500' :
                activity.type === 'warning' ? 'bg-yellow-500' :
                activity.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`}></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">{activity.event}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default PerformanceAnalytics;
