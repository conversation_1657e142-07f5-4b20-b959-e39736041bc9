import React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Pill, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { NeurotransmitterProfile } from '@/data/neurotransmitterData';

interface NeurotransmitterCardProps {
  neurotransmitter: NeurotransmitterProfile;
  currentLevel?: number;
  showDetails?: boolean;
  onDetailsClick?: () => void;
  className?: string;
}

const NeurotransmitterCard: React.FC<NeurotransmitterCardProps> = ({
  neurotransmitter,
  currentLevel = 50,
  showDetails = true,
  onDetailsClick,
  className = ''
}) => {
  const { name, description, primaryFunctions, deficiencySymptoms, supportingSupplements, optimalRange, color } = neurotransmitter;
  
  // Calculate level status
  const isOptimal = currentLevel >= optimalRange[0] && currentLevel <= optimalRange[1];
  const isLow = currentLevel < optimalRange[0];
  const isHigh = currentLevel > optimalRange[1];
  
  // Progress angle for circular progress
  const progressAngle = (currentLevel / 100) * 360;
  
  const getLevelStatus = () => {
    if (isOptimal) return { text: 'Optimal', icon: CheckCircle, color: 'text-green-600' };
    if (isLow) return { text: 'Low', icon: TrendingDown, color: 'text-red-600' };
    return { text: 'High', icon: TrendingUp, color: 'text-orange-600' };
  };
  
  const status = getLevelStatus();
  const StatusIcon = status.icon;

  return (
    <motion.div
      className={`cosmic-neurotransmitter-card cosmic-neurotransmitter-card--${neurotransmitter.id} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -4 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div 
            className="w-12 h-12 rounded-full flex items-center justify-center text-white"
            style={{ background: neurotransmitter.gradient }}
          >
            <Brain className="w-6 h-6" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">{name}</h3>
            <div className="flex items-center space-x-2">
              <StatusIcon className={`w-4 h-4 ${status.color}`} />
              <span className={`text-sm font-medium ${status.color}`}>
                {status.text}
              </span>
            </div>
          </div>
        </div>
        
        {/* Progress Ring */}
        <div className="relative">
          <div 
            className="cosmic-progress-ring cosmic-progress-ring--serotonin"
            style={{ 
              '--progress-angle': `${progressAngle}deg`,
              background: `conic-gradient(from 0deg, ${color} 0deg, ${color} ${progressAngle}deg, #e5e7eb ${progressAngle}deg, #e5e7eb 360deg)`
            }}
          >
            <div className="cosmic-progress-ring__inner">
              <div className="cosmic-progress-ring__value">{currentLevel}</div>
              <div className="cosmic-progress-ring__label">Level</div>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-600 mb-6 leading-relaxed">{description}</p>

      {/* Key Functions */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
          <Activity className="w-4 h-4 mr-2" style={{ color }} />
          Primary Functions
        </h4>
        <div className="grid grid-cols-1 gap-2">
          {primaryFunctions.slice(0, 3).map((func, index) => (
            <div key={index} className="flex items-center text-sm text-gray-600">
              <div 
                className="w-2 h-2 rounded-full mr-3"
                style={{ backgroundColor: color }}
              />
              {func}
            </div>
          ))}
          {primaryFunctions.length > 3 && (
            <div className="text-sm text-gray-500 italic">
              +{primaryFunctions.length - 3} more functions
            </div>
          )}
        </div>
      </div>

      {/* Current Status Indicators */}
      {!isOptimal && (
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <AlertTriangle className="w-4 h-4 mr-2 text-orange-500" />
            {isLow ? 'Deficiency Symptoms' : 'Excess Symptoms'}
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {(isLow ? deficiencySymptoms : neurotransmitter.excessSymptoms).slice(0, 3).map((symptom, index) => (
              <div key={index} className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 rounded-full mr-3 bg-orange-400" />
                {symptom}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Supporting Supplements */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
          <Pill className="w-4 h-4 mr-2" style={{ color }} />
          Key Supplements
        </h4>
        <div className="flex flex-wrap gap-2">
          {supportingSupplements.slice(0, 4).map((supplement, index) => (
            <span
              key={index}
              className="px-3 py-1 text-xs font-medium rounded-full border"
              style={{ 
                borderColor: color + '40',
                backgroundColor: color + '10',
                color: color
              }}
            >
              {supplement}
            </span>
          ))}
          {supportingSupplements.length > 4 && (
            <span className="px-3 py-1 text-xs font-medium text-gray-500 rounded-full border border-gray-200 bg-gray-50">
              +{supportingSupplements.length - 4} more
            </span>
          )}
        </div>
      </div>

      {/* Optimal Range */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Optimal Range</span>
          <span className="font-medium text-gray-900">
            {optimalRange[0]}% - {optimalRange[1]}%
          </span>
        </div>
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div
            className="h-2 rounded-full"
            style={{
              background: neurotransmitter.gradient,
              width: `${(optimalRange[1] - optimalRange[0])}%`,
              marginLeft: `${optimalRange[0]}%`
            }}
          />
          <div
            className="relative -mt-2 w-1 h-2 bg-gray-800 rounded-full"
            style={{ marginLeft: `${currentLevel}%` }}
          />
        </div>
      </div>

      {/* Action Buttons */}
      {showDetails && (
        <div className="flex space-x-3">
          <button
            onClick={onDetailsClick}
            className="flex-1 cosmic-button cosmic-button--outline text-sm py-2"
            style={{ borderColor: color, color: color }}
          >
            <Info className="w-4 h-4 mr-2" />
            View Details
          </button>
          <button
            className="flex-1 cosmic-button text-sm py-2 text-white"
            style={{ background: neurotransmitter.gradient }}
          >
            <Pill className="w-4 h-4 mr-2" />
            Get Supplements
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default NeurotransmitterCard;
