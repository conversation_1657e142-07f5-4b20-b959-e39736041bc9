import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, Progress } from '@/components/ui';
import { Brain, Activity, TestTube2 } from 'lucide-react';

interface NeurotransmitterLevel {
  name: string;
  value: number; // 0-100 percentage
  optimalRange: [number, number];
}

interface CognitiveMetric {
  name: string;
  current: number;
  baseline: number;
}

interface SupplementRecommendation {
  name: string;
  dosage: string;
  purpose: string;
}

interface NeuroregulationCardProps {
  bravermanResults: {
    dopamine: number;
    serotonin: number;
    gaba: number;
    acetylcholine: number;
    dominantType: 'dopamine' | 'serotonin' | 'gaba' | 'acetylcholine';
  };
  cognitiveMetrics: CognitiveMetric[];
  recommendations: SupplementRecommendation[];
}

const NeuroregulationCard: React.FC<NeuroregulationCardProps> = ({
  bravermanResults,
  cognitiveMetrics,
  recommendations
}) => {
  const neurotransmitters: NeurotransmitterLevel[] = [
    { name: 'Dopamine', value: bravermanResults.dopamine, optimalRange: [40, 60] },
    { name: 'Serotonin', value: bravermanResults.serotonin, optimalRange: [40, 60] },
    { name: 'GABA', value: bravermanResults.gaba, optimalRange: [40, 60] },
    { name: 'Acetylcholine', value: bravermanResults.acetylcholine, optimalRange: [40, 60] }
  ];

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-6 h-6 text-indigo-600" />
          Neuroregulation Profile
        </CardTitle>
        <div className="text-sm text-gray-500">
          Based on Braverman Test Results
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Neurotransmitter Levels */}
        <div>
          <h3 className="font-medium mb-3 flex items-center gap-2">
            <Activity className="w-4 h-4 text-rose-500" />
            Neurotransmitter Levels
          </h3>
          <div className="space-y-4">
            {neurotransmitters.map((nt) => (
              <div key={nt.name} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{nt.name}</span>
                  <span className={nt.value < nt.optimalRange[0] || nt.value > nt.optimalRange[1] 
                    ? "text-rose-500 font-medium" : "text-green-500"}>
                    {nt.value}%
                  </span>
                </div>
                <Progress 
                  value={nt.value} 
                  className={nt.value < nt.optimalRange[0] 
                    ? "bg-yellow-500" 
                    : nt.value > nt.optimalRange[1] 
                      ? "bg-purple-500" 
                      : "bg-green-500"}
                />
                <div className="text-xs text-gray-500">
                  Optimal range: {nt.optimalRange[0]}% - {nt.optimalRange[1]}%
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cognitive Metrics */}
        <div>
          <h3 className="font-medium mb-3">Cognitive Metrics</h3>
          <div className="grid grid-cols-2 gap-4">
            {cognitiveMetrics.map((metric) => (
              <Card key={metric.name} className="p-4">
                <div className="text-sm text-gray-500 mb-1">{metric.name}</div>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{metric.current}</span>
                  <span className="text-sm text-gray-500">/ {metric.baseline}</span>
                </div>
                <div className={`text-xs mt-1 ${metric.current >= metric.baseline ? 'text-green-500' : 'text-amber-500'}`}>
                  {metric.current >= metric.baseline ? 'Above baseline' : 'Below baseline'}
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Supplement Recommendations */}
        <div>
          <h3 className="font-medium mb-3 flex items-center gap-2">
            <TestTube2 className="w-4 h-4 text-emerald-500" />
            Personalized Recommendations
          </h3>
          <div className="space-y-3">
            {recommendations.map((rec) => (
              <div key={rec.name} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="bg-emerald-100 p-2 rounded-full">
                  <TestTube2 className="w-5 h-5 text-emerald-600" />
                </div>
                <div>
                  <div className="font-medium">{rec.name}</div>
                  <div className="text-sm text-gray-600">Dosage: {rec.dosage}</div>
                  <div className="text-sm text-gray-600">Purpose: {rec.purpose}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NeuroregulationCard;