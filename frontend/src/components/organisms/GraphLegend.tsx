import React from 'react';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import Badge from '@/components/atoms/Badge';
import { 
  Pill, 
  Droplets, 
  Heart, 
  Shield, 
  Zap, 
  Leaf, 
  Brain, 
  Activity, 
  Info,
  Minus
} from 'lucide-react';

interface GraphLegendProps {
  className?: string;
}

const GraphLegend: React.FC<GraphLegendProps> = ({ className = '' }) => {
  const nodeCategories = [
    { 
      name: 'Vitamins', 
      color: '#3b82f6', 
      icon: Pill, 
      description: 'Essential vitamins (A, C, D, E, B-complex)',
      size: 'Standard'
    },
    { 
      name: 'Minerals', 
      color: '#10b981', 
      icon: Droplets, 
      description: 'Essential minerals and trace elements',
      size: 'Standard'
    },
    { 
      name: 'Omega-3', 
      color: '#8b5cf6', 
      icon: Heart, 
      description: 'Essential fatty acids (EPA, DHA)',
      size: 'Standard'
    },
    { 
      name: 'Probiotics', 
      color: '#14b8a6', 
      icon: Shield, 
      description: 'Beneficial bacteria for gut health',
      size: 'Standard'
    },
    { 
      name: 'Amino Acids', 
      color: '#f97316', 
      icon: Zap, 
      description: 'Building blocks of proteins',
      size: 'Standard'
    },
    { 
      name: 'Herbs', 
      color: '#10b981', 
      icon: Leaf, 
      description: 'Herbal and botanical supplements',
      size: 'Standard'
    },
    { 
      name: 'Adaptogens', 
      color: '#047857', 
      icon: Brain, 
      description: 'Stress-adapting herbs',
      size: 'Standard'
    },
    { 
      name: 'Health Areas', 
      color: '#ef4444', 
      icon: Activity, 
      description: 'Target health systems',
      size: 'Large (1.5x)'
    },
    { 
      name: 'Effects', 
      color: '#f59e0b', 
      icon: Info, 
      description: 'Biological effects and mechanisms',
      size: 'Medium (1.2x)'
    }
  ];

  const relationshipTypes = [
    {
      name: 'SUPPORTS',
      color: '#10b981',
      style: 'solid',
      width: 'Variable',
      description: 'Positive support relationship'
    },
    {
      name: 'PRODUCES',
      color: '#059669',
      style: 'solid',
      width: 'Variable',
      description: 'Directly produces an effect'
    },
    {
      name: 'ENHANCES_ABSORPTION',
      color: '#047857',
      style: 'solid',
      width: 'Variable',
      description: 'Improves absorption of another supplement'
    },
    {
      name: 'SYNERGISTIC_WITH',
      color: '#065f46',
      style: 'solid',
      width: 'Variable',
      description: 'Works better when combined'
    },
    {
      name: 'REQUIRES',
      color: '#3b82f6',
      style: 'solid',
      width: 'Variable',
      description: 'Needs another supplement to function'
    },
    {
      name: 'COMPETES_WITH',
      color: '#f59e0b',
      style: 'dashed',
      width: 'Variable',
      description: 'Competes for absorption or function'
    },
    {
      name: 'CONTRAINDICATED_WITH',
      color: '#dc2626',
      style: 'dashed',
      width: 'Variable',
      description: 'Should not be taken together'
    }
  ];

  const strengthLevels = [
    { name: 'High', width: '3px', opacity: '0.8', description: 'Strong evidence' },
    { name: 'Medium', width: '2px', opacity: '0.6', description: 'Moderate evidence' },
    { name: 'Low', width: '1.5px', opacity: '0.4', description: 'Limited evidence' }
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Node Categories Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Node Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {nodeCategories.map((category) => {
              const Icon = category.icon;
              return (
                <div key={category.name} className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: category.color }}
                  >
                    <Icon className="w-2.5 h-2.5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">{category.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {category.size}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 truncate">
                      {category.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Relationship Types Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Relationship Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {relationshipTypes.map((relationship) => (
              <div key={relationship.name} className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Minus 
                    className="w-6 h-0.5" 
                    style={{ 
                      color: relationship.color,
                      strokeDasharray: relationship.style === 'dashed' ? '3,3' : 'none'
                    }} 
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">
                      {relationship.name.replace(/_/g, ' ')}
                    </span>
                    <Badge
                      variant="secondary"
                      className={`text-xs border-[${relationship.color}] text-[${relationship.color}]`}
                    >
                      {relationship.style}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600">
                    {relationship.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Relationship Strength Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Relationship Strength</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {strengthLevels.map((strength) => (
              <div key={strength.name} className="flex items-center space-x-3">
                <div 
                  className="w-8 h-0.5 bg-gray-600"
                  style={{ 
                    height: strength.width,
                    opacity: strength.opacity 
                  }}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">{strength.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {strength.width}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600">
                    {strength.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Interaction Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Interaction Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span><strong>Click</strong> nodes to view detailed information</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span><strong>Hover</strong> over elements for quick tooltips</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span><strong>Drag</strong> nodes to reposition them</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span><strong>Zoom</strong> and pan to explore the graph</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span><strong>Filter</strong> categories and relationships</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GraphLegend;
