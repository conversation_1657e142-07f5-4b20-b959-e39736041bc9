import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';
import { agenticAPI } from '@/services/api';
import {
  Terminal,
  Play,
  Square,
  RefreshCw,
  Zap,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Bot,
  Brain,
  Search,
  Shield,
  TrendingUp,
  Sparkles,
  Code,
  Database,
  Wifi,
  WifiOff
} from 'lucide-react';

interface DebugLog {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'success';
  category: 'api' | 'research' | 'agent' | 'system' | 'lm-studio';
  message: string;
  data?: any;
}

interface SystemStatus {
  api: boolean;
  lmStudio: boolean;
  backend: boolean;
  agents: number;
  activeFlows: number;
}

const DebugConsole: React.FC = () => {
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    api: false,
    lmStudio: false,
    backend: false,
    agents: 0,
    activeFlows: 0
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom of logs
  useEffect(() => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  // Add log entry
  const addLog = (level: DebugLog['level'], category: DebugLog['category'], message: string, data?: any) => {
    const newLog: DebugLog = {
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level,
      category,
      message,
      data
    };
    setLogs(prev => [...prev.slice(-99), newLog]); // Keep last 100 logs
  };

  // System health check
  const checkSystemHealth = async () => {
    addLog('info', 'system', '🔍 Checking system health...');
    
    try {
      // Check backend health
      const healthResponse = await fetch('http://localhost:5420/api/monitoring/health');
      const healthData = await healthResponse.json();
      
      // Check system status
      const systemResponse = await fetch('http://localhost:5420/api/monitoring/system');
      const systemData = await systemResponse.json();
      
      const newStatus: SystemStatus = {
        api: healthData.success,
        lmStudio: healthData.checks.lmStudio,
        backend: healthResponse.ok,
        agents: systemData.data?.totalAgents || 0,
        activeFlows: systemData.data?.activeAgents || 0
      };
      
      setSystemStatus(newStatus);
      
      if (newStatus.api && newStatus.lmStudio && newStatus.backend) {
        addLog('success', 'system', '✅ All systems operational!', newStatus);
      } else {
        addLog('warning', 'system', '⚠️ Some systems are down', newStatus);
      }
      
    } catch (error) {
      addLog('error', 'system', `❌ Health check failed: ${error.message}`);
      setSystemStatus({
        api: false,
        lmStudio: false,
        backend: false,
        agents: 0,
        activeFlows: 0
      });
    }
  };

  // Test LM Studio connection
  const testLMStudio = async () => {
    addLog('info', 'lm-studio', '🤖 Testing LM Studio connection...');
    
    try {
      const response = await fetch('http://192.168.0.179:1234/v1/models');
      const models = await response.json();
      
      if (response.ok && models.data) {
        addLog('success', 'lm-studio', `✅ LM Studio connected! Models: ${models.data.map(m => m.id).join(', ')}`);
        return true;
      } else {
        addLog('error', 'lm-studio', '❌ LM Studio not responding');
        return false;
      }
    } catch (error) {
      addLog('error', 'lm-studio', `❌ LM Studio connection failed: ${error.message}`);
      return false;
    }
  };

  // Test autonomous research
  const testAutonomousResearch = async () => {
    addLog('info', 'research', '🚀 Testing autonomous research...');
    
    try {
      const response = await fetch('http://localhost:5420/api/enhanced-research/autonomous', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          supplement: 'Test Vitamin C',
          goals: ['Immune Support'],
          depth: 'basic'
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        addLog('success', 'research', `✅ Research started! Flow ID: ${result.flowId}`);
        
        // Monitor the flow
        setTimeout(async () => {
          try {
            const statusResponse = await fetch(`http://localhost:5420/api/enhanced-research/flows/${result.flowId}/status`);
            const statusData = await statusResponse.json();
            addLog('info', 'research', `📊 Flow status: ${statusData.data?.status} (${statusData.data?.progress}%)`);
          } catch (error) {
            addLog('warning', 'research', `⚠️ Could not check flow status: ${error.message}`);
          }
        }, 5000);
        
        return true;
      } else {
        addLog('error', 'research', `❌ Research failed: ${result.error}`);
        return false;
      }
    } catch (error) {
      addLog('error', 'research', `❌ Research test failed: ${error.message}`);
      return false;
    }
  };

  // Run comprehensive tests
  const runComprehensiveTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    addLog('info', 'system', '🧪 Starting comprehensive test suite...');
    
    const tests = [
      { name: 'System Health Check', test: checkSystemHealth },
      { name: 'LM Studio Connection', test: testLMStudio },
      { name: 'Autonomous Research', test: testAutonomousResearch }
    ];
    
    const results = [];
    
    for (const { name, test } of tests) {
      try {
        const startTime = Date.now();
        const success = await test();
        const duration = Date.now() - startTime;
        
        results.push({
          name,
          success,
          duration,
          timestamp: new Date()
        });
        
        addLog(success ? 'success' : 'error', 'system', 
          `${success ? '✅' : '❌'} ${name}: ${success ? 'PASSED' : 'FAILED'} (${duration}ms)`);
        
      } catch (error) {
        results.push({
          name,
          success: false,
          duration: 0,
          error: error.message,
          timestamp: new Date()
        });
        
        addLog('error', 'system', `❌ ${name}: ERROR - ${error.message}`);
      }
    }
    
    setTestResults(results);
    setIsRunningTests(false);
    
    const passedTests = results.filter(r => r.success).length;
    addLog('info', 'system', `🏁 Test suite completed: ${passedTests}/${results.length} tests passed`);
  };

  // Start monitoring
  const startMonitoring = () => {
    setIsMonitoring(true);
    addLog('info', 'system', '📡 Starting real-time monitoring...');
    
    const interval = setInterval(async () => {
      await checkSystemHealth();
    }, 10000); // Check every 10 seconds
    
    // Store interval ID for cleanup
    (window as any).monitoringInterval = interval;
  };

  // Stop monitoring
  const stopMonitoring = () => {
    setIsMonitoring(false);
    addLog('info', 'system', '⏹️ Stopping monitoring...');
    
    if ((window as any).monitoringInterval) {
      clearInterval((window as any).monitoringInterval);
      delete (window as any).monitoringInterval;
    }
  };

  // Clear logs
  const clearLogs = () => {
    setLogs([]);
    addLog('info', 'system', '🧹 Debug console cleared');
  };

  // Get log level color
  const getLogLevelColor = (level: DebugLog['level']) => {
    switch (level) {
      case 'success': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-blue-600';
    }
  };

  // Get log level icon
  const getLogLevelIcon = (level: DebugLog['level']) => {
    switch (level) {
      case 'success': return <CheckCircle className="w-3 h-3" />;
      case 'warning': return <AlertTriangle className="w-3 h-3" />;
      case 'error': return <AlertTriangle className="w-3 h-3" />;
      default: return <Activity className="w-3 h-3" />;
    }
  };

  // Get category icon
  const getCategoryIcon = (category: DebugLog['category']) => {
    switch (category) {
      case 'api': return <Database className="w-3 h-3" />;
      case 'research': return <Search className="w-3 h-3" />;
      case 'agent': return <Bot className="w-3 h-3" />;
      case 'lm-studio': return <Brain className="w-3 h-3" />;
      default: return <Code className="w-3 h-3" />;
    }
  };

  // Initialize
  useEffect(() => {
    addLog('info', 'system', '🚀 Debug Console initialized - Siła grzmotu, pełna moc wiatru! ⚡');
    checkSystemHealth();
  }, []);

  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Terminal className="w-5 h-5 text-blue-600" />
            System Status
            <Sparkles className="w-4 h-4 text-yellow-500" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="flex items-center gap-2">
              {systemStatus.backend ? <Wifi className="w-4 h-4 text-green-600" /> : <WifiOff className="w-4 h-4 text-red-600" />}
              <span className="text-sm">Backend</span>
              <Badge className={systemStatus.backend ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {systemStatus.backend ? 'UP' : 'DOWN'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Brain className="w-4 h-4 text-purple-600" />
              <span className="text-sm">LM Studio</span>
              <Badge className={systemStatus.lmStudio ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {systemStatus.lmStudio ? 'CONNECTED' : 'OFFLINE'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Bot className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Agents</span>
              <Badge className="bg-blue-100 text-blue-800">
                {systemStatus.agents}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-orange-600" />
              <span className="text-sm">Active Flows</span>
              <Badge className="bg-orange-100 text-orange-800">
                {systemStatus.activeFlows}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm">Monitoring</span>
              <Badge className={isMonitoring ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                {isMonitoring ? 'ACTIVE' : 'INACTIVE'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-yellow-600" />
            Debug Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={checkSystemHealth}
              className="flex items-center gap-2"
              variant="outline"
            >
              <RefreshCw className="w-4 h-4" />
              Health Check
            </Button>
            
            <Button
              onClick={runComprehensiveTests}
              disabled={isRunningTests}
              className="flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              {isRunningTests ? 'Running Tests...' : 'Run Tests'}
            </Button>
            
            <Button
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              variant={isMonitoring ? "destructive" : "primary"}
              className="flex items-center gap-2"
            >
              {isMonitoring ? <Square className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isMonitoring ? 'Stop Monitor' : 'Start Monitor'}
            </Button>
            
            <Button
              onClick={clearLogs}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Terminal className="w-4 h-4" />
              Clear Logs
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    {result.success ? 
                      <CheckCircle className="w-4 h-4 text-green-600" /> : 
                      <AlertTriangle className="w-4 h-4 text-red-600" />
                    }
                    <span className="font-medium">{result.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {result.success ? 'PASS' : 'FAIL'}
                    </Badge>
                    <span className="text-sm text-gray-500">{result.duration}ms</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Debug Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Terminal className="w-5 h-5 text-gray-600" />
            Debug Logs ({logs.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-96 overflow-y-auto">
            {logs.map(log => (
              <div key={log.id} className="flex items-start gap-2 mb-1">
                <span className="text-gray-500 text-xs">
                  {log.timestamp.toLocaleTimeString()}
                </span>
                <div className={`flex items-center gap-1 ${getLogLevelColor(log.level)}`}>
                  {getLogLevelIcon(log.level)}
                  {getCategoryIcon(log.category)}
                </div>
                <span className="flex-1">{log.message}</span>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugConsole;
