import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Brain, 
  Sparkles, 
  TrendingUp, 
  Clock, 
  Target,
  Lightbulb,
  MessageCircle,
  Zap,
  Star
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  intent?: {
    type: string;
    confidence: number;
    entities: string[];
    keywords: string[];
    domain: string;
    complexity: string;
    scope: string;
  };
  actions?: Array<{
    type: string;
    description: string;
    confidence: number;
  }>;
  recommendations?: Array<{
    type: string;
    title: string;
    description: string;
    confidence: number;
    priority: string;
  }>;
}

interface CognitiveChatProps {
  userId: string;
  sessionId: string;
  onMessageSent?: (message: string) => void;
  className?: string;
}

export const CognitiveChat: React.FC<CognitiveChatProps> = ({
  userId,
  sessionId,
  onMessageSent,
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      content: inputValue,
      type: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await fetch('/api/enhanced-ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          userId,
          sessionId,
          context: messages.slice(-5) // Last 5 messages for context
        }),
      });

      const data = await response.json();

      if (data.success) {
        const assistantMessage: Message = {
          id: `msg_${Date.now()}_assistant`,
          content: data.data.response,
          type: 'assistant',
          timestamp: new Date(),
          intent: data.data.intent,
          actions: data.data.actions,
          recommendations: data.data.recommendations
        };

        setMessages(prev => [...prev, assistantMessage]);
        onMessageSent?.(inputValue);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getIntentIcon = (type: string) => {
    switch (type) {
      case 'research': return <Brain className="w-4 h-4" />;
      case 'analysis': return <TrendingUp className="w-4 h-4" />;
      case 'synthesis': return <Sparkles className="w-4 h-4" />;
      case 'validation': return <Target className="w-4 h-4" />;
      case 'exploration': return <Lightbulb className="w-4 h-4" />;
      default: return <MessageCircle className="w-4 h-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-blue-600 bg-blue-100';
      case 'complex': return 'text-orange-600 bg-orange-100';
      case 'expert': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className={`flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-100 ${className}`}>
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-blue-200 p-4">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Cognitive Research Assistant</h3>
            <p className="text-sm text-gray-600">AI-powered research companion</p>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span>Enhanced AI</span>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-3xl ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                {/* Message Bubble */}
                <div
                  className={`px-4 py-3 rounded-2xl ${
                    message.type === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white ml-12'
                      : 'bg-white/90 backdrop-blur-sm text-gray-900 mr-12 shadow-lg border border-blue-100'
                  }`}
                >
                  <p className="text-sm leading-relaxed">{message.content}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className={`text-xs ${message.type === 'user' ? 'text-blue-100' : 'text-gray-500'}`}>
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>

                {/* Intent Analysis (for assistant messages) */}
                {message.type === 'assistant' && message.intent && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-3 mr-12"
                  >
                    <div className="bg-white/70 backdrop-blur-sm rounded-xl p-3 border border-blue-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="flex items-center space-x-1 text-sm font-medium text-gray-700">
                          {getIntentIcon(message.intent.type)}
                          <span>Intent: {message.intent.type}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3 text-yellow-500" />
                          <span className="text-xs text-gray-600">{message.intent.confidence}% confidence</span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-gray-500">Domain:</span>
                          <span className="ml-1 font-medium">{message.intent.domain}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Complexity:</span>
                          <span className={`ml-1 px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(message.intent.complexity)}`}>
                            {message.intent.complexity}
                          </span>
                        </div>
                      </div>

                      {message.intent.entities.length > 0 && (
                        <div className="mt-2">
                          <span className="text-xs text-gray-500">Entities:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {message.intent.entities.map((entity, index) => (
                              <span
                                key={index}
                                className="px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs"
                              >
                                {entity}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Actions (for assistant messages) */}
                {message.type === 'assistant' && message.actions && message.actions.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-3 mr-12"
                  >
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-3 border border-green-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Zap className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">Actions Taken</span>
                      </div>
                      <div className="space-y-2">
                        {message.actions.map((action, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-sm text-green-700">{action.description}</span>
                            <span className="text-xs text-green-600">{action.confidence}%</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Recommendations (for assistant messages) */}
                {message.type === 'assistant' && message.recommendations && message.recommendations.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-3 mr-12"
                  >
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-3 border border-purple-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Lightbulb className="w-4 h-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-800">Recommendations</span>
                      </div>
                      <div className="space-y-2">
                        {message.recommendations.map((rec, index) => (
                          <div key={index} className="border border-purple-200 rounded-lg p-2 bg-white/50">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-purple-800">{rec.title}</span>
                              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                                {rec.priority}
                              </span>
                            </div>
                            <p className="text-xs text-purple-700">{rec.description}</p>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-xs text-purple-600">Confidence: {rec.confidence}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Typing Indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl px-4 py-3 mr-12 shadow-lg border border-blue-100">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-600">AI is thinking...</span>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="bg-white/80 backdrop-blur-sm border-t border-blue-200 p-4">
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about research, analysis, or supplements..."
              className="w-full px-4 py-3 bg-white/90 backdrop-blur-sm border border-blue-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              rows={1}
              style={{ minHeight: '48px', maxHeight: '120px' }}
            />
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Send className="w-5 h-5" />
            )}
          </motion.button>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <div className="flex items-center space-x-2">
            <Clock className="w-3 h-3" />
            <span>Session: {sessionId.slice(-8)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CognitiveChat;
