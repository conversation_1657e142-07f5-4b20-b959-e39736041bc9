import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  MessageCircle,
  BarChart3,
  Users,
  Settings,
  Search,
  Sparkles,
  Zap,
  Target,
  Globe,
  Menu,
  X,
  Home,
  Activity,
  FileText,
  Layers
} from 'lucide-react';

// Import our enhanced components
import CognitiveChat from '../chat/CognitiveChat';
import AIModelsDashboard from '../ai/AIModelsDashboard';
import ResearchStrategySelector from '../research/ResearchStrategySelector';
import CollaborativeWorkspace from '../workspace/CollaborativeWorkspace';
import PerformanceAnalytics from '../analytics/PerformanceAnalytics';

interface TabConfig {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
  description: string;
  color: string;
}

interface UltimateResearchDashboardProps {
  userId?: string;
  sessionId?: string;
  className?: string;
}

export const UltimateResearchDashboard: React.FC<UltimateResearchDashboardProps> = ({
  userId = 'user_123',
  sessionId = 'session_456',
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('chat');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [systemStats, setSystemStats] = useState({
    activeUsers: 23,
    totalRequests: 4480,
    averageQuality: 89.2,
    costSaved: 1247
  });

  const tabs: TabConfig[] = [
    {
      id: 'chat',
      label: 'Cognitive Chat',
      icon: <MessageCircle className="w-5 h-5" />,
      component: CognitiveChat,
      description: 'AI-powered research assistant with natural language understanding',
      color: 'from-blue-500 to-indigo-600'
    },
    {
      id: 'models',
      label: 'AI Models',
      icon: <Brain className="w-5 h-5" />,
      component: AIModelsDashboard,
      description: 'Multi-model ensemble performance and monitoring',
      color: 'from-green-500 to-emerald-600'
    },
    {
      id: 'strategies',
      label: 'Research Strategies',
      icon: <Target className="w-5 h-5" />,
      component: ResearchStrategySelector,
      description: 'Adaptive research strategy selection and optimization',
      color: 'from-purple-500 to-violet-600'
    },
    {
      id: 'workspace',
      label: 'Collaborative Workspace',
      icon: <Users className="w-5 h-5" />,
      component: CollaborativeWorkspace,
      description: 'Real-time collaborative research environment',
      color: 'from-pink-500 to-rose-600'
    },
    {
      id: 'analytics',
      label: 'Performance Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      component: PerformanceAnalytics,
      description: 'Real-time system performance and insights',
      color: 'from-orange-500 to-red-600'
    }
  ];

  const activeTabConfig = tabs.find(tab => tab.id === activeTab);

  useEffect(() => {
    // Simulate real-time stats updates
    const interval = setInterval(() => {
      setSystemStats(prev => ({
        activeUsers: prev.activeUsers + Math.floor(Math.random() * 3) - 1,
        totalRequests: prev.totalRequests + Math.floor(Math.random() * 10),
        averageQuality: Math.max(85, Math.min(95, prev.averageQuality + (Math.random() - 0.5) * 2)),
        costSaved: prev.costSaved + Math.floor(Math.random() * 5)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const renderActiveComponent = () => {
    if (!activeTabConfig) return null;

    const Component = activeTabConfig.component;
    const props: any = {};

    // Pass specific props based on component type
    switch (activeTab) {
      case 'chat':
        props.userId = userId;
        props.sessionId = sessionId;
        break;
      case 'workspace':
        props.workspaceId = 'workspace_001';
        props.userId = userId;
        break;
      default:
        break;
    }

    return <Component {...props} className="h-full" />;
  };

  return (
    <div className={`flex h-screen bg-gray-100 ${className}`}>
      {/* Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 280, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            className="bg-white border-r border-gray-200 flex flex-col shadow-lg"
          >
            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Ultimate Research</h1>
                  <p className="text-sm text-gray-600">AI-Powered Platform</p>
                </div>
              </div>
            </div>

            {/* System Stats */}
            <div className="p-4 border-b border-gray-200">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">{systemStats.activeUsers}</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">Active Users</p>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">{systemStats.totalRequests.toLocaleString()}</span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">Requests</p>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-800">{systemStats.averageQuality.toFixed(1)}%</span>
                  </div>
                  <p className="text-xs text-purple-600 mt-1">Quality</p>
                </div>
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-orange-600" />
                    <span className="text-sm font-medium text-orange-800">${systemStats.costSaved}</span>
                  </div>
                  <p className="text-xs text-orange-600 mt-1">Saved</p>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto p-4">
              <nav className="space-y-2">
                {tabs.map((tab, index) => (
                  <motion.button
                    key={tab.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                      activeTab === tab.id
                        ? `bg-gradient-to-r ${tab.color} text-white shadow-lg`
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {tab.icon}
                    <div className="flex-1 text-left">
                      <p className="font-medium">{tab.label}</p>
                      <p className={`text-xs ${activeTab === tab.id ? 'text-white/80' : 'text-gray-500'}`}>
                        {tab.description}
                      </p>
                    </div>
                  </motion.button>
                ))}
              </nav>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {userId.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">User {userId.slice(-3)}</p>
                    <p className="text-xs text-gray-600">Premium Plan</p>
                  </div>
                </div>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>

              {activeTabConfig && (
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 bg-gradient-to-r ${activeTabConfig.color} rounded-lg flex items-center justify-center text-white`}>
                    {activeTabConfig.icon}
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">{activeTabConfig.label}</h2>
                    <p className="text-sm text-gray-600">{activeTabConfig.description}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {/* Search */}
              <div className="relative">
                <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Status Indicator */}
              <div className="flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">All Systems Operational</span>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                  <Globe className="w-5 h-5" />
                </button>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                  <Settings className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="h-full"
            >
              {renderActiveComponent()}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Floating Action Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
        onClick={() => setActiveTab('chat')}
      >
        <MessageCircle className="w-6 h-6" />
      </motion.button>

      {/* Background Particles Effect */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-blue-500/20 rounded-full"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            animate={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            transition={{
              duration: Math.random() * 20 + 10,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default UltimateResearchDashboard;
