import { GraphNode, GraphRelationship } from '@/types';

// 🧠 Comprehensive Neurotransmitter Knowledge Graph Data
// Based on neuroscience research and supplement interactions

export interface NeurotransmitterProfile {
  id: string;
  name: string;
  description: string;
  primaryFunctions: string[];
  deficiencySymptoms: string[];
  excessSymptoms: string[];
  naturalSources: string[];
  supportingSupplements: string[];
  antagonisticSupplements: string[];
  optimalRange: [number, number];
  color: string;
  gradient: string;
}

export const neurotransmitterProfiles: NeurotransmitterProfile[] = [
  {
    id: 'serotonin',
    name: 'Serotonin',
    description: 'The "happiness neurotransmitter" that regulates mood, sleep, appetite, and social behavior',
    primaryFunctions: [
      'Mood regulation and emotional stability',
      'Sleep-wake cycle control',
      'Appetite and digestion regulation',
      'Social behavior and empathy',
      'Pain perception modulation',
      'Cognitive flexibility'
    ],
    deficiencySymptoms: [
      'Depression and anxiety',
      'Insomnia and sleep disorders',
      'Increased appetite and cravings',
      'Social withdrawal',
      'Increased pain sensitivity',
      'Obsessive-compulsive behaviors'
    ],
    excessSymptoms: [
      'Serotonin syndrome (rare)',
      'Nausea and digestive issues',
      'Restlessness and agitation',
      'Confusion and disorientation',
      'Muscle rigidity',
      'Hyperthermia'
    ],
    naturalSources: [
      'Turkey and chicken',
      'Eggs and dairy products',
      'Salmon and tuna',
      'Pumpkin seeds',
      'Bananas and cherries',
      'Dark chocolate'
    ],
    supportingSupplements: [
      '5-HTP (5-Hydroxytryptophan)',
      'Tryptophan',
      'Vitamin B6 (Pyridoxine)',
      'Magnesium',
      'Zinc',
      'Omega-3 fatty acids',
      'Probiotics',
      'St. John\'s Wort'
    ],
    antagonisticSupplements: [
      'High-dose Tyrosine',
      'Excessive caffeine',
      'High-dose B-complex without balance'
    ],
    optimalRange: [40, 60],
    color: '#3b82f6',
    gradient: 'linear-gradient(135deg, #60a5fa 0%, #2563eb 100%)'
  },
  {
    id: 'dopamine',
    name: 'Dopamine',
    description: 'The "motivation neurotransmitter" that drives reward-seeking, focus, and motor control',
    primaryFunctions: [
      'Motivation and reward processing',
      'Focus and attention',
      'Motor control and coordination',
      'Goal-directed behavior',
      'Pleasure and satisfaction',
      'Working memory'
    ],
    deficiencySymptoms: [
      'Lack of motivation and drive',
      'Difficulty concentrating',
      'Fatigue and low energy',
      'Depression and apathy',
      'Motor control issues',
      'Anhedonia (inability to feel pleasure)'
    ],
    excessSymptoms: [
      'Hyperactivity and restlessness',
      'Impulsivity and risk-taking',
      'Addiction susceptibility',
      'Paranoia and psychosis (extreme cases)',
      'Insomnia',
      'Aggressive behavior'
    ],
    naturalSources: [
      'Lean proteins (chicken, fish)',
      'Almonds and walnuts',
      'Apples and bananas',
      'Beets and sesame seeds',
      'Green tea',
      'Dark chocolate'
    ],
    supportingSupplements: [
      'L-Tyrosine',
      'L-DOPA (Mucuna Pruriens)',
      'Iron',
      'Vitamin B6',
      'Folate',
      'Rhodiola Rosea',
      'Ginkgo Biloba',
      'Curcumin'
    ],
    antagonisticSupplements: [
      'High-dose Tryptophan',
      'Excessive Magnesium',
      'High-dose GABA supplements'
    ],
    optimalRange: [40, 60],
    color: '#10b981',
    gradient: 'linear-gradient(135deg, #34d399 0%, #059669 100%)'
  },
  {
    id: 'gaba',
    name: 'GABA',
    description: 'The "calming neurotransmitter" that promotes relaxation and reduces anxiety',
    primaryFunctions: [
      'Anxiety and stress reduction',
      'Muscle relaxation',
      'Sleep promotion',
      'Seizure prevention',
      'Blood pressure regulation',
      'Cognitive calmness'
    ],
    deficiencySymptoms: [
      'Anxiety and panic attacks',
      'Insomnia and restlessness',
      'Muscle tension and spasms',
      'Seizure susceptibility',
      'Hypertension',
      'Racing thoughts'
    ],
    excessSymptoms: [
      'Excessive sedation',
      'Cognitive impairment',
      'Memory problems',
      'Respiratory depression (extreme cases)',
      'Muscle weakness',
      'Coordination problems'
    ],
    naturalSources: [
      'Fermented foods (yogurt, kefir)',
      'Sprouted grains',
      'Broccoli and lentils',
      'Brown rice',
      'Oats and barley',
      'Cherries and berries'
    ],
    supportingSupplements: [
      'GABA supplements',
      'L-Theanine',
      'Magnesium',
      'Taurine',
      'Passionflower',
      'Valerian Root',
      'Lemon Balm',
      'Ashwagandha'
    ],
    antagonisticSupplements: [
      'High-dose stimulants',
      'Excessive caffeine',
      'High-dose Tyrosine'
    ],
    optimalRange: [40, 60],
    color: '#a855f7',
    gradient: 'linear-gradient(135deg, #c084fc 0%, #7c3aed 100%)'
  },
  {
    id: 'acetylcholine',
    name: 'Acetylcholine',
    description: 'The "learning neurotransmitter" that enhances memory, focus, and cognitive function',
    primaryFunctions: [
      'Memory formation and recall',
      'Learning and cognitive processing',
      'Attention and focus',
      'Muscle contraction',
      'Autonomic nervous system regulation',
      'Neuroplasticity enhancement'
    ],
    deficiencySymptoms: [
      'Memory problems and forgetfulness',
      'Difficulty learning new information',
      'Poor concentration',
      'Muscle weakness',
      'Dry mouth and eyes',
      'Cognitive decline'
    ],
    excessSymptoms: [
      'Muscle cramps and spasms',
      'Excessive salivation',
      'Nausea and vomiting',
      'Bradycardia (slow heart rate)',
      'Miosis (constricted pupils)',
      'Respiratory issues'
    ],
    naturalSources: [
      'Eggs (especially yolks)',
      'Fish and seafood',
      'Meat and poultry',
      'Nuts and seeds',
      'Cruciferous vegetables',
      'Whole grains'
    ],
    supportingSupplements: [
      'Alpha-GPC',
      'CDP-Choline (Citicoline)',
      'Choline Bitartrate',
      'Phosphatidylserine',
      'Huperzine A',
      'Bacopa Monnieri',
      'Lion\'s Mane Mushroom',
      'Ginkgo Biloba'
    ],
    antagonisticSupplements: [
      'Anticholinergic herbs',
      'High-dose Scopolamine-containing plants'
    ],
    optimalRange: [40, 60],
    color: '#f59e0b',
    gradient: 'linear-gradient(135deg, #fbbf24 0%, #d97706 100%)'
  }
];

// Extended supplement nodes with neurotransmitter focus
export const neurotransmitterSupplementNodes: GraphNode[] = [
  // Serotonin Support
  {
    id: '5-htp',
    labels: ['Supplement', 'Amino Acid', 'Serotonin Precursor'],
    properties: {
      name: '5-HTP (5-Hydroxytryptophan)',
      description: 'Direct precursor to serotonin, crosses blood-brain barrier',
      category: 'Amino Acids',
      targetNeurotransmitter: 'Serotonin',
      targetHealthArea: 'Mood, Sleep, Appetite',
      commonUses: 'Depression, insomnia, weight management',
      dosageRange: '50-300mg daily',
      safetyLevel: 'Generally Safe',
      mechanismOfAction: 'Converts directly to serotonin in the brain',
      evidenceLevel: 'Strong',
      interactions: 'Avoid with SSRIs, MAOIs'
    }
  },
  {
    id: 'tryptophan',
    labels: ['Supplement', 'Essential Amino Acid', 'Serotonin Precursor'],
    properties: {
      name: 'L-Tryptophan',
      description: 'Essential amino acid precursor to serotonin',
      category: 'Amino Acids',
      targetNeurotransmitter: 'Serotonin',
      targetHealthArea: 'Mood, Sleep',
      commonUses: 'Sleep disorders, mood support, anxiety',
      dosageRange: '500-2000mg daily',
      safetyLevel: 'Very Safe',
      mechanismOfAction: 'Converts to 5-HTP then serotonin',
      evidenceLevel: 'Strong',
      interactions: 'Competes with other amino acids'
    }
  },

  // Dopamine Support
  {
    id: 'l-tyrosine',
    labels: ['Supplement', 'Amino Acid', 'Dopamine Precursor'],
    properties: {
      name: 'L-Tyrosine',
      description: 'Amino acid precursor to dopamine and norepinephrine',
      category: 'Amino Acids',
      targetNeurotransmitter: 'Dopamine',
      targetHealthArea: 'Focus, Motivation, Stress Response',
      commonUses: 'ADHD, depression, stress, cognitive enhancement',
      dosageRange: '500-2000mg daily',
      safetyLevel: 'Generally Safe',
      mechanismOfAction: 'Converts to L-DOPA then dopamine',
      evidenceLevel: 'Strong',
      interactions: 'Avoid with MAOIs, thyroid medications'
    }
  },
  {
    id: 'mucuna-pruriens',
    labels: ['Supplement', 'Herb', 'Natural L-DOPA'],
    properties: {
      name: 'Mucuna Pruriens',
      description: 'Natural source of L-DOPA, direct dopamine precursor',
      category: 'Herbal Supplements',
      targetNeurotransmitter: 'Dopamine',
      targetHealthArea: 'Motivation, Mood, Motor Function',
      commonUses: 'Parkinson\'s support, depression, libido',
      dosageRange: '100-500mg daily (standardized extract)',
      safetyLevel: 'Generally Safe',
      mechanismOfAction: 'Provides L-DOPA which converts to dopamine',
      evidenceLevel: 'Strong',
      interactions: 'Monitor with Parkinson\'s medications'
    }
  },

  // GABA Support
  {
    id: 'l-theanine',
    labels: ['Supplement', 'Amino Acid', 'GABA Enhancer'],
    properties: {
      name: 'L-Theanine',
      description: 'Amino acid that promotes GABA activity and alpha brain waves',
      category: 'Amino Acids',
      targetNeurotransmitter: 'GABA',
      targetHealthArea: 'Relaxation, Focus, Stress Relief',
      commonUses: 'Anxiety, stress, focus enhancement',
      dosageRange: '100-400mg daily',
      safetyLevel: 'Very Safe',
      mechanismOfAction: 'Enhances GABA activity, promotes alpha waves',
      evidenceLevel: 'Strong',
      interactions: 'Synergistic with caffeine'
    }
  },
  {
    id: 'gaba-supplement',
    labels: ['Supplement', 'Neurotransmitter', 'Calming'],
    properties: {
      name: 'GABA Supplement',
      description: 'Direct GABA supplementation for calming effects',
      category: 'Neurotransmitters',
      targetNeurotransmitter: 'GABA',
      targetHealthArea: 'Anxiety, Sleep, Relaxation',
      commonUses: 'Anxiety, insomnia, stress relief',
      dosageRange: '250-750mg daily',
      safetyLevel: 'Generally Safe',
      mechanismOfAction: 'May increase GABA levels peripherally',
      evidenceLevel: 'Moderate',
      interactions: 'Avoid with sedatives, alcohol'
    }
  },

  // Acetylcholine Support
  {
    id: 'alpha-gpc',
    labels: ['Supplement', 'Choline Source', 'Nootropic'],
    properties: {
      name: 'Alpha-GPC',
      description: 'Highly bioavailable choline source for acetylcholine synthesis',
      category: 'Nootropics',
      targetNeurotransmitter: 'Acetylcholine',
      targetHealthArea: 'Memory, Focus, Cognitive Function',
      commonUses: 'Memory enhancement, ADHD, cognitive decline',
      dosageRange: '300-600mg daily',
      safetyLevel: 'Very Safe',
      mechanismOfAction: 'Provides choline for acetylcholine synthesis',
      evidenceLevel: 'Strong',
      interactions: 'Generally well-tolerated'
    }
  },
  {
    id: 'cdp-choline',
    labels: ['Supplement', 'Choline Source', 'Nootropic'],
    properties: {
      name: 'CDP-Choline (Citicoline)',
      description: 'Choline source that also supports brain membrane health',
      category: 'Nootropics',
      targetNeurotransmitter: 'Acetylcholine',
      targetHealthArea: 'Memory, Focus, Brain Health',
      commonUses: 'Cognitive enhancement, stroke recovery, ADHD',
      dosageRange: '250-500mg daily',
      safetyLevel: 'Very Safe',
      mechanismOfAction: 'Provides choline and supports phospholipid synthesis',
      evidenceLevel: 'Strong',
      interactions: 'Generally well-tolerated'
    }
  }
];

// Neurotransmitter-specific relationships
export const neurotransmitterRelationships: GraphRelationship[] = [
  // 5-HTP to Serotonin
  {
    id: 'rel-5htp-serotonin',
    type: 'CONVERTS_TO',
    properties: { strength: 'high', evidenceLevel: 'strong', mechanism: 'direct conversion' },
    source: '5-htp',
    target: 'serotonin'
  },
  // Tryptophan to Serotonin
  {
    id: 'rel-tryptophan-serotonin',
    type: 'PRECURSOR_TO',
    properties: { strength: 'medium', evidenceLevel: 'strong', mechanism: 'via 5-HTP' },
    source: 'tryptophan',
    target: 'serotonin'
  },
  // L-Tyrosine to Dopamine
  {
    id: 'rel-tyrosine-dopamine',
    type: 'PRECURSOR_TO',
    properties: { strength: 'high', evidenceLevel: 'strong', mechanism: 'via L-DOPA' },
    source: 'l-tyrosine',
    target: 'dopamine'
  },
  // Mucuna to Dopamine
  {
    id: 'rel-mucuna-dopamine',
    type: 'CONVERTS_TO',
    properties: { strength: 'high', evidenceLevel: 'strong', mechanism: 'L-DOPA conversion' },
    source: 'mucuna-pruriens',
    target: 'dopamine'
  },
  // L-Theanine to GABA
  {
    id: 'rel-theanine-gaba',
    type: 'ENHANCES',
    properties: { strength: 'medium', evidenceLevel: 'strong', mechanism: 'GABA receptor modulation' },
    source: 'l-theanine',
    target: 'gaba'
  },
  // Alpha-GPC to Acetylcholine
  {
    id: 'rel-alphagpc-acetylcholine',
    type: 'PRECURSOR_TO',
    properties: { strength: 'high', evidenceLevel: 'strong', mechanism: 'choline donation' },
    source: 'alpha-gpc',
    target: 'acetylcholine'
  },
  // CDP-Choline to Acetylcholine
  {
    id: 'rel-cdpcholine-acetylcholine',
    type: 'PRECURSOR_TO',
    properties: { strength: 'high', evidenceLevel: 'strong', mechanism: 'choline donation' },
    source: 'cdp-choline',
    target: 'acetylcholine'
  }
];

export const neurotransmitterCategories = [
  'Serotonin Support',
  'Dopamine Support', 
  'GABA Enhancement',
  'Acetylcholine Boost',
  'Norepinephrine Support',
  'Multi-Neurotransmitter'
];

export const neurotransmitterInteractionTypes = [
  'CONVERTS_TO',
  'PRECURSOR_TO',
  'ENHANCES',
  'INHIBITS',
  'COMPETES_WITH',
  'SYNERGISTIC_WITH',
  'BALANCES'
];
