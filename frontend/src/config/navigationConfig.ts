import {
  Activity, Network, Sparkles, Pill, Search, BarChart3, Brain, Microscope,
  Heart, Zap, Shield, Target, Leaf, FlaskConical, Lightbulb, TrendingUp,
  BookOpen, Upload, Bot, Cpu, Workflow, Database, Layers
} from 'lucide-react';

export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: string;
  color?: string;
  description?: string;
  isNew?: boolean;
  isEnhanced?: boolean;
}

export interface NavSection {
  title: string;
  items: NavItem[];
  icon: React.ComponentType<any>;
  color: string;
  description: string;
  priority: number;
}

// Navigation configuration based on /docs/najlepsze documentation
export const navigationConfig: NavSection[] = [
  {
    title: 'Core Platform',
    icon: Activity,
    color: '#3b82f6',
    description: 'Essential platform features and data visualization',
    priority: 1,
    items: [
      { 
        name: 'Dashboard', 
        href: '/', 
        icon: Activity, 
        description: 'System overview with real-time metrics and health insights',
        isEnhanced: true
      },
      { 
        name: 'Knowledge Graph', 
        href: '/graph', 
        icon: Network, 
        description: 'Interactive supplement and health knowledge visualization',
        isEnhanced: true
      },
      { 
        name: 'Infinite Graph', 
        href: '/infinite-graph', 
        icon: Sparkles, 
        badge: 'Demo', 
        description: 'Advanced graph exploration with AI-powered insights',
        isNew: true
      },      { 
        name: 'Supplements Database', 
        href: '/supplements', 
        icon: Pill, 
        badge: 'Enhanced', 
        description: 'Comprehensive supplement database with interactions',
        isEnhanced: true
      },
      { 
        name: 'Global Search', 
        href: '/search', 
        icon: Search, 
        description: 'AI-powered search across all health data',
        isEnhanced: true
      },
      { 
        name: 'Analytics Hub', 
        href: '/analytics', 
        icon: BarChart3, 
        description: 'Advanced analytics and health trend analysis',
        isEnhanced: true
      }
    ]
  },
  {
    title: 'Neuroscience Intelligence',
    icon: Brain,
    color: '#8b5cf6',
    description: 'Advanced neurotransmitter analysis and optimization',
    priority: 2,
    items: [
      { 
        name: 'Neuroregulation Overview', 
        href: '/neuroregulation', 
        icon: Brain, 
        badge: 'Live Data', 
        color: '#8b5cf6', 
        description: 'Real-time neurotransmitter system monitoring',
        isEnhanced: true
      },
      { 
        name: 'Neuromediators Hub', 
        href: '/neuromediators', 
        icon: Microscope, 
        badge: 'Unified', 
        color: '#6366f1', 
        description: 'Comprehensive neurotransmitter interaction dashboard',
        isNew: true
      },
      { 
        name: 'Serotonin System', 
        href: '/serotonin', 
        icon: Heart, 
        badge: 'Mood', 
        color: '#3b82f6', 
        description: 'Mood, sleep, and happiness optimization',
        isEnhanced: true
      },
      { 
        name: 'Dopamine System', 
        href: '/dopamine', 
        icon: Zap, 
        badge: 'Energy', 
        color: '#10b981', 
        description: 'Motivation, focus, and energy enhancement',
        isEnhanced: true
      },